# Application Review Guide for Ops Users

## Overview

The Application Review system provides Ops users with a comprehensive interface to review, approve, and reject partner applications efficiently.

## Access

- **Who can access**: Users with `ops` or `super_admin` roles
- **URL**: `/admin/leads` (renamed to "Application Review")
- **Navigation**: Admin > Leads in the sidebar

## Current Users with Access

- **<EMAIL>** - Ops role ✅
- **<PERSON> (<EMAIL>)** - Super Admin role ✅

## Features

### 📊 Dashboard Overview

The main page shows:
- **Statistics Cards**: Total applications, pending, under review, approved, rejected
- **Tabbed View**: Filter by All, Pending, Under Review, Completed
- **Priority Indicators**: High (red), Medium (yellow), Low (gray)
- **Source Tracking**: Shows how applicants found us (direct, referral, utm, etc.)

### 🔍 Application Review Process

1. **Click "Review"** on any application to open the detailed review modal
2. **Three Tabs Available**:
   - **Application Details**: Full contact info, company details, metadata
   - **Activity Log**: Timeline of all actions taken on the application  
   - **Review & Actions**: Where you perform approval/rejection actions

### ⚡ Quick Actions

#### For Pending Applications:
- **Start Review**: Moves to "Under Review" status, sends notification to applicant
- **Quick Approve**: Immediately approves the application
- **Reject**: Opens rejection dialog with predefined reasons

#### For Under Review Applications:
- **Approve**: Completes the approval process
- **Reject**: Same rejection flow as pending

### 🔄 Automated Workflow

When you **approve** an application:
1. ✅ Status changed to "approved"
2. ✅ Email automatically added to whitelist  
3. ✅ Priority boosted to "High" for follow-up
4. ✅ Approval email sent to applicant with login instructions
5. ✅ Activity logged with your name and timestamp

When you **reject** an application:
1. ❌ Status changed to "rejected" 
2. ❌ Email deactivated in whitelist (but record kept)
3. ❌ Rejection email sent with reason and notes
4. ❌ Activity logged for audit trail

### 📧 Email Notifications

**To Applicants**:
- ✉️ **Approved**: Welcome email with next steps and portal login link
- ✉️ **Rejected**: Polite rejection with reason and support contact info  
- ✉️ **Under Review**: Confirmation that review has started

**To Ops Team**:
- 🔔 All ops users get notified when applications are submitted/approved/rejected
- ⚙️ Notification preferences can be managed per user

### 📋 Rejection Reasons

Predefined options for consistency:
- Incomplete Application  
- Not Qualified
- Duplicate Application
- Invalid Company Information
- Terms Not Accepted
- Other (with custom notes)

## Current Test Data

There are **4 sample applications** ready for testing:

1. **Francesco Oddo** (<EMAIL>) - Pending ⏳
2. **Sarah Johnson** - High Priority Pending 🔴  
3. **Mike Chen** - Currently Under Review 👀
4. **John Smith** - Pending ⏳

## How to Review Francesco's Application

1. Go to `/admin/leads`
2. Find "Francesco Oddo" in the applications list
3. Click "Review" to open the detailed modal
4. Review his application details:
   - Company: Francesco Consulting
   - Type: Consulting  
   - Status: Currently Pending
5. Go to "Review & Actions" tab
6. Click **"Quick Approve"** or **"Start Review"** then **"Approve"**
7. Add review notes if needed
8. Application will be automatically:
   - Added to email whitelist
   - Sent approval email
   - Logged as <NAME_EMAIL>

## Features Comparison: Before vs After

### ❌ Before (Old System)
- Basic Kanban board with hardcoded data
- No real application details
- Manual email whitelist management
- No activity tracking
- No automated notifications

### ✅ After (New System)  
- Real application data from database
- Complete applicant profiles with contact info
- Automated email whitelist sync on approval
- Full activity audit trail
- Automated email notifications to applicants and ops team
- Role-based access control
- Priority-based organization
- Advanced filtering and search

## Security & Permissions

- ✅ Only Ops and Super Admin roles can access
- ✅ All actions are logged with user attribution
- ✅ RLS (Row Level Security) enforced at database level
- ✅ Email whitelist automatically synced
- ✅ Audit trail for compliance

## Support & Next Steps

The system is production-ready and can handle real partner applications. Future enhancements could include:

- Integration with Resend/Postmark for actual email sending (currently logs to console)
- Advanced search and filtering options
- Bulk approval actions
- Application form integration
- Partner onboarding automation

**For questions or issues, contact the development team.**