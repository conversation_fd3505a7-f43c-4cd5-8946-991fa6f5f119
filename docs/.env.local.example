# IBC Partner Portal - Environment Configuration Template
# Copy this file to .env.local and fill in your actual values

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_
CLERK_SECRET_KEY=sk_test_
CLERK_WEBHOOK_SECRET=whsec_
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up

# Supabase Database
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJ
SUPABASE_SERVICE_ROLE_KEY=eyJ

# File Storage (UploadThing)
UPLOADTHING_SECRET=sk_live_
UPLOADTHING_APP_ID=your-app-id

# Email Service (Resend)
RESEND_API_KEY=re_
NEXT_PUBLIC_EMAIL_SUPPORT=<EMAIL>

# Security & Anti-abuse
TURNSTILE_SECRET_KEY=0x
CSRF_SECRET=generate-with-openssl-rand-hex-32
NEXT_PUBLIC_TURNSTILE_SITE_KEY=0x

# Monitoring & Observability
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/xxx
SENTRY_ORG=ibc
SENTRY_PROJECT=partner-portal
SENTRY_AUTH_TOKEN=your-auth-token

# Feature Flags
NEXT_PUBLIC_FEATURE_INVOICE_WIZARD=true
NEXT_PUBLIC_FEATURE_BULK_REASSIGNMENT=false
NEXT_PUBLIC_FEATURE_REFERRAL_METRICS=true
NEXT_PUBLIC_FEATURE_ADMIN_ANALYTICS=true

# Development Settings
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
DATABASE_SEED_ENABLED=true

# Rate Limiting (Optional - Redis)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Webhook Secrets
WEBHOOK_SECRET_SUPABASE=your-webhook-secret
WEBHOOK_SECRET_UPLOADTHING=your-uploadthing-webhook-secret