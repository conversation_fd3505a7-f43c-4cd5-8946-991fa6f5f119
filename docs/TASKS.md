# IBC Partners Dashboard - Implementation Tasks

## Status Overview

**Current State:** Solid foundation with complete UI scaffolding, database schema (15 migrations applied), and core infrastructure. All lib files exist and build successfully. Ready for data integration and business logic implementation.

**Critical Gap:** Some UI components still need Supabase integration (admin dashboard). Core partner features use real data.

**Security Note:** NO ID DOCUMENT or INVOICE STORAGE per security requirements. Invoice generation is client-side only with zero persistence.

**Legend:**
- ✅ Implemented (fully functional)
- ❌ Missing/Needs Implementation  
- 🔧 Partially Complete (needs finishing)
- 🔌 Ready for Integration (components exist, need data)

## PRD Analysis & Implementation Status

### Authentication & Access Control (PRD §2, §49-56)
- ✅ **Clerk email-only login** - OTP/magic links fully implemented
- ✅ **Access gating middleware** - Profile status checks in middleware.ts
- ✅ **RBAC system** - Complete role-based access control (lib/rbac.ts)
- ✅ **Email whitelist enforcement** - Clerk webhook with partners_email_whitelist table
- ✅ **Profile creation workflow** - Auto-create profiles from <PERSON> webhook
- ✅ **Status-based access control** - Pending/suspended/deleted users redirected
- ✅ **Enhanced access-pending page** - Context-aware status messages

### User Roles & Tier System (PRD §57-67, §68-92)
- ✅ All role types defined (Partner, Sales, Operations, Accounting, Super Admin)
- ✅ Tier system structure (Trusted 5%, Elite 7.5%, Diamond 10%)
- ✅ Commission rate calculations in lib/rbac.ts
- ❌ **Tier assignment by admins** - Admin UI for tier management
- ❌ **Tier benefits display** - Visual tier benefits and requirements

### Partner Onboarding & Registration (PRD §96-129)
- ✅ **Onboarding form UI** - Complete 4-step wizard with validation
- ✅ **Company type dropdown** - All PRD-specified types implemented
- ✅ **Field validation** - Real-time validation with Zod schemas
- ✅ **API integration** - POST /api/onboarding with profile creation
- ✅ **Billing address collection** - Complete address form with validation
- ✅ **T&C acceptance** - Terms tracking with version and timestamp
- ✅ **Profile status workflow** - Creates pending profiles awaiting approval
- ✅ **Referral slug generation** - Auto-creates unique partner referral links
- ❌ **ID document upload** - REMOVED: No ID document storage per security requirements
- ❌ **Internal POC assignment** - Dropdown of ops team members
- ❌ **Email whitelist sync** - Auto-sync approved emails from intake form

### Main Dashboard Homepage (PRD §136-156)
- ✅ **Partner name display** - "Gian's Dashboard" style personalization with real data
- ✅ **Quick stats overview** - Real metrics: total earnings, leads count, deals count
- ✅ **My Tier section** - Current tier badge with real tier data
- ✅ **My Earnings summary** - Real pending payout from confirmed deals
- ✅ **Withdraw CTA** - Links to withdrawal flow
- ✅ **Submit New Deal CTA** - Both referral link and direct submission
- ✅ **Referral link display** - Shows partner's unique referral slug
- ❌ **IBC Sales contact block** - Assigned sales rep details with contact options
- ❌ **Telegram group link access**

### Sidebar Navigation & Pages (PRD §157-310)

#### 4.1 Account Overview (PRD §161-178)
- 🔌 **Tier display** - Current tier with full benefits list
- ❌ **Assigned sales contact** - Name, role, contact methods
- 🔌 **Portfolio tracker table** - Referred leads with status filtering
- ❌ **Telegram group links** - "Project Name <> IBC Group" access
- ❌ **Date/company/status filters** - Advanced filtering options

#### 4.2 Deal Summary (PRD §180-212) 
- ✅ **Commissions sheet** - Real data: project, deal value, commission amounts, status
- ✅ **Deal filtering** - By status, date range, value, search functionality
- ✅ **Commission calculations** - Real commission rates based on tier
- ✅ **Deal status tracking** - Real statuses: pending, confirmed, paid, disputed
- ✅ **CSV export** - Client-side export with real deal data
- ✅ **Invoice generation** - Client-side PDF creation (zero persistence)
- ❌ **Token tracking details** - Total tokens, received, liquidated amounts

#### 4.3 My Earnings (PRD §214-238)
- ✅ **Earnings overview** - Real data: total withdrawn, pending payout, estimated future
- ✅ **Breakdown table** - Real deal details with commission values and payment dates
- ✅ **Tier progression tracker** - Shows current volume vs next tier requirements
- ✅ **Withdraw CTA** - Auto-generate invoice with deal breakdown
- ❌ **Cash bonus tracking** - For Elite/Diamond tiers ($500/$1000)
- ❌ **Accounting status integration** - In Review/Approved/Paid workflow

#### 4.4 Connect Us (Lead Submission) (PRD §240-251)
- ✅ **Lead submission form** - Real API integration with all required fields
- ✅ **Duplicate detection** - Warns partners about existing similar leads
- ✅ **Partner attribution tracking** - Links leads to correct partner via cookies
- ✅ **Analytics tracking** - Tracks form starts, completions, abandonment
- ✅ **Partner/Sales context** - Shows referring partner and assigned sales contact
- ❌ **Operations approval flow** - Admin review and notification system
- ❌ **Telegram group setup** - Auto-create groups for approved leads

#### 4.5 Account Settings (PRD §253-276)
- 🔌 **Editable profile fields** - Pre-filled from onboarding data
- ❌ **Approval workflow** - Pending review badges, admin notifications
- ❌ **Change versioning** - Changelog for profile updates
- ❌ **ID document re-upload** - REMOVED: No ID document storage per security requirements

#### 4.6 Tier Info (PRD §278-287)
- 🔌 **Unique referral link** - Partner-specific tracking URLs
- 🔌 **Tier badge & description** - Visual tier display
- ❌ **Referral metrics** - Projects referred, closed deals counters

#### 4.7 Terms & Conditions (PRD §289-291)
- ✅ **Read-only T&C page** - Version-controlled display
- 🔧 **Version tracking** - Needs accepted version storage

#### 4.8 Contact Us (PRD §293-300)
- ❌ **Assigned sales contact** - Name, Telegram/WhatsApp, email, booking links
- ❌ **Telegram group access** - Direct links to partner groups
- ❌ **Feedback form** - Stored messages in portal
- ❌ **FAQs/SOPs** - Notion embedding or document hosting

#### 4.9 Resources (PRD §302-309)
- 🔌 **Company materials** - Basic structure exists
- ❌ **Document library** - Blurbs, case studies, decks, submission guides

## Admin Dashboard & Workflows (PRD §311-353)

### 1. Sales Workflow (PRD §314-317)
- ✅ **Sales admin pages** - Basic structure exists
- ❌ **Data scoping** - Sales sees only assigned partners (RBAC exists)
- ❌ **Partner feedback view** - View questions/feedback from assigned partners
- ❌ **Lead status updates** - Update connection statuses for partners

### 2. Operations Workflow (PRD §319-331) 
- ✅ **Admin UI pages** - Partners, leads, deals interfaces exist
- ❌ **Onboarding approval** - Accept/reject applications with tier assignment
- ❌ **Sales assignment** - Manual salesperson selection for partners  
- ❌ **Profile auto-generation** - Create dashboard profiles upon approval
- ❌ **Unique Ambassador ID** - Auto-assign partner identification
- ❌ **Email login enablement** - Whitelist approved partner emails
- ✅ **CSV export** - Partners export implemented, needs expansion
- ❌ **Account settings approval** - Approve/reject profile changes

### 3. Accounting Workflow (PRD §333-340)
- ❌ **Withdrawal request management** - Edit, track, confirm requests
- ❌ **Deal editing** - View and edit all deals with financial data
- ❌ **Financial number updates** - Update deal values and commissions
- ✅ **CSV/Invoice generation** - Export functionality for accounting
- ❌ **Invoice approval** - REMOVED: Client-side only invoice generation (zero persistence)  
- ❌ **Payment status tracking** - Mark as complete/pending with TX details

### 4. Super Admin Tools (PRD §342-352)

#### Ned (COO) Specific Requirements:
- ❌ **Tier approval system** - Approve partner tier upgrades
- ❌ **Sales assignment** - Assign sales reps to unassigned partners  
- ❌ **Bulk reassignment** - Reassign all partners when sales staff changes
- ❌ **Deal approval** - Approve/reject deals from partners

#### Bob & Senior Members:
- ❌ **Full system access** - All features and configurations
- ✅ **Analytics dashboard** - Top-performing partners, revenue metrics
- ❌ **Operations oversight** - Monitor all admin functions

## Technical Infrastructure & User Flows (PRD §354-443)

### Database & API Integration (PRD §354-381)
- ✅ **Supabase PostgreSQL** - Complete schema with 15 migrations applied
- ✅ **Next.js + TypeScript** - App Router with Tailwind/shadcn components
- ✅ **Clerk Auth** - Email-only login implemented  
- ✅ **All lib files** - supabase/server.ts, validations, rbac all exist and functional
- ✅ **Core API routes** - Profile, deals, leads, earnings with role-based access
- ✅ **Data integration** - Dashboard, deals, earnings use real Supabase data
- ✅ **Commission calculations** - Real-time tier-based commission calculations
- ❌ **Admin API routes** - Admin dashboard endpoints need implementation  
- ✅ **Storage buckets** - UploadThing integration for company materials and support documents only
- ✅ **Referral tracking system** - Partner-unique link attribution with cookie tracking

### Form Validation & Edge Cases (PRD §383-406)
- ✅ **Comprehensive Zod schemas** - All validation schemas implemented
- ✅ **Input sanitization** - Character limits, symbol prevention 
- ❌ **"Other" dynamic fields** - Free-text input when "Other" selected
- ❌ **Duplicate deal detection** - Notify partners of matching deals
- ❌ **Soft-delete behavior** - Prevent accidental data loss
- ❌ **Export UTF-8 encoding** - Ensure CSV compatibility
- ❌ **Loading state protection** - Disable exports during data updates
- ❌ **Approval reminders** - Email flags for delayed admin actions

### User Flows Implementation (PRD §408-443)

#### Login Flow (PRD §410-415)
- ✅ **Email entry** - Clerk integration complete
- ✅ **OTP/Magic link** - Automatic email delivery  
- ❌ **Whitelist check** - Redirect if email not approved
- ✅ **Dashboard redirect** - Post-authentication routing

#### Submit Deal → Earn Flow (PRD §417-428)
- ✅ **Referral link sharing** - Partner-unique URLs with landing pages
- ✅ **Form tracking** - Cookie/UTM attribution on submissions
- ❌ **Deal qualification** - IBC team review and logging
- ❌ **Dashboard updates** - Auto-update partner's deal view
- ❌ **Earnings updates** - Real-time tier stats and commission tracking

#### Withdrawal Flow (PRD §430-443)
- ✅ **"Withdraw" button** - Links to withdrawal flow
- ✅ **Invoice generation** - Client-side PDF creation (zero persistence)
- ❌ **Admin workflow** - Review, approve, mark as paid
- ❌ **Status updates** - In Review/Approved/Paid with TX details
- ❌ **Email integration** - REMOVED: Client-side invoice generation only (no server storage/sending)

## Critical Missing Components Summary

### 🚨 **Highest Priority** (Blocks Core Functionality)
1. **✅ Replace mock data with real Supabase integration** - Dashboard, deals, earnings, lead submission complete
2. **✅ Email whitelist enforcement** - Clerk webhook with automatic profile creation
3. **✅ Profile creation workflow** - Auto-create Supabase profiles from Clerk users  
4. **✅ Status-based access control** - Middleware checks profile status with context-aware pages
5. **✅ Approval workflows** - Operations review system for onboarding, leads, profile changes  
6. **✅ Admin dashboard data integration** - Connect admin components to real data

### 📊 **High Priority** (Core Business Logic - PRD CRITICAL GAPS)
1. **Token Deal Tracking** (PRD §188-192)
   - Total tokens, received tokens, liquidation amounts
   - Token-to-USD conversion tracking
   - Commission splits for token vs fiat deals
2. **Sales Assignment System** (PRD §44, §164, §324)
   - Admin UI to assign sales reps to partners
   - Sales reps can only see their assigned partners
   - Bulk reassignment tool for staff changes
3. **Cash Bonus System** (PRD §82, §90)
   - Elite tier: $500 per token deal
   - Diamond tier: $1000 per token deal  
   - Deducted from liquidation amounts
4. **Withdrawal Request Management** (PRD §430-443, §334-340)
   - Admin review/approval workflow
   - Payment status tracking (In Review/Approved/Paid)
   - Transaction reference/TX hash tracking
5. **Lead Approval Workflow** (PRD §249-251)
   - Operations approval queue
   - Notification system for approvals/rejections
   - Telegram group auto-creation on approval

### 🔧 **Medium Priority** (Enhanced Features & PRD Requirements)
1. **✅ Analytics dashboard** - Top partners, revenue metrics, performance tracking
2. **Communication Preferences** (PRD §103)
   - Multi-select preferences (WhatsApp, Email, Telegram)
   - WhatsApp number collection and storage
3. **Internal POC Assignment** (PRD §125)
   - Dropdown of operations team members
   - Track internal person managing each partner
4. **Profile Change Approval** (PRD §266-276)
   - Version control for profile updates
   - Admin approval workflow for changes
   - Changelog tracking system
5. **Telegram Integration** (PRD §155, §173, §296)
   - Direct links to partner-specific groups
   - Group creation notifications
   - Telegram handle storage and display
6. **Advanced Filtering** - Date ranges, multi-field searches across tables
7. **Bulk Operations** - Mass reassignment, batch approvals

### 🎨 **UI/UX Enhancements** (PRD Partner Experience)
1. **Account Overview Page** (PRD §161-178)
   - Portfolio tracker with lead statuses (warm/cold/won/lost)
   - Advanced filtering by date/company/status
   - Bulk reassignment tool for admins
2. **Tier Benefits Display** (PRD §282-287)
   - Visual comparison of all tier benefits
   - Interactive progression tracker
   - Requirements display for next tier
3. **Contact/Support Page** (PRD §293-300)
   - Calendly/HubSpot booking links
   - FAQ/SOP documentation (Notion embed)
   - Feedback form with message storage
4. **Resources Page** (PRD §302-309)
   - Company materials library
   - Case studies and decks
   - Submission guides
5. **"Other" Field Handling** (PRD §388)
   - Dynamic text input when "Other" selected
   - Store and display custom entries

### 📈 **Success Metrics Tracking** (PRD §459-467)
- ❌ **Partner onboarding rate** - 100% approved partners using dashboard
- ❌ **Manual task reduction** - 80% decrease in manual processes  
- ❌ **Deal visibility** - 100% real-time deal status for partners
- ❌ **Tier upgrades** - Track 30% of Trusted Partners upgrading
- ❌ **CSV export usage** - Admin data export for CRM/email lists
- ❌ **Lead conversion** - 65% approved leads to deals tracking

## Next Implementation Phase (Based on PRD Gap Analysis)

**Phase 1: Critical Business Logic** (3-4 days) - HIGHEST PRIORITY
1. **Token Deal Tracking System**
   - Add token fields to deals table (total, received, liquidated)
   - Build liquidation tracking UI
   - Implement token-to-USD conversion calculations
2. **Sales Assignment System**  
   - Add sales_rep_id to partners table
   - Build admin UI for assignment
   - Implement role-based data scoping
   - Create bulk reassignment tool
3. **Withdrawal Request Management**
   - Create withdrawal_requests table
   - Build approval workflow UI
   - Add payment status tracking
   - Implement TX hash/reference storage

**Phase 2: Commission & Bonus System** (2-3 days)
1. **Cash Bonus Tracking**
   - Add bonus calculation logic ($500 Elite, $1000 Diamond)
   - Deduct from liquidation amounts
   - Display in earnings overview
2. **Lead Approval Workflow**
   - Build operations approval queue
   - Add notification system
   - Implement Telegram group creation trigger
3. **Profile Change Versioning**
   - Add changelog table
   - Build approval workflow
   - Implement version tracking

**Phase 3: UI/UX Enhancements** (2-3 days)
1. **Complete Missing Pages**
   - Account Overview with portfolio tracker
   - Contact/Support page with booking links
   - Resources page with document library
2. **Tier Benefits Display**
   - Visual benefits comparison
   - Interactive progression tracker
3. **Communication Preferences**
   - Multi-select options
   - WhatsApp number storage
4. **"Other" Field Handling**
   - Dynamic text inputs
   - Custom entry storage
