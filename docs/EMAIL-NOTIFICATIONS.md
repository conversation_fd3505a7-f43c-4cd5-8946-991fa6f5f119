# Email Notification System

The IBC Partners Portal includes a comprehensive email notification system built with Resend for reliable transactional email delivery.

## Overview

The system sends automated notifications for:
- Partner application approvals/rejections
- Deal status updates (confirmed, rejected, paid)
- Manual notifications from admin interface

## Architecture

### Core Components

1. **Email Service** (`lib/email.ts`)
   - Resend integration
   - Email template system
   - Delivery error handling

2. **API Routes**
   - `/api/notifications/partner-status` - Manual partner notifications
   - `/api/notifications/deal-status` - Manual deal notifications
   - `/api/webhooks/supabase` - Automatic notifications via webhooks

3. **Admin Interface** (`app/components/admin/notification-center.tsx`)
   - Manual notification sending
   - Delivery status tracking
   - Recent notifications log

## Email Templates

### Partner Application Approved
- **Trigger**: Partner status changes from `pending` to `active`
- **Content**: Welcome message, tier assignment, login instructions
- **CTA**: Access Partner Dashboard

### Partner Application Rejected
- **Trigger**: Partner status changes from `pending` to `rejected`
- **Content**: Polite rejection with optional reason
- **CTA**: Contact support for feedback

### Deal Status Update
- **Trigger**: Deal status changes to `confirmed`, `rejected`, or `paid`
- **Content**: Deal details, commission amount, status explanation
- **CTA**: View Deal Details

## Configuration

### Environment Variables
```env
# Required
RESEND_API_KEY=re_...

# Optional for webhooks
SUPABASE_WEBHOOK_SECRET=your-secret-key

# App URL for links
NEXT_PUBLIC_APP_URL=https://partners.ibcgroup.com
```

### Resend Setup
1. Create account at [resend.com](https://resend.com)
2. Add and verify sending domain
3. Generate API key
4. Configure SPF/DKIM records

## Usage

### Automatic Notifications
Notifications are automatically sent when:
- Admin approves/rejects partner applications
- Deal status changes through admin interface
- Database triggers fire (via webhooks)

### Manual Notifications
Admins can send notifications via:
1. Admin Dashboard → Notifications
2. Select notification type
3. Choose recipient and status
4. Add custom message (optional)
5. Send notification

### Testing
Use the testing script to validate email delivery:

```bash
# Test approval email
bun run scripts/test-emails.ts --type approval --email <EMAIL>

# Test rejection email  
bun run scripts/test-emails.ts --type rejection --email <EMAIL>

# Test deal update email
bun run scripts/test-emails.ts --type deal-update --email <EMAIL>
```

## Security Features

- **Email Validation**: All recipient emails validated before sending
- **Rate Limiting**: Prevents spam/abuse via API rate limits
- **Admin Only**: Notification endpoints require admin authentication
- **Content Sanitization**: All dynamic content is properly escaped
- **Webhook Verification**: Supabase webhook signatures verified

## Monitoring

### Delivery Tracking
- Resend provides delivery confirmations
- Failed deliveries logged with error details
- Recent notifications displayed in admin interface

### Error Handling
- Graceful degradation if email service unavailable
- Retry logic for temporary failures
- Comprehensive error logging

## Database Schema

### Notification Log (Future Enhancement)
Consider adding a notifications table to track:
```sql
CREATE TABLE notification_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  recipient_email text NOT NULL,
  notification_type text NOT NULL,
  subject text NOT NULL,
  sent_at timestamptz DEFAULT now(),
  status text DEFAULT 'sent',
  error_message text,
  created_at timestamptz DEFAULT now()
);
```

## Email Templates Customization

Templates are defined in `lib/email.ts` with:
- Professional HTML design
- Responsive layout
- Brand consistency
- Clear call-to-action buttons
- Status-specific styling

### Template Structure
- Header with IBC Group branding
- Main content with relevant information
- Call-to-action button
- Support contact information
- Professional footer

## API Endpoints

### POST /api/notifications/partner-status
Send partner status change notification.

**Body:**
```json
{
  "partnerId": "uuid",
  "status": "active" | "rejected",
  "reason": "optional rejection reason",
  "tier": "trusted" | "elite" | "diamond"
}
```

### POST /api/notifications/deal-status
Send deal status change notification.

**Body:**
```json
{
  "dealId": "uuid", 
  "status": "pending" | "confirmed" | "rejected" | "paid",
  "statusMessage": "optional custom message"
}
```

### POST /api/webhooks/supabase
Webhook endpoint for automatic notifications (internal use).

## Best Practices

1. **Email Deliverability**
   - Use verified sending domain
   - Maintain good sender reputation
   - Include unsubscribe links (future enhancement)
   - Monitor bounce/complaint rates

2. **Content Guidelines**
   - Clear, professional language
   - Action-oriented subject lines
   - Mobile-responsive design
   - Include relevant context

3. **Privacy & Compliance**
   - Only send necessary notifications
   - Include privacy policy links
   - Respect opt-out preferences
   - Secure handling of personal data

## Troubleshooting

### Common Issues

**Emails Not Sending**
- Check RESEND_API_KEY configuration
- Verify domain verification in Resend
- Check API rate limits

**Emails Going to Spam**
- Verify SPF/DKIM records
- Check sender reputation
- Review email content for spam triggers

**Webhook Not Triggering**
- Verify SUPABASE_WEBHOOK_SECRET
- Check webhook URL configuration
- Review webhook logs in Supabase

### Debug Commands
```bash
# Test email configuration
bun run scripts/test-emails.ts --type approval --email <EMAIL>

# Check recent email logs
curl -X GET "https://api.resend.com/emails" \
  -H "Authorization: Bearer $RESEND_API_KEY"
```

## Future Enhancements

1. **Email Preferences**
   - Partner notification preferences
   - Frequency controls
   - Unsubscribe management

2. **Advanced Templates**
   - Dynamic content blocks
   - Personalization tokens
   - A/B testing capabilities

3. **Analytics**
   - Open/click tracking
   - Delivery analytics
   - Engagement metrics

4. **Additional Notifications**
   - Tier progression alerts
   - Monthly performance summaries
   - System maintenance notices