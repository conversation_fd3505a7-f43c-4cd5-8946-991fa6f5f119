# Product Requirements Document (PRD) for IBC Partnership Program

## I. Overview

**PROJECT TITLE:** IBC Group / Ventures — Partnership Program Dashboard

**OWNER:** IBC Ventures LTD - Partnership and Operations Team

**PREPARED BY:** <PERSON><PERSON> - Head of Partnerships and <PERSON> (Head of Operations)

**DATE:** July 30, 2025 - August 8, 2025

**PRD STATUS:**
- ☑ WORK IN PROGRESS
- ☑ SUBMITTED
- ☐ APPROVED

### Executive Summary

The IBC Partnership Program Dashboard is a secure, self-service portal for IBC’s referral partners, deal introducers, and affiliate network. It enables users to submit and track already referred deals, manage their leads, view their commission history, access resources, request connections, and upgrade to higher tiers. This tool empowers partners to actively contribute to IBC’s deal pipeline while enjoying transparency and automation in their journey. Furthermore, this also provides the IBC Operations, Sales, Accounting, and Partnership teams with the tools to manage approvals, assignments, payments, and communications.

*(Visual: Header image with "International Blockchain Consulting" logo in green blocks.)*

## Objectives & Goals

| Objective | KPIs |
| --- | --- |
| Streamline partner onboarding and qualification | Reduce manual onboarding, manual agreements, and other manual tasks by 80% |
| Provide a secure and transparent dashboard for partners to view and manage their activity. | 100% of partners can see the real-time status of all their deals |
| Enable frictionless tracking of referrals | Referral link system with attribution accuracy >95% |
| Motivate partners to close more deals through tiering | At least 30% of Trusted Partners upgrade within 3 months |
| Automate partner earnings withdrawal workflow | 90% reduction in manual payout tracking for ops |
| Centralize resources and support | 90% of partner support requests resolved via dashboard resources |
| Give internal teams role-based access to perform approvals, assignments, deal tracking, and payments. | Streamline internal processes within the dashboard and improve efficiency by 90% |

## II. Dashboard Details

### User Personas

| User / Persona | Description | Needs |
| --- | --- | --- |
| Affiliate Partners | New introducer approved via form. (Trusted Partner Tier) | Referral tools, basic performance stats, and onboarding |
| Power Closers | Active dealmaker working toward Elite/Diamond status | Deal breakdowns, tier progression, cash bonus tracker, and access to exclusive tier benefits. |
| Sales | IBC Sales team leading the partnerships | View their assigned partners and their leads, update connection statuses, communicate with partners + (must have individual views with no access to other sales representatives’ pipelines) |
| Operations | Internal IBC Operations team ensuring the proper flow of the system | Approve/reject onboarding applications, assign tiers, assign sales people to each partner, approve/reject leads, reassign portfolios, manage tier benefits, and project lists |
| Accounting | IBC Accounting team in charge of referral payments, agreements, invoices, and records | View deals, update financial numbers, approve/reject invoices, mark payments complete. |
| Super Admin | Department Heads (Ned, Bob, Mona, Franchesco, etc.) | Full access to all functions, data, and configuration. |

### Authentication / Access Control

- Email-only Login
  - Registration
  - Magic Link or OTP (via Firebase or Supabase Auth)
  - Login is only enabled for pre-approved emails (form-synced list or whitelist)
  - 2FA for users (Optional)

### User Roles:

- Partner (Default) - Access will be dependent on the tier. Higher tier means more access to additional features
  - Trusted Partner Tier 
  - Elite Partner Tier
  - Diamond Partner Tier
- Sales team - Can only view their own partners or partners assigned to them. No access to the full list 
- Operations team - Access to onboarding forms and requests, approval and assignment of partner tiers, approval of partner-referred projects, management of tier benefits, and full access to project and partners list.
- Accounting team - View deals, edit and approve invoices, update financial records, and mark payments complete
- Super Admin (Bob / Ned / Other Senior Members Only) - Full access

## III. Tier System Progression

### Trusted Partner

- Commission: 5% (Token / Hybrid / Fiat)
- Access: Deal submissions, TG group, dashboard
- Perks / Bonus: None yet
- Discord role: Trusted Partner
- Upgrade Rule: 3 closed deals or $150K+ generated/liquidated revenue

### Elite Partner

- Commission: 7.5% (Token / Hybrid / Fiat)
- Additional Access: Access to selected incubation deals, VIP invites to IBC events, Private Discord Chat, Personal IBC Project Manager, and a Direct line to Ned (Chief of Sales at IBC)
- Perks / Bonus: $500 cash per token deal (deducted from liquidation) and private calls
- Discord role: Elite Partner
- Upgrade Rule: Possible upgrade after 10 closed deals or $1M+ generated/liquidated revenue

### Diamond Partner

- Commission: 10% (Token / Hybrid / Fiat)
- Additional Access: Website and social media recognition, Custom structuring, premium incubation access, VVIP invites to IBC events (with specially arranged setup), VVIP invites to IBC private calls, and NDA access to investor alpha, token launches, and more.
- Perks / Bonus: $1000 direct cash per token closed deal (discounted from liquidation)
- Discord role: Diamond Partner
- Upgrade Rule: Invite-only from Bob (COO at IBC) and Ned

## IV. Feature Set (Modular Breakdown)

### 1. Partner Onboarding and Registration

- Referral Links: Unique for each salesperson + website referral link
- Fields to be collected: 
  - Personal Info: 
    - Full Name of Partner (free text) (required)
    - Identification (Passport or Government ID - for the agreement)
    - Preferred Communication Method (multi-select: WhatsApp, Email, Telegram)
    - Email
    - Telegram Handle, WhatsApp number of Partner 
    - Company Name (free text)
    - X account of Company/Partner (link)
    - Company Type (dropdown)
      - Venture Capital (VC)
      - Central Exchange (CEX)
      - Decentralized Exchange (DEX)
      - Launchpad
      - Marketing Agency
      - Incubator/Accelerator
      - Market Making (MM)
      - Development 
      - Technology
      - Research
      - Data and aggregation platform
      - External BD
      - Deal Flow Individual
      - Angel Investor
      - Others (Specify) (free text)
  - Partner Role within the company (dropdown or text: CEO, CTO, CMO, BD, Founder, Co-Founder, etc.)
  - Internal POC: Dropdown of internal ops list.
  - Payment Details: Billing Address (required), Preferred Payment Method (Bank Transfer or USDT) — bank/wallet details collected later.
  - (Important) Terms & Conditions: Must tick checkbox before submission; store version number accepted.
  - Input fields should enforce max character limits (e.g., 50 characters for company name) and sanitize symbols to prevent database injection or formatting issues in exports

### 2. Login and Authentication

- Email field + OTP or magic link
- Error handling for unapproved accounts
- Admin-controlled whitelist or auto-sync from the intake form

### 3. Main Dashboard Homepage 

*(Visual: Sample dashboard layout showing sections like "Gian's Dashboard", quick overview table with projects, deal values, commissions; "My Tier" with progress bar; "My Earnings" summary; CTAs for "Submit New Deal", "Withdraw", "View History"; sidebar navigation; referral metrics; IBC information with contact details.)*

- Heading with Partner’s Name (Gian’s Dashboard)
- Quick stats 
  - Quick overview of recent deals and basic referral table (can be expanded within the main dashboard, or the full detailed table will be available in the account overview)
  - My Tier - current tier level (with quick list of benefits) and tier progression tracker 
  - My Earnings - main earnings summary (Total $ Generated by partner, Total $ Received, and Total $ pending)
- Call to Action (CTA): Withdraw 
- CTA: Submit New Deal 
  - via the “My Referral Link” which will be linked to IBC forms for approval or rejection (the partner can have the project fill in the forms with the needed details of the project and deal). 
  - CJN Onboarding Form  - current project form, so we need to have a unique link for each partner to track where it came from.
  - With a Quick summary of Referral Metrics (How many projects referred, how many are closed)
  - via the “Submit New Deal” button - The partner can opt to fill out the forms for the project if they have the necessary details. This will link to the “Connect Us” portion of the dashboard (See 4.4).
- can also view deal history, redirect to deal summary page (which is also in the sidebar navigation)
- IBC Information - this will be where the partner can find the contact of the assigned sales for questions and other communication needs
  - Sales representative's Name and Job title
  - Email, WhatsApp, and Telegram contact options should be available
  - Easy access to the link for the official Telegram group between the partner and IBC

### 4. Sidebar Navigation

*(Visual: Sidebar with items: Main Dashboard, Account Overview, Deal Summary, My Earnings, Connect Us, Account Settings, Tier Info, Terms & Conditions, Contact Us, Resources; footer with social icons for X, Discord, LinkedIn.)*

#### 4.1 Account Overview

- Display the assigned Tier with the full list of benefits for that tier
- Assigned Sales Person contact - this is for the partner to ping our sales for any pending replies on our side, or to ping the sales for any groups revived by the partner
- Portfolio Tracker: Table of referred leads - should be filterable by the partner (project name, date, status - warm, cold, won, lost, etc.)
  - Project Name
  - Date added
  - Status - this will help the partner keep track and chase any referrals that are getting cold
    - warm
    - cold
    - won
    - lost
  - Link to group ( Project Name <> IBC Group )
- Filters 
  - Date 
  - Company Name
  - Status 
- Admin note: Bulk reassignment tool for admin use when sales staff change.

#### 4.2 Deal Summary

- Display assigned Tier, full list of benefits, and current salesperson contact
- Table of commissions sheet 
  - Project Name
  - Deal Type (Upfront Compensation, Marketing, Investment, etc.)
  - Closed By (IBC sales rep)
  - Partner Name (Who referred us to the Project)
  - Amount (with details of tokens/fiat)
    - Total # of Tokens from the deal
    - Received # of Tokens from the deal
    - Liquidated # of Tokens (how much we have liquidated so far)
    - Liquidation Value in USD
  - Commission % and value
    - Due Token in USD (to be paid to the referrer)
    - Due Fiat in USD (to be paid to the referrer)
    - The remaining (Pending) commission to be received by the referrer
  - Status 
    - In Progress
    - Closed
    - Lost
    - Paid
- Deal-level details + aggregated totals
- Date added and last updated
- Filters 
  - Date 
  - Range (Highest Value to lowest and vice versa, Highest commission to lowest and vice versa)
  - Type
  - Status
- Export as CSV (can be optional)
- Export invoice (automated) 

*(Visual: Commissions Sheet/Table example with columns: Project, Deal Type, Closed By, Deal Value, Liquidation Value, Commission % (Diamond Tier), Commissions Value, Status. Rows for sample projects like "Francesco AI", "XYZ", "Bob Token" with values and statuses like Paid, In Progress, Pending.)*

#### 4.3 My Earnings

- Earnings overview
  - Estimated Earnings from all referred deals (Sum of Commissions Column)
  - Withdrawn (Total Earned - Sum of all Paid commissions)
  - Withdrawal history (with reference and transaction details)
  - Pending Earnings (Total Pending to be received - Sum of all due payments marked as “Pending”)
- Breakdown Table
  - Deal Name, Type, Amount, % Commission, Commission value in USD, and Status (Pending, Paid, Lost/Rejected).
- For applicable tiers:
  - Upfront compensation
  - Cash Bonus
- “Withdraw” CTA:
  - Auto-generate a draft invoice with a deal breakdown
    - Partner will fill up the necessary details and store the invoice in local storage (for security)
    - Editable payment method fields (bank/wallet).
  - Invoice emailed <NAME_EMAIL>
  - Method: USDT Wallet/Bank Transfer
  - Request form with Amount, Wallet Address, Notes, etc.
- Status to reflect in the Admin Panel
- Admin Note: Accounting can edit values and include statuses. Also, approve or reject invoices 
- Accounting stages: In Review / Approved / Paid.
- Payment Details (Once Paid)
  - TX ID (if via crypto)
  - Transaction/Reference number (if via alternative payment methods)

#### 4.4 Connect Us (Lead Submission)

- Fields to fill:
  - Company Name / Project Name
  - Website
  - X / Twitter Link
  - Name of POC from the Project
  - Position of POC
  - Notes / Comments (Optional)
- Admin Note: Operations will approve or reject 
  - If approved, Sales will notify the partner to set up a Telegram group then the lead will be added to the Partner’s dashboard 
  - If rejected, Sales will notify the partner with the appropriate reason

#### 4.5 Account Settings

- Partners can edit submitted info based on onboarding forms
- Fields Displayed & Editable (Pre-filled with their onboarding data):
  - Full Name
  - Email 
  - Telegram Handle
  - X (Twitter) Profile Link
  - Company Name
  - Company Type
  - Role / Position
  - Identification Document (View or Re-upload)
  - Billing Address
- Edit Process Logic:
  1. Partner updates one or more editable fields in the Account Settings section.
  2. A “Submit Changes” button appears when edits are detected.
  3. Upon submission:
     - The partner sees a confirmation message: “Your update has been submitted and is pending approval by IBC Ops. You will be notified once approved.”
     - Fields show a Pending Review badge or lock until approved.
     - Admins receive a flag in the backend to approve, deny, or request clarification.
  4. Once approved, the new data:
     - Updates in the dashboard
     - Is reflected in exports
     - Is versioned/stored with a changelog

#### 4.6 Tier Info 

- Partner’s Unique Referral link
- Tier badge + description
- Benefits displayed visually
- Tier progression tracker (e.g., 2/3 deals closed, $110K/$150K)
- Referral Metrics:
  - Projects Referred
  - Closed Deals
- In this section, the partner can view the Tier blurb (as seen on III. Tier System Progression) - The partner should be able to see which tiers are available and what benefits come with it (for example, trusted tier partner will be able to see benefits of a diamond partner and requirements to be considered for the upgrade).

#### 4.7 Terms & Conditions

- Read-only, version-controlled

#### 4.8 Contact Us

- Assigned IBC Sales with Name, Telegram/WhatsApp contact, Email, and other contact info
- Telegram group link access
- Calendly or HubSpot booking link for additional support (Must be the booking link of the assigned salesperson)
- Feedback form (Forms or comment box)
  - Stored messages in the portal
- FAQs/SOPs (hosted in Notion or embedded doc)

#### 4.9 Resources

- Company Materials
- Blurbs
- Case Studies
- Decks
- Company links (Optional - already at the bottom of the side navigation panel)
- Submission guides - How to use the dashboard

## V. Admin Dashboard (Backend for Admin / Internal Use)

### 1. Sales Workflow

- Sales and Partners table/view
  - i. Each salesperson can view their own partner's table, but cannot view others’ records
  - ii. Can view feedback and questions from their assigned partners

### 2. Operational Workflow

- a. Review & Approval
  - i. Ops can accept or reject the application
  - ii. Assign Tier (configurable in admin panel)
  - iii. Assign Salesperson (manual selection)
- b. Upon Approval
  - i. Dashboard profile auto-generated.
  - ii. Unique Ambassador ID assigned.
  - iii. Login is enabled via email.
- c. View full partner lists and all records 
  - i. Export full partner list as CSV
- d. Approve account settings updates/changes

### 3. Accounting Workflow

- a. Edit, track, and confirm withdrawal requests
- b. View and edit all deals (including the referred leads table of partners)
- c. Update financial numbers
- d. Generate a payout invoice or CSVs 
- e. Approve or reject invoices and access all invoice data
- f. Mark payment status as complete, pending, or other

### 4. Super Admin

- a. Ned 
  - i. Needs to be able to approve tiers 
  - ii. Assign sales for partners without an assigned sales representative
  - iii. Re-assign sales representatives to each partner - when a sales representative resigns, Ned should be able to bulk reassign all leads to another sales representative (Bulk reassignment)
  - iv. Approve or reject deals from partners
- b. Bob and others
  - i. Full access to all features
  - ii. Analytics - can see top-performing partners and view revenue generated
  - iii. Oversee all operations and admin features

## VI. Technical Architecture

- Partners Registration - this table should be queryable and exportable by admins for CRM use and partner management.

| Field | Type |
| --- | --- |
| id | (UUID) |
| email | (string) |
| name | (string) |
| company_name | (string) |
| company_type | (enum: VC, CEX, DEX, Launchpad, Other) |
| role | (string) |
| tier_level | (enum: Trusted, Elite, Diamond) |
| unique referral code | (identifier) |
| created_at | (timestamp) |

- Other Technical
  - Note: Suggestion by Gian, to be updated by the Dev or the person in charge of creating the dashboard

| Layer | Tech Stack |
| --- | --- |
| Frontend | React.js / Next.js (Tailwind UI or ShadCN) |
| Backend | Firebase Functions or Node.js with Supabase |
| Database | Supabase (PostgreSQL) |
| Auth | Firebase Auth or Supabase Auth |
| Storage | Firebase Storage or Supabase bucket (for files/materials) |
| Admin CMS | Supabase UI / Retool / Custom panel |
| Referral Tracking | UTM / Custom Link Tracker (shortened + DB attribution) |

## VII. Edge Cases & Handling

| Layer | Tech Stack |
| --- | --- |
| Partner skips company/role/type form on first login | Prevent dashboard access until form is completed (soft block with inline reminder) |
| Partner selects “Other” for company type | Show a free-text input field for custom entry |
| User enters invalid characters (e.g. emojis, symbols) in role/company fields | Validate input: alphanumeric + limited special characters only |
| Multiple partners list the same company | Allow duplicates, but flag for admin view (e.g. users with same org) |
| Partner wants to change company, role, or type post-registration | Allow editing in “Account Settings” with autosave confirmation |
| User logs in with an unapproved email | Show message: "Access pending. Please contact your IBC PM or check your registration email." |
| Referral link used but form not completed | Track click; don't credit the deal unless submission occurs |
| Withdrawal request without min balance | Disable button, tooltip explaining threshold |
| Tier upgrade delay despite meeting conditions | Automatic flag in the admin panel for manual review |
| Partner submits a duplicate deal | Notify partner; show matching existing deal in the system |
| Admin exports partner list with missing fields | Fill in “Not Provided” for blank entries |
| Admin filters by company type but partners selected “Other” | Display custom entries dynamically in filter dropdown or tag |
| Admin tries to export data while list is updating / loading | Disable export button and show loading spinner until data is fully loaded |
| Special characters in CSV export | Ensure UTF-8 encoding on all export files for compatibility |
| Limit export functionality to Admin/Super |  |
| Admin exports contain sensitive info | Admin roles only |
| Admin deletes a partner entry accidentally | Add soft-delete or confirmation |
| In account settings, Partner submits incomplete update (e.g., missing company type) | Block submission; inline validation required for all required fields |
| Partner resubmits a previously rejected change | Allow re-submission with previous values auto-filled |
| Admin delays approval > 48 hours | Optional reminder email/flag in admin panel |

## VIII. User Flows

### Login

1. Partner enters email
2. OTP/magic link sent
3. Logged in if whitelisted
4. Redirect to dashboard

### Submit Deal → Earn

1. Partner shares referral link or submits a new deal
2. Partner will fill out the form (with tracking cookie or UTM)
   a. Website
   b. Deal details
   c. Group link (Telegram or WhatsApp)
   d. IBC form (so the board can review it)
     i. CJN Onboarding Form
3. The IBC team qualifies and logs the deal
4. Dashboard auto-updates the deal in the partner’s view
5. Once the deal closes, earnings and tier stats update

### Request Withdrawal

Note: Withdrawal information (invoice) goes to the accounting team to execute and is not automatically paid within the dashboard.

1. Partner clicks "Withdraw."
2. Auto-generated invoice downloaded and stored locally for the user
3. Partner fills up the necessary details
4. Choose a method and input details
   a. billing address 
   b. account details (bank)
   c. wallet address (for crypto)
5. Invoice is sent to admin.ibcgroup.io
6. Admin reviews and marks as paid
7. Status changes to “Paid” with optional Tx hash or reference

## IX. Development Timeline

Note: Conservative estimates by Gian, to be updated by the Dev or the person in charge of creating the dashboard

| Phase | Deliverables | Timeline |
| --- | --- | --- |
| Discovery | Finalized features, wireframes | Aug 2, 2025 |
| Design | Hi-fi mockups, UI kit | Aug 10, 2025 |
| MVP Development | Login, Referral Tab, Earnings | Aug 11, 2025 |
| Admin Panel + Deal Logic | CMS, deal & payout logic | Aug 12, 2025 |
| QA + UAT | Testing, Bug Fixes | Aug 15, 2025 |
| Beta Launch | Soft Release with partners and messaging partners to onboard | Aug 22, 2025 |
| Public Rollout | Full Partner Base Onboarded | Sept 1, 2025 |

## X. Success Metrics

- 100% of approved partners onboarded to the dashboard
- 90% partner satisfaction in the feedback survey
- at least 30% monthly growth in referrals from active partners
- 75% decrease in manual update workload for the BD/Partnership team (Gian) + accounting team
- at least 20 Tier upgrades within 2 months
- Admins must be able to export partner data in CSV format for use in investor CRM, email lists, and payout processing.
- at least a 65% conversion rate of approved leads to deals