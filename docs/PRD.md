# IBC Partnership Program — Technical PRD

**Product**: IBC Partner Portal (Web)

**Owners**: Partnerships, Operations, Sales, Accounting, Engineering

**Primary Sources**: Business PRD (personas, tiers, features, success metrics)

**Tech Stack (committed)**: Next.js (App Router), TypeScript, Tailwind + shadcn/ui, Supabase (Postgres + RLS, Edge Functions), Clerk (Auth & Org), UploadThing (to S3-compatible storage), Dub.co (recommended) or alternatives for referral links, TanStack Form + Zod (validation), Resend/Postmark (email), Sentry (monitoring), Vercel (hosting + cron), Cloudflare Turnstile (anti-abuse)

---

## 1) Goals & Non-Goals

**Goals**

- Ship a secure, self-serve portal for partners to submit/track referrals, view commissions, and request payouts.
- Provide internal backoffice for Sales/Ops/Accounting to qualify leads, manage tiers, confirm deals & payments.
- Accurate referral attribution (>95%) with partner-unique links.
- Tier progression automation & visibility.

**Non-Goals**

- Automated payouts (execution remains manual, but tracked in app).
- CRM replacement. We export/ingest with CSV or Webhooks.

**Key Constraints**

- Email whitelist gating; only pre-approved users can log in.
- Invoice artifacts are generated and **kept client-side**; only metadata is stored server-side unless Accounting uploads proof-of-payment.

---

## 2) User Types & Roles

- **Partner** (Trusted / Elite / Diamond)
- **Sales** (can only see partners/leads assigned to them)
- **Operations (Ops)**
- **Accounting**
- **Super Admin** (department heads)

Role/permission matrix is enforced in both UI and DB (RLS). See §7 and §8.

---

## 3) System Architecture (High-Level)

- **Next.js (App Router)** SSR/ISR for dashboards, Route Handlers for APIs, Server Actions for mutations where safe.
- **Clerk** for authentication, MFA, and allow-list. Map Clerk user → `profile.user_id` (UUID) through JWT claims.
- **Supabase** hosts Postgres + Row Level Security (RLS), SQL migrations via `supabase/migrations`.
- **Supabase Edge Functions** for webhooks (Dub, UploadThing callbacks), scheduled jobs (Vercel Cron can call them).
- **UploadThing → S3** for KYC IDs and attachments. Links are signed, time-limited. Virus scan optional future.
- **Referral**: Dub.co shortlinks + webhooks to attribute click→form→lead. Alternatives in §5.
- **Email**: Resend/Postmark for transactional notifications.
- **Monitoring**: Sentry (frontend & backend), Supabase log drains.
- **Hosting**: Vercel (Preview/Prod), custom domain.

---

## 4) Data Model (ERD Outline)

> All tables live in `public` schema unless noted. All have `created_at timestamptz default now()`, `updated_at` via trigger, and soft-delete `deleted_at` where applicable.

### 4.1 Core Identity

- **profiles**

  - `id uuid pk` — mirrors Clerk `user.id`
  - `email text unique not null`
  - `full_name text`
  - `company_name text`
  - `company_type text` (enum via check or reference to `company_types`)
  - `role text` (free text)
  - `tier text not null default 'trusted'` (enum: trusted|elite|diamond)
  - `status text not null default 'active'` (active|pending|suspended)
  - `billing_address jsonb`
  - `telegram text`, `whatsapp text`, `x_profile text`
  - `internal_poc uuid fk → users(id)` (assigned Sales)
  - `terms_version text`, `terms_accepted_at timestamptz`

- **users** (internal staff directory)

  - `id uuid pk` (Clerk org member id)
  - `email text unique`
  - `display_name text`
  - `role text` (sales|ops|accounting|super\_admin)

- **teams\_assignments** (for Sales→Partner mapping)

  - `id uuid pk`
  - `sales_user_id uuid fk → users`
  - `partner_id uuid fk → profiles`
  - `active boolean default true`

### 4.2 Referrals & Deals

- **referral\_links**

  - `id uuid pk`
  - `partner_id uuid fk → profiles`
  - `slug text unique` (e.g., `gian`)
  - `destination_url text` (form URL)
  - `vendor text` (dub|internal)
  - `vendor_id text` (shortlink id)
  - `utm jsonb` (default UTM schema)

- **referral\_events** (from Dub webhooks)

  - `id uuid pk`
  - `referral_link_id uuid`
  - `type text` (click|unique\_click|conversion)
  - `ip inet`, `ua text`, `country text`
  - `occurred_at timestamptz`

- **leads** ("Connect Us" submissions)

  - `id uuid pk`
  - `partner_id uuid`
  - `company_name text`
  - `website text`, `x_link text`
  - `project_poc jsonb {name, title, email, telegram}`
  - `notes text`
  - `status text default 'in_review'` (in\_review|approved|rejected)
  - `source jsonb {link_id, utm, cookie_id}`

- **deals**

  - `id uuid pk`
  - `lead_id uuid fk → leads`
  - `deal_type text` (upfront|marketing|investment|incubation|hybrid)
  - `closed_by uuid fk → users`
  - `value_usd numeric`
  - `token_details jsonb {symbol, total, received, liquidated, liquidation_usd}`
  - `commission_pct numeric` (derived by tier at close)
  - `commission_usd numeric generated always as (value_usd * commission_pct) stored`
  - `status text` (in\_progress|closed|lost|paid)
  - `last_updated timestamptz`

- **earnings** (per-deal partner view)

  - `id uuid pk`
  - `deal_id uuid`
  - `partner_id uuid`
  - `commission_due_usd numeric`
  - `commission_paid_usd numeric default 0`
  - `pending_usd numeric generated always as (commission_due_usd - commission_paid_usd) stored`

### 4.3 Payouts & Invoices

- **withdrawal\_requests**

  - `id uuid pk`
  - `partner_id uuid`
  - `amount_usd numeric`
  - `method text` (usdt|bank)
  - `wallet_or_bank jsonb`
  - `status text default 'in_review'` (in\_review|approved|paid|rejected)
  - `accounting_note text`
  - `tx_ref text` (hash or bank reference)

- **payout\_applications** (deal → withdrawal mapping)

  - `id uuid pk`
  - `withdrawal_id uuid fk`
  - `deal_id uuid fk`
  - `applied_usd numeric`

### 4.4 Content & Files

- **files**
  - `id uuid pk`
  - `owner_id uuid` (profile or user)
  - `purpose text` (kyc|id|attachment|proof\_of\_payment)
  - `key text` (S3 key)
  - `mime text`, `size int`
  - `expires_at timestamptz null`

### 4.5 Tiering & Rules

- **tiers** (seeded)

  - trusted: 5% | upgrade when ≥3 closed deals OR ≥150k revenue
  - elite: 7.5% | upgrade when ≥10 deals OR ≥1M revenue
  - diamond: 10% | invite-only

- **tier\_progress** (materialized view or table)

  - `partner_id`, `closed_deals_count`, `revenue_usd`
  - `auto_flag boolean` (meets upgrade) → task for Ops.

---

## 5) Referral Tracking Design — Internal Slug Links (no Dub)

**Approach**

- Use native, human-readable partner slugs and a dedicated route: `/referral/{slug}` (e.g., `/referral/francesco`).
- Each partner has a unique `referral_slug` (stored on `profiles`).
- Visiting `/referral/{slug}` resolves the partner server-side and either:
  1. renders a lightweight landing page with the partner shown as POC and a clear CTA to “Apply/Refer a Project”, or
  2. immediately forwards to `/lead/new?partner={partner_id}&slug={slug}` (configurable).

**Attribution & Prefill**

- On `/referral/{slug}`, set a first‑party, essential cookie (e.g., `ibc_partner_ref`) containing `{partner_id, slug}` with a 30‑day TTL for continuity; this avoids any third‑party tracker.
- The **Lead** page always trusts the **server‑resolved** partner from slug/cookie; client‑supplied `partner_id` is ignored.
- The **POC panel** on the lead page displays the partner’s name, avatar (optional), and contact handles to confirm attribution.
- UTM parameters (if present) are passed through and stored on the lead record for reporting.

**Slug Governance**

- Constraints: 3–32 chars, letters/numbers/dashes, reserved list (e.g., `admin`, `support`, `help`, `referral`, `lead`, etc.).
- Partners may request vanity changes; Ops approval required.

**Metrics (optional/minimal)**

- If click counts are needed, increment a `referral_hits` table (partner\_id, slug, occurred\_at, ip\_country) on server render of `/referral/{slug}`. This is optional and can be added later without breaking the flow.

**Privacy & Simplicity**

- No external shortener or webhooks; no third‑party cookies.
- All attribution is first‑party and deterministic. The cookie is scoped to the ibc domain and marked `SameSite=Lax`.

---

## 6) Forms & Validation

**Principles**

- Single source of truth with **Zod** schemas; forms use **TanStack Form** with field-level validation + server revalidation (never trust client).
- XSS/HTMLi guarded via `.trim()`, `.max()`, and whitelist patterns; free-text fields escaped server-side.
- Public forms protected by **Cloudflare Turnstile** + IP rate limits; all submissions idempotent with a nonce.
- No PII beyond what is required; see §15.

### 6.1 Shared Schemas (Zod)

> Stored in `schemas/*.ts`; imported by UI and API. Server-side always validates again.

- **Common**: `uuid`, `email`, `url`, `money` (decimal ≥ 0, 2dp), `slug` (`^[a-z0-9-]{3,32}$`).
- **Contacts**: `telegram` (`^[a-zA-Z0-9_]{5,}$`), `whatsapp` (E.164), `name` (2–80 chars).

### 6.2 Lead Submission (`/lead/new`)

Fields

- `company_name` (2–120), `website` (url, optional), `x_link` (url, optional)
- `project_poc.{name,title,email,telegram}` (email required)
- `notes` (≤ 2000 chars)
- Hidden/readonly attribution: `{utm, click_id, link_id, cookie_id}` Validation & UX
- Inline errors; disable submit until valid; Turnstile challenge before POST.
- POST `/api/leads` → service role inserts + maps attribution; returns lead id.

### 6.3 Withdrawal Request (`/withdrawals/new`)

Fields

- `amount_usd` (money > 0), `method` (`usdt|bank`)
- If **usdt**: `wallet_or_bank.{network, address}` (address regexp per network)
- If **bank**: `wallet_or_bank.{iban|account_no, swift, bank_name, beneficiary_name}` Validation & Rules
- Server computes `available_balance(partner_id)` and rejects client-specified amounts beyond it.
- Zod `.refine()` ensures method-specific fields present & valid.

### 6.4 Account Settings (`/account`)

Fields

- `full_name`, `company_name`, `billing_address`, `contacts` (telegram/whatsapp/x) Process
- Autosave to a **pending change** request; Ops approves in backoffice (§10.2). Audit trail kept.

### 6.5 Referral Vanity Slug Request (`/referrals`)

Fields

- `desired_slug` (`slug` pattern), reason (≤ 280 chars) Rules
- Server ensures uniqueness; reserved list enforced; Ops approval creates/updates Dub link.

### 6.6 Admin/Backoffice Forms

- **Ops Approvals**: approve/deny partner onboarding & profile changes; required `reason` on denial.
- **Sales Deal Editor**: `deal_type` enum; `value_usd` (money); optional `token_details` with `{symbol (A–Z 2–8), totals ≥ 0}`; cross-field guards.
- **Accounting Payouts**: status machine `in_review → approved → paid|rejected` with mandatory `tx_ref` on `paid`.

### 6.7 Invoice Wizard — **Zero-Persistence Design (client-only)**

- **Goal**: Partners can **autofill** their amounts (e.g., \$1,000 for any reason) and generate a PDF **without storing any invoice data** in DB **or** browser storage.
- **Mechanics**:
  - Prefill from profile (name, company, wallet/bank) via API **read-only**.
  - Line items: `{description (≤140), amount_usd (money), quantity (default 1)}`; computed totals, currency = USD.
  - All state kept **in-memory**; **no** `localStorage`, `IndexedDB`, or cookies. `Cache-Control: no-store` on the page; Service Worker bypassed.
  - On **Download**, generate PDF client-side and embed an **encrypted JSON payload** (WebCrypto AES‑GCM) in PDF metadata (XMP) with a one-time **passphrase** supplied by the partner. We never transmit or store the key or payload.
  - For **Re-use**, partner **drags & drops the PDF** back into the wizard: we parse the file **client-side only**, decrypt with the passphrase, and repopulate the form. File contents are **never uploaded**.
  - Optional: include a printable **QR** containing only a short hash of totals (no PII) for human reconciliation.
- **Outputs**: a downloadable PDF; optional email to Accounting contains only a **summary** (totals + reference id), never the PDF or line items.

### 6.8 Accessibility & i18n

- Labels/aria for all inputs; keyboard navigable; numeric fields with locale-aware parsing but stored as decimal strings.

---

## 7) Authentication & Access Control

- **Clerk** user management (email-only sign-in + OTP/Magic link), optional 2FA.
- **Allowlist**: Partners are provisioned in `profiles` with `email`; a Clerk webhook (user.created) checks allowlist before completing onboarding—otherwise show "Access pending" view.
- **Session to DB**: Use Clerk JWT template with `sub` = user id; Supabase client uses `postgres-bridge` with RLS policies:
  - `profiles`: `id = auth.uid()` for partner reads/writes (limited fields).
  - `leads`: partner can `select` where `partner_id = auth.uid()`; insert via public form with service role; staff by role.
  - `deals`, `earnings`: partner `select` where `partner_id = auth.uid()`.
  - Staff roles attached via `users.role` and a `get_my_role()` helper; policies gate per-role access.

---

## 8) Authorization Policies (RLS Snippets)

- **Partners**
  - `select` on their own `profiles`, `leads`, `deals`, `earnings`, `withdrawal_requests`.
  - `insert` on `withdrawal_requests` with constraint `amount ≤ available_balance(partner_id)`.
- **Sales**
  - `select` partners/deals/leads **only** where an active `teams_assignments` exists to them.
- **Ops**
  - `update` approval fields on `profiles`, `leads`; assign tiers & sales.
- **Accounting**
  - `update` payout statuses and financials on `deals`/`earnings`.
- **Super Admin**
  - bypass via elevated role check function.

---

## 9) Feature Specifications (Partner UI)

### 9.1 Dashboard Home

- Header: partner name, tier badge, assigned Sales contact (name, email, Telegram, WhatsApp).
- Stats: *Projects Referred*, *Closed Deals*, *Tier Progress* (e.g., `2/3 deals` and `$110k/$150k`).
- Cards: **My Tier**, **My Earnings** (Total Generated, Total Received, Total Pending).
- CTAs: **Withdraw**, **Submit New Deal**, **View Deal History**.
- **My Referral Link** panel with copy buttons and QR; shows referral metrics (clicks, referrals, closed) and link management (request vanity slug).

### 9.2 Account Overview

- Tier benefits list; portfolio tracker table (filters: date, company, status warm|cold|won|lost); deep-links into deal or lead detail.

### 9.3 Deal Summary

- Commissions table with columns: project, type, closed by, value, liquidation USD, commission %, commission USD, status.
- Filters: date range, value/commission sort, status.
- Export CSV.

### 9.4 My Earnings

- Totals: **Estimated**, **Withdrawn**, **Pending**.
- Breakdown table: deal name, type, amount, % commission, USD value, status (Pending|Paid|Lost/Rejected).
- **Withdraw** flow: launches invoice wizard (client-only artifact), then posts `withdrawal_requests` record.

### 9.5 Connect Us (Lead Submission)

- Form fields: company/project, website, X link, POC name/title, notes.
- On submit: create `leads` → notify Ops. If approved, partner sees it in Portfolio.

### 9.6 Account Settings

- Editable fields (from profile). On submit: create a `profile_changes` request row for Ops to approve; UI shows pending badges and audit trail.

### 9.7 Tier Info, Terms & Conditions, Contact Us, Resources

- Tier Info: badge, benefits, progression tracker.
- T&C: rendered from CMS (versioned). Stored `terms_version` & `accepted_at` per user.
- Contact: assigned Sales info + booking link.
- Resources: decks/case studies via signed file links.

---

## 10) Admin/Backoffice

### 10.1 Sales

- My Partners view (only assigned). See partner metrics, portfolio, and open questions.
- Deal editor for assigned leads/deals.

### 10.2 Ops

- Application queue: approve/deny onboarding, assign tier & salesperson.
- Account changes queue: approve/deny field edits; changelog retained.
- Bulk reassignment tool: move all partners from Sales A → Sales B.

### 10.3 Accounting

- Withdrawal queue: statuses (In Review → Approved → Paid), capture `tx_ref` or on-chain hash; optional upload proof (UploadThing).
- Deal financials editor (liquidation values, tokens received/liquidated).
- Exports: partner list, payouts, deals CSV.

### 10.4 Super Admin

- All-access dashboard, analytics (top partners, revenue), tier approvals.

---

## 11) API, Server Actions & Edge Functions

**Route Handlers (examples)**

- `POST /api/leads` — public form submission (Turnstile + rate limit); service role inserts and attributes with UTM/cookie.
- `POST /api/withdrawals` — partner-initiated; server validates available balance → create request.
- `GET /api/exports/deals.csv` — role-gated streaming CSV.

**Edge Functions**

- `webhooks/dub` — verify signature; write `referral_events` + upsert link if unknown.
- `cron/tier-flags` — nightly recompute `tier_progress`, flag auto-upgrades.
- `cron/reminders` — nudge Ops if approvals >48h pending; email via Resend.

**Utilities**

- `lib/attr.ts` — parse UTM/cookie; map to partner.
- `lib/rbac.ts` — helpers that mirror RLS in UI.

---

## 12) File Storage & Uploads

- UploadThing routes: `/api/upload/kyc`, `/api/upload/proof`.
- S3 keys use `org/{role}/{userId}/{uuid}.{ext}`; all downloads are signed URLs with 5–10 min lifetime.
- Virus scanning (optional future) via queued Lambda; for now limit types and size.

---

## 13) Invoices & Withdrawals

### 13.1 Withdrawals (server-backed)

- Partners request payouts against `earnings.pending_usd`.
- Accounting processes statuses and records `tx_ref`; optional **proof\_of\_payment** upload via UploadThing.

### 13.2 Invoices (partner-generated, **no persistence**)

- The app provides a client-only **Invoice Wizard** (see §6.7) that pre-fills identity/payment data and lets partners add amounts (e.g., \$1,000) for any reason.
- We **do not** store invoice drafts, line items, amounts, or PDFs **in the DB** and **do not** persist them in browser storage. All processing is in-memory.
- Partners **download** a pre-filled PDF template. To reuse, they can **drag & drop** the same PDF back into the wizard; parsing and prefill happen **entirely in-browser** with WebCrypto decryption using their passphrase.
- The app may send Accounting a **minimal summary** (partner id, date, gross total, withdrawal id/ref) **without** line items or attachments.
- Caching disabled on invoice routes (`Cache-Control: no-store`, `Pragma: no-cache`); Service Worker ignores these paths; Sentry breadcrumbs redact invoice context.

---

## 14) Tier Progression Logic

- Trusted (5%), Elite (7.5%), Diamond (10%).
- Close event recalculates `tier_progress`. If thresholds met (≥3 closed deals or ≥150k USD for Elite; ≥10 deals or ≥1M USD for Diamond), create `tier_review` task for Ops. Diamond remains manual invite.

---

## 15) Security, Privacy & Compliance

- Clerk MFA optional; email-only sign-in + magic links.
- Email allowlist enforced pre-login. Unapproved users see "Access pending".
- RLS everywhere; no broad `service_role` use in client.
- PII minimization: KYC files only if required; time-limited access; audit trail of views.
- Prevent SQLi/HTMLi via parameterized queries + Zod validation.
- CSRF: Next Route Handlers require session; public forms use Turnstile + per-IP rate limit.
- Content Security Policy; HTTP security headers via `next-safe`.

---

## 16) Observability & SLOs

- **SLOs**: p95 dashboard TTFB < 500ms (SSR), API p95 < 300ms (read) / < 800ms (writes), uptime ≥ 99.9%.
- **Monitoring**: Sentry (frontend/backend), Supabase error logs, Webhook dead-letter queue with alert on >5 consecutive failures.
- **Analytics**: aggregate partner performance; no 3rd-party trackers without consent.

---

## 17) Testing Strategy

- Unit: Zod schemas, RBAC helpers, commission math.
- Integration: Route Handlers with mocked Clerk/Supabase.
- E2E: Playwright flows — login gating, submit lead, see portfolio, request withdrawal, admin approves.
- Security tests: RLS negative tests; ensure cross-tenant isolation.

---

## 18) Deployment & Environments

- Envs: `dev`, `staging`, `prod`.
- One-click DB reset in `dev` with seed partners and a few deals.
- Vercel Preview deployments on PR; DB branches via Supabase `shadow` db for migrations.
- Feature flags for Modules: Referral Metrics, Invoices Wizard, Bulk Reassignment.

**Config Vars**

- `CLERK_…`, `NEXT_PUBLIC_CLERK_…`
- `NEXT_PUBLIC_SUPABASE_URL`, `SUPABASE_SERVICE_ROLE_KEY`
- `UPLOADTHING_TOKEN`, `S3_BUCKET`, `S3_REGION`, `S3_ACCESS_KEY_ID`, `S3_SECRET_ACCESS_KEY`
- `DUB_API_KEY`, `DUB_WEBHOOK_SECRET`
- `RESEND_API_KEY` or `POSTMARK_TOKEN`
- `TURNSTILE_SECRET`

---

## 19) Migrations (initial)

- `01_profiles.sql`, `02_users.sql`, `03_tiers.sql`, `04_referral_links.sql`, `05_leads.sql`, `06_deals.sql`, `07_earnings.sql`, `08_withdrawals.sql`, `09_files.sql`, `10_assignments.sql`, `11_policies.sql`.

---

## 20) Open Questions & Decisions Needed

1. Should Accounting optionally store counterparty invoices on S3 for audit? (Current stance: partner keeps local; we store only metadata.)
2. Confirm withdrawal minimum & frequency limits.
3. Confirm whether Sales should see partner contact details for **all** assigned portfolios or only per-deal.
4. Confirm whether partners can request vanity referral slugs.
5. Confirm whether to expose API keys to partners for programmatic submissions (probably **no** at v1).

---

## 21) Milestones (engineering)

- **M0**: Schema & RLS, Clerk wiring, Dub link creation (2–3 days)
- **M1**: Partner UI (Home, Deal Summary, Earnings) (3–5 days)
- **M2**: Leads + Approvals + Sales/Ops views (3–5 days)
- **M3**: Withdrawals + Accounting (3–4 days)
- **M4**: Exports + Analytics + Polish (3–4 days)
- **M5**: QA/UAT + Docs (3 days)

---

## 22) Acceptance Criteria (samples)

- A partner on Trusted tier sees accurate totals (±\$1 rounding) and only their data.
- Sales user sees only assigned partners/leads and cannot access others via direct URL.
- Submitting a lead via public form with a partner’s link attributes the lead to that partner and appears within 30 seconds on the dashboard.
- Withdrawing when balance is below threshold shows a disabled button with tooltip.
- Ops can bulk reassign all partners from Sales A to Sales B in ≤ 2 clicks and within 1 minute for 1k partners.
- **Invoice privacy**: No invoice amounts/line items appear in network logs or server DB. DevTools audit shows **no** local storage/IndexedDB usage on invoice routes. Reloading the invoice page loses state unless a PDF is dropped and decrypted client-side.

---

## 23) Pages & Navigation Structure (Next.js App Router)

### 23.1 Route Map (simplified)

```
/app
  /(public)
    /referral/[slug]  → Partner landing (attribution cookie) (§24.4.1)
  /(auth)
    /sign-in  /sign-up  (Clerk hosted routes)
  /(portal)
    /                 → Dashboard Home (§24.4.3)
    /onboarding       → First-time setup stepper (§24.4.10)
    /account
      /overview       → Portfolio tracker (§24.4.11)
      /settings       → Account Settings (§24.4.7)
    /tier             → Tier Info (§24.4.12)
    /terms            → Terms & Conditions (§24.4.13)
    /support          → Contact & Support (§24.4.14)
    /referrals        → Link panel & vanity slug request (§24.4.9)
    /lead/new         → Connect Us form (§24.4.2)
    /deals            → Deal Summary (§24.4.4)
    /earnings         → My Earnings (§24.4.5)
    /withdrawals
      /new            → Withdrawal Request + Invoice Wizard (§24.4.6)
      /[id]           → Withdrawal detail & timeline (§24.4.6)
    /resources        → Resources (§24.4.8)
  /(admin)
    /                 → Admin home (§24.5.1)
    /partners         → Sales: My Partners (§24.5.2)
    /leads            → Ops: Approvals queue (§24.5.3)
    /deals            → Sales/Ops: Deal editor (§24.5.4)
    /withdrawals      → Accounting queue (§24.5.5)
    /analytics        → Super Admin dashboards (§24.5.6)
    /tools/reassign   → Bulk reassignment tool (§24.5.7)
/api
  /leads (POST)          /withdrawals (POST)
  /exports/deals.csv (GET)
```

### 23.2 Navigation & Linking

- **Referral flow**: `/referral/{slug}` → server resolves partner → show landing or redirect to `/lead/new?partner={id}&slug={slug}` with a short‑lived, first‑party cookie for continuity.
- **Onboarding soft‑block**: after first login, redirect to `/onboarding` until required steps are completed; banner on all pages with progress.
- **Protected routes**: `middleware.ts` enforces Clerk session; role gates under `(admin)` via server components.
- **Breadcrumbs**: derived from segments; deep‑linkable detail views under `/deals/[id]` and `/withdrawals/[id]`.
- **Loading/UI**: per‑route loading and error boundaries; use Suspense for charts/tables.

### 23.3 Layout & Components

- `app/(portal)/layout.tsx` provides nav/sidebar, tier badge, and role-aware menus.
- `useActiveNav()` highlights current; `<Link prefetch>` for intra-portal transitions.
- Feature flags toggle menu items.

### 23.4 Guardrails

- Disable caching on invoice routes; `robots.txt` disallows `/invoice*`.
- All admin routes require `users.role ∈ {sales,ops,accounting,super_admin}` with server-side checks + RLS.

---

## 24) UI/UX Specification (for design & scaffolding)

This section is for the UI/UX designer to start in Figma and for frontend to scaffold quickly. It lists **pages**, **core components**, and **states**. Design with Tailwind + shadcn/ui tokens and support light/dark modes.

### 24.1 Design Principles

- **Clarity first**: data tables and forms over vanity graphs.
- **Consistency**: one component per purpose; avoid bespoke variants.
- **Speed**: default to server-rendered pages with progressive hydration.
- **Accessibility**: WCAG AA color contrast; full keyboard paths; ARIA on interactive elements.

### 24.2 Global System

- **Grid & spacing**: 12‑col fluid grid (1280–1440 content max), 4/8pt spacing scale.
- **Typography**: Display (xl/2xl), Body (base), Mono for codes/ids; truncate long names with tooltip.
- **Color**: Success (green), Warning (amber), Danger (red), Info (blue). Neutral grays for chrome.
- **Elevation**: cards with soft shadows and `rounded-2xl`. Use subtle borders.

### 24.3 Shared Components (atomic → composite)

- **Atoms**: Button (primary/secondary/ghost/destructive), IconButton, Badge (tier: Trusted/Elite/Diamond), Tag, Tooltip, Toast, Skeleton, Spinner.
- **Form fields**: TextField, TextArea, Select, Combobox, Radio, Checkbox, Switch, CurrencyInput (USD), PhoneInput (E.164), FileDrop (UploadThing), DateInput.
- **Layout**: AppShell (TopBar + SideNav), Breadcrumbs, PageHeader (title + subtitle + actions), StatCard, KPIGroup, EmptyState, Pagination, Tabs, Stepper.
- **Data**: DataTable (TanStack Table) with column filters, sort, density toggle, CSV export; InlineDrawer/Sheet for details; Modal for confirmations.
- **Feedback**: Inline form errors, success banners; non-blocking toasts for background ops.

### 24.4 Partner‑facing Pages

1. **Referral Landing** — `/referral/{slug}`

   - Hero with partner POC: avatar/initials, name/company, short intro, “Referred by {Partner}”.
   - Primary CTA: “Apply / Refer a Project” → `/lead/new` (preserves attribution).
   - Secondary: brief FAQ link.
   - States: invalid slug (friendly 404 with Contact), suspended partner (disabled CTA), loading skeleton.
   - SEO/Share: OpenGraph with partner name + logo; share buttons; copy link.

2. **Lead Submission (Connect Us)** — `/lead/new`

   - Left: form (company/project, website, X link, POC name/title/email/telegram, notes, Turnstile).
   - Right: sticky POC card with referring partner, tier badge, assigned Sales contact if known.
   - Actions: Submit (disabled until valid), Cancel.
   - Success screen: confirmation, what happens next, link to dashboard if authenticated.

3. **Dashboard Home** — `/`

   - PageHeader: greeting, tier badge, assigned Sales contact quick‑contact.
   - KPIGroup: Projects Referred, Closed Deals, Tier Progress bar, Earnings (Total/Paid/Pending).
   - Cards: “My Referral Link” (shows `/referral/{slug}`, copy + QR), “Recent Deals”, “Announcements”.
   - Empty states for new partners.

4. **Deals** — `/deals`

   - DataTable: columns [Project, Type, Closed By, Value USD, Commission %, Commission USD, Status, Updated].
   - Filters: date range, status, type; quick search.
   - Row → **Deal Detail Drawer**: overview, token details, activity.

5. **Earnings** — `/earnings`

   - Summary tiles (Estimated, Withdrawn, Pending).
   - Table by deal; “Withdraw” primary CTA when pending > 0.

6. **Withdrawals** — `/withdrawals`, `/withdrawals/new`, `/withdrawals/[id]`

   - List view: status badges, amount, date, tx\_ref if paid.
   - New: method selector (USDT/Bank), amount, method‑specific fields, preview.
   - **Invoice Wizard**: launched as full‑page or modal from New; clearly marked **no data is stored**; Download PDF; Reuse via drag‑and‑drop of a previous PDF (client‑only processing).
   - Detail: timeline of status changes; proof‑of‑payment preview if uploaded by Accounting.

7. **Account Settings** — `/account/settings`

   - Sections: Profile, Company, Billing address, Contacts (Telegram/WhatsApp/X).
   - Inline save with pending‑change badges and audit trail link.

8. **Resources** — `/resources`

   - Card grid of assets (case studies, decks, guides) with secure download links; filters.

9. **Referrals** — `/referrals`

   - Show the partner’s current slug, copy/QR, request‑vanity form, history of slug changes.

10. **Onboarding (soft‑block)** — `/onboarding`

    - Step 1: Personal & Company (full name, company, role, company type + “Other”).
    - Step 2: Comms preferences (WhatsApp/Telegram/Email) + handles.
    - Step 3: Billing address (required).
    - Step 4: Terms & Conditions acceptance (store version).
    - Done: "Access pending" or unlock if pre‑approved.
    - States: unapproved email (whitelist), partial progress recovery, validation errors.

11. **Account Overview (Portfolio Tracker)** — `/account/overview`

    - Table of referred leads with status chips (warm/cold/won/lost), date filters, quick links to Deal detail.
    - Per‑row quick actions: open Telegram/WhatsApp group link, add internal note (if allowed).

12. **Tier Info (full view)** — `/tier`

    - Current tier badge, benefits block, clear upgrade rules (e.g., ≥3 closed deals **or** ≥\$150k revenue for Elite).
    - Progression components: bar for deals and revenue; what’s next card.

13. **Terms & Conditions** — `/terms`

    - Versioned read‑only viewer; shows current accepted version + timestamp; CTA to re‑accept if updated.

14. **Contact & Support** — `/support`

    - Assigned Sales card with Telegram/WhatsApp/email, booking link (Calendly or similar), short contact form.
    - FAQ/Help links; SOP/Notion embed (optional).

### 24.5 Admin/Backoffice Pages

1. **Admin Home** — `/admin`

   - High‑level metrics, quick links to queues.

2. **Partners** — `/admin/partners`

   - Table with filters (tier, status, assigned Sales). Row → Partner admin profile with tabs: Overview, Portfolio, Changes, Notes.

3. **Leads Queue** — `/admin/leads`

   - Kanban (In Review / Approved / Rejected) or Table with bulk actions. Detail drawer: source info (slug/UTM/cookie), decision controls.

4. **Deals** — `/admin/deals`

   - Table + editor for deal fields; activity log.

5. **Withdrawals** — `/admin/withdrawals`

   - Queue by status; approve → paid with tx\_ref; attach proof via UploadThing.

6. **Analytics** — `/admin/analytics`

   - Top partners, revenue by tier, conversion by source. (Charts are nice‑to‑have; ship tables first.)

7. **Bulk Reassignment Tool** — `/admin/tools/reassign`

   - Select Sales A → Sales B, confirm count, execute with progress bar; post‑action summary of moved partners.

8. **Admin Home** — `/admin`

   - High‑level metrics, quick links to queues.

9. **Partners** — `/admin/partners`

   - Table with filters (tier, status, assigned Sales). Row → Partner admin profile with tabs: Overview, Portfolio, Changes, Notes.

10. **Leads Queue** — `/admin/leads`

    - Kanban (In Review / Approved / Rejected) or Table with bulk actions. Detail drawer: source info (slug/UTM/cookie), decision controls.

11. **Deals** — `/admin/deals`

    - Table + editor for deal fields; activity log.

12. **Withdrawals** — `/admin/withdrawals`

    - Queue by status; approve → paid with tx\_ref; attach proof via UploadThing.

13. **Analytics** — `/admin/analytics`

    - Top partners, revenue by tier, conversion by source. (Charts are nice‑to‑have; ship tables first.)

### 24.6 States & Patterns to Design

- **Access pending** after login (whitelist not approved): explain steps; CTA to Contact.
- **Onboarding soft‑block** banner with progress and Resume button on every page until completed.
- **Duplicate lead** submitted: show possible match modal with diff and "Use existing" option.
- **Withdrawal below minimum**: disabled CTA with tooltip + link to earnings.
- **Export in progress**: disable export button with spinner and toast on completion.
- Global: Loading (skeletons), Empty (illustrations + CTA), Error (retry/Support), Disabled (role or tier gates), Success (non‑modal banners).
- Forms: inline validation + summary error at top; confirm destructive actions; optimistic updates with rollback.

### 24.7 Navigation & IA

- AppShell with SideNav sections: Home, Deals, Earnings, Withdrawals, Referrals, Resources, Account.
- Admin SideNav: Home, Partners, Leads, Deals, Withdrawals, Analytics.
- Breadcrumbs on all detail pages; sticky action bar for tall pages.

### 24.8 Accessibility & Internationalization

- Keyboard: tab order left→right, Esc closes drawers/modals, focus traps.
- Screen readers: aria‑labels on icon buttons; form fields associated with errors via `aria‑describedby`.
- Copy: short, action‑oriented labels; avoid jargon; tooltips for abbreviations.

### 24.9 Intentional Difference: Invoice Handling

- **Original PRD** showed an app‑driven email of invoices to `<EMAIL>`.
- **Final design (confirmed here)**: the app **does not store or transmit invoice content**. Partners **download a PDF locally** and may **email it themselves**. The app may send Accounting a **minimal, non‑sensitive summary** only (totals + reference id), strictly no line items or attachments.

### 24.10 Handoff Checklist

- Figma: Components (atoms → templates), color/typography styles, interaction specs, responsive breakpoints.
- Export: icon set, illustration placeholders, OG images for referral landing.
- Design tokens align with Tailwind config for 1:1 implementation.

---

*End of technical PRD.*

