## IBC Partner Portal — Implementation & Testing Plan

Owner: Engineering
Updated: 2025-08-09

---

### 0) Executive Summary

The repository already contains a solid UI scaffold in Next.js (App Router) with shadcn/ui. The PRD defines a full-stack portal backed by Supabase with Clerk auth, referral flows, RLS, and admin tooling. The gap is primarily backend (DB schema, policies, API/Edge functions), auth wiring, schema validation, and comprehensive testing. This plan is phased to deliver a production-ready MVP with strong logging, security, and tests.

Key principles
- Use Bun for scripts/tooling; prefer bunx for CLIs
- Strong console logging in all server handlers and key client flows
- Single source of truth validation with Zod; server re-validates all client input
- RLS-first design; UI mirrors RBAC
- Incremental delivery with feature flags where useful

---

### 1) Codebase Assessment

Tech/Tooling
- Framework: Next.js 15 App Router, TypeScript, Tailwind + shadcn/ui
- UI components: rich set under components/ui; app-level shell and sidebar present
- Packages: React 19, next-themes, lucide-react, TanStack Table, zod (present), but no TanStack Form yet
- Config: next.config.mjs, tailwind.config.ts, postcss
- Package manager: repo has pnpm-lock.yaml; scripts in package.json use next (we will standardize on Bun commands in scripts)
- Testing/CI: no tests present; no Playwright/Jest/Vitest config; no CI

Current app structure (selected)
- app/layout.tsx — global layout + ThemeProvider
- app/page.tsx — dashboard scaffold (static data)
- app/components/* — AppShell, PageHeader, Onboarding banner, Invoice wizard (UI only)
- app/referral/[slug] — referral landing page scaffold with client component
- app/lead/new/page.tsx — lead form scaffold (local state, no zod/form libs)
- app/withdrawals/page.tsx — list scaffold, links to /withdrawals/new (not implemented yet)
- app/admin/* — admin section scaffolds for partners, leads, deals, withdrawals, analytics
- components/app-sidebar.tsx — nav with partner + admin sections
- lib/utils.ts — UI helpers only

Missing (per PRD)
- Auth: Clerk integration, org/role; allowlist gate
- Data: Supabase client, migrations, RLS policies, Edge Functions
- APIs: Route Handlers (/api/leads, /api/withdrawals, /api/exports/*)
- Validation: Shared Zod schemas under schemas/*
- RBAC helpers: lib/rbac.ts mirroring RLS
- Referral attribution utils: lib/attr.ts; cookies/UTM capture
- Storage: UploadThing routes and S3 signing, proofs/KYC
- Emails: Resend/Postmark
- Monitoring: Sentry
- Security: Turnstile, rate limiting, CSRF on mutations
- Tests: unit/integration/e2e; fixtures and seeds
- DevX: env scaffolding, scripts, seed data, CI

---

### 2) PRD Analysis (highlights)

Core features
- Partner portal with dashboard, portfolio overview, earnings, withdrawals
- Referral links (slug), landing page, attribution cookie → lead submission → lead/deal lifecycle
- Admin modules: Partners (Sales), Leads queue (Ops), Deals editor, Withdrawals (Accounting), Analytics, Tools (Bulk reassignment)
- Withdrawals: request → review → approve → paid; optional proof-of-payment upload
- Invoices wizard: client-side only (no persistence), PDF export
- Data model: profiles, users, tiers, referral_links, referral_events, leads, deals, earnings, withdrawals, files, assignments; RLS throughout
- API/Edge: route handlers for public lead submission, partner withdrawals, CSV export; edge webhooks (Dub), cron jobs (tier flags, reminders)
- Security & performance: Turnstile, rate limits, strict Zod, RLS negative tests, Sentry, p95 targets

Environments & config
- dev/staging/prod; Vercel hosting; Supabase DB + policies; feature flags for certain modules
- Required env vars for Clerk, Supabase, UploadThing, email, Turnstile, Sentry

---

### 3) Gap Analysis

Build new
- Supabase migrations + RLS; local dev container; seed scripts
- Supabase client (server + browser), auth JWT bridge
- Clerk auth + middleware gate + allowlist webhook handler
- Shared Zod schemas under schemas/*; TanStack Form usage in forms
- Route Handlers: POST /api/leads, POST /api/withdrawals, GET /api/exports/deals.csv
- Edge functions: webhooks/dub, cron/tier-flags, cron/reminders
- UploadThing config + routes for KYC/proofs
- lib/attr.ts (UTM/cookie parsing) and lib/rbac.ts (role guards), lib/log.ts (structured logging)
- Turnstile integration on public forms; rate limiting
- Sentry init (client/server)
- Tests: unit, integration, Playwright E2E; Supabase RLS tests
- CI: Lint, typecheck, unit/integration, Playwright on PR (preview deploy optional)

Modify/upgrade existing
- Convert lead form to TanStack Form + Zod; call /api/leads
- Wire referral landing to set attribution cookie and real partner state (loading/suspended)
- Withdrawals pages: implement /withdrawals/new and /withdrawals/[id]
- Admin scaffolds: data tables with TanStack Table; role-gated route groups
- Sidebar items guarded by role + feature flags

Reuse
- AppShell, App Sidebar, PageHeader, shadcn/ui components, existing layout and route skeletons

---

### 4) Implementation Plan (phased)

Effort legend: S (≤0.5d), M (1–2d), L (3–5d), XL (>1wk)

Phase M0 — Repo hygiene & DevX (S)
- docs/.env.local.example with all required keys
- Switch scripts to Bun (keep pnpm lock for now but run via Bun)
- Add robust console logging helper lib/log.ts used across server handlers
- Add feature flags mechanism (simple NEXT_PUBLIC flags or server config)

Phase M1 — Auth & Session (M)
- Install and wire Clerk (middleware.ts) with allowlist gate
  - Files: middleware.ts, app/(auth)/sign-in redirect config via Clerk, lib/auth.ts helper
- Clerk webhook (user.created) → check allowlist in profiles; show /access-pending otherwise
  - Files: app/api/webhooks/clerk/route.ts, app/access-pending/page.tsx (exists)

Phase M2 — Database & RLS (L)
- Supabase project wiring + client
  - Files: lib/supabase/server.ts, lib/supabase/client.ts
- SQL migrations under supabase/migrations per PRD §19
  - Files: supabase/migrations/01_profiles.sql … 11_policies.sql
- RLS policies matching PRD and helpers (get_my_role())
- Seed script for dev

Phase M3 — Schemas, RBAC, Attribution (M)
- Zod shared schemas under schemas/* (common, contacts, lead, withdrawal, deal, earnings)
- TanStack Form in client forms; server-side revalidation
- lib/rbac.ts (role predicates) and lib/attr.ts (UTM/cookie → partner mapping)
- Middleware to persist referral cookie from /referral/*

Phase M4 — Public Lead Submission (M)
- Implement POST /api/leads with Turnstile verification, idempotency nonce, duplicate detection
  - Files: app/api/leads/route.ts, lib/rate-limit.ts
- Wire app/lead/new to call API; success → thank-you/redirect

Phase M5 — Partner Portal Data (M)
- Replace dashboard stats with SSR data (earnings/leads/deals)
- Implement /account/overview and /referrals with real data
- CSV export GET /api/exports/deals.csv

Phase M6 — Withdrawals (M–L)
- Implement app/withdrawals/new (Invoice Wizard integration) and /withdrawals/[id]
- POST /api/withdrawals with server-side balance checks
- Accounting queue in app/admin/withdrawals with status transitions; optional UploadThing proof

Phase M7 — Admin Modules (L)
- app/admin/partners, /leads, /deals with DataTable, filters, role gates
- Bulk reassignment tool at /admin/tools/reassign

Phase M8 — Emails & Notifications (S–M)
- Resend/Postmark transactional emails for submissions, approvals, payouts
- Vercel Cron → Supabase Edge Functions for reminders/tier flags

Phase M9 — Monitoring, Security, Perf (S–M)
- Sentry (frontend/backend) init; Supabase log drains config
- Rate limits, CSRF tokens on mutations, strict headers
- Perf budgets (p95 TTFB/latency) and basic k6/autocannon checks

Phase M10 — E2E, RLS Negative Tests, UAT (M)
- Comprehensive Playwright coverage
- RLS isolation tests via Supabase
- UAT against acceptance criteria

Phase M11 — Launch (S)
- Review envs, feature flags, seed data; ship



Detailed task breakdown (selected with paths)
- Add logging helper
  - lib/log.ts — export log.info/debug/warn/error with requestId, userId; console.log under the hood
  - Usage: every API route and critical client action logs entry/exit + key fields (no secrets)
- Clerk auth
  - middleware.ts — withAuth, route groups: /(portal) requires auth, /(public) open
  - app/(auth) routes — configure Clerk hosted pages
  - app/api/webhooks/clerk/route.ts — verify signature, allowlist check, set profile status
- Supabase client
  - lib/supabase/server.ts — createServerClient() with Clerk JWT
  - lib/supabase/client.ts — createBrowserClient() for anon reads when safe
- Schemas
  - schemas/common.ts, schemas/lead.ts, schemas/withdrawal.ts, schemas/deal.ts, schemas/earnings.ts
- APIs
  - app/api/leads/route.ts — POST (Turnstile, rate limit, duplicate match, insert)
  - app/api/withdrawals/route.ts — POST (RLS-safe insert after balance check)
  - app/api/exports/deals.csv/route.ts — GET (streaming CSV)
- Edge Functions
  - supabase/functions/webhooks/dub/index.ts — signature verify, upsert referral_events
  - supabase/functions/cron/tier-flags/index.ts — recompute
  - supabase/functions/cron/reminders/index.ts — email nudges
- Storage
  - app/api/upload/kyc/route.ts and app/api/upload/proof/route.ts — UploadThing handlers
- Referral
  - lib/attr.ts — parse UTM, set cookie; app/referral/[slug] sets cookie; middleware reads it

Dependencies
- M1 (Auth) before any gated pages fetch real data
- M2 (DB/RLS) before APIs and SSR data
- M3 (Schemas/RBAC) before converting forms and route handlers
- M4 before partners can submit public leads; M6 depends on M2 and M3

Estimates (rough)
- M0 S, M1 M, M2 L, M3 M, M4 M, M5 M, M6 L, M7 L, M8 M, M9 S–M, M10 M, M11 S

Success criteria per phase
- M1: Unapproved users see /access-pending; approved users reach /(portal) with session
- M2: All tables exist; RLS passes basic positive reads and negative cross-tenant tests
- M4: Lead submission creates lead row; duplicate path confirmed; Turnstile enforced
- M6: Withdrawal request moves through statuses; balance enforced; proof upload works
- M7: Admin tables load and allow gated actions; audit logs present
- M9: Sentry receiving events; rate limits active; p95 targets met on test environment
- M10: All E2E pass; RLS negative tests green; UAT sign-off

---

### 5) Testing Strategy

Harness & tooling
- Unit: Vitest + ts-node, React Testing Library for components
- Integration: Vitest for route handlers using next/router mocks; Supabase client mocked; Zod validation tests
- E2E: Playwright (headed/CI), test users and seeded DB
- Performance: autocannon (Node) or k6 scripts for key endpoints; Lighthouse for UX
- Security: RLS negative tests, CSRF, rate-limit behavior, Turnstile bypass attempts

Setup tasks
- Add devDependencies: vitest, @testing-library/react, @testing-library/jest-dom, playwright, msw (for API mocks)
- Configure scripts (bun test, bunx playwright install, bunx playwright test)
- tests/ folder (only place for tests per repo rules)

Unit tests (examples)
- schemas/*: happy/edge/failure cases; trimming, max lengths, regex
- lib/rbac.ts: role matrices per route/page
- lib/attr.ts: UTM parsing, cookie serialization
- lib/log.ts: shape, redaction of secrets
- Commission math and money utils

Integration tests
- app/api/leads/route.ts: valid submit, duplicate, invalid Turnstile, rate limited, XSS payloads rejected
- app/api/withdrawals/route.ts: insufficient balance, success path, idempotency
- app/api/exports/deals.csv: role-gated, format correctness

E2E scenarios (Playwright)
- Auth gating: unapproved → /access-pending; approved → dashboard
- Referral → lead submission → see lead under admin queue
- Partner sees portfolio/earnings; requests withdrawal; admin approves; partner sees paid
- Suspended referral slug → messaging; invalid slug → 404

---

### 6) File/Folder Additions (planned)

- middleware.ts — Clerk gating & referral cookie handling
- lib/
  - auth.ts — Clerk helpers
  - supabase/{server.ts,client.ts} — typed clients
  - rbac.ts — role helpers
  - attr.ts — referral attribution
  - log.ts — structured console logging
  - rate-limit.ts — simple in-memory/Upstash plan
- schemas/
  - common.ts, lead.ts, withdrawal.ts, deal.ts, earnings.ts
- app/api/
  - leads/route.ts, withdrawals/route.ts, exports/deals.csv/route.ts
  - webhooks/clerk/route.ts
  - upload/{kyc,proof}/route.ts
- supabase/
  - migrations/01_profiles.sql … 11_policies.sql
  - functions/{webhooks/dub,cron/tier-flags,cron/reminders}/index.ts
- docs/
  - plan.md (this), PRD.md (exists), ADRs as needed
- tests/
  - unit/*, integration/*, e2e/*

Each new file should include a short header comment summarizing its purpose/context.

---

### 7) Risks & Mitigations

- Auth <-> DB mapping complexity → start with minimal allowlist, add robust logging in middleware and webhooks
- RLS mistakes → write RLS negative tests early; pair-review SQL policies
- Duplicate detection correctness → deterministic rules + manual override path (submit anyway) logged
- External service flakiness (Clerk, Supabase, UploadThing) → retries, idempotency keys, dead-letter logging

---

### 8) Next Actions (current sprint)

1. M0 setup: env example, Bun scripts, lib/log.ts scaffold with usage in existing pages (console logs with context)
2. M1 auth: add Clerk, middleware.ts, /access-pending flow, webhook route
3. M2 DB: create initial migrations subset (profiles, users, tiers), wire supabase client; add seed script
4. Testing bootstrap: add Vitest/RTL/Playwright config and first unit tests for schemas/common.ts and lib/log.ts

Deliverables
- Running dev app with Clerk-protected portal routes
- Passing unit tests (schemas/log) and a smoke Playwright test (home loads, referral slug 200)
- plan.md tracked and updated as phases complete



---

### 9) PRD Cross‑Check (What’s implemented vs missing)

Implemented/partial
- App Router scaffolding for portal and admin: app/(portal) pages (dashboard, deals, earnings, withdrawals list), admin routes present
- Referral landing: app/referral/[slug] (sets link to lead form; middleware sets attribution cookie)
- Auth: Clerk wired in middleware.ts, lib/auth.ts; access-pending page exists; Clerk webhook endpoint scaffolded
- Supabase: server/browser clients, typed Database, migrations including RLS policies and core functions (set_auth_user_id, get_my_role, can_access_partner)
- Validation: zod schemas under lib/validations/*; used in route handlers (leads, withdrawals, deals, analytics, profile)
- RBAC: lib/rbac.ts permission/role helpers; used in APIs
- Logging: lib/log.ts with structured console logging; used widely; middleware logs auth/referral events

Missing/incomplete vs PRD
- Turnstile on public forms (lead submission) and IP rate limiting
- UploadThing + S3 routes (/api/upload/kyc, /api/upload/proof) and file validations
- CSV export route GET /api/exports/deals.csv
- Dub.co webhook edge function and periodic cron functions (tier flags, reminders)
- Email notifications (Resend/Postmark) for events
- Sentry instrumentation (frontend/backend)
- Feature flag toggles surfaced in UI/admin (basic env flags exist in lib/feature-flags.ts)
- Admin route server-side role enforcement (middleware only logs; not enforcing roles yet)
- Terms acceptance persistence to DB (currently localStorage only)
- Tests (unit/integration/E2E) and CI
- Env example and runtime env validation; missing many deps in package.json (e.g., @clerk/nextjs, @supabase/ssr, svix)
- Security headers (CSP/HSTS/etc.) and CSRF origin checks
- Attribution utils lib/attr.ts (middleware sets cookie but no server usage yet)

---

### 10) Security & Best Practices Audit

AuthN/AuthZ
- Clerk middleware protects routes; public API allowlist includes /api/leads and /api/webhooks/* which is correct for public submissions/webhooks
- Admin pages lack enforced role checks on server components; API routes do gate with permissions, but UI access should also check and handle 403 gracefully
- requirePermission throws -> many handlers will convert to 500 (catch-all) instead of 403; standardize error handling to avoid leaking 500s for authz failures

RLS & DB
- RLS policies and helper functions exist and look aligned with PRD; service-role used for public lead submission appropriately bypasses RLS
- Ensure all new tables from migrations are RLS‑enabled with least privilege; add RLS negative tests

Input validation & sanitation
- Zod used across routes; good. For free-text (notes), ensure escaping on render; avoid logging raw PII; consider trimming/normalizing in schemas
- Analytics and other schemas referenced exist; verify they enforce bounds (date ranges, pagination)

Rate limiting & anti‑abuse
- No rate limiting found; add per-IP/path limiter for public endpoints and sensitive POSTs
- Cloudflare Turnstile not integrated; add verification on POST /api/leads

CSRF & headers
- For authenticated POSTs using cookies, add Origin/Referer checks and CSRF token for high‑risk mutations
- Add security headers via middleware: CSP, HSTS, X-Content-Type-Options, X-Frame-Options, Referrer-Policy, Permissions-Policy

Secrets & env
- No docs/.env.local.example and no runtime env validation; add zod-based env parsing
- Missing dependencies for used imports: @clerk/nextjs, @supabase/ssr, svix, etc.; install via Bun and lock

Logging & monitoring
- Good structured logging with redaction; expand sensitiveFields (add email, address, phone) and disable logging of full request bodies in prod
- Sentry not initialized; add @sentry/nextjs setup for client/server, capture exceptions in handlers

Cookies & privacy
- Referral cookie stores IP and UTM client‑readable (httpOnly: false). Move IP to server logs only; set cookie httpOnly: true, secure, sameSite=lax; read server‑side on submission

Build/quality
- next.config.mjs ignores TypeScript/ESLint errors; revert to enforcing both
- No tests or CI; add Vitest/Playwright and GitHub Actions
- Package manager inconsistency (pnpm lock, npm scripts, Bun preference); standardize to Bun scripts

Uploads
- Missing UploadThing routes and server validation; restrict mime/size; sign URLs; do not persist invoice data per PRD

Exports
- CSV export endpoint missing; add streaming response; enforce role gates

---

### 11) Remediation Checklist (prioritized)

P0 – Blockers
- Add Turnstile verification to POST /api/leads and implement IP rate limiting for /api/leads and /api/webhooks/* (prevent abuse)
- Enforce admin role gates on server for app/(admin) and standardize requirePermission error handling to return 403
- Reinstate TypeScript and ESLint checks (remove ignore* in next.config.mjs)
- Add environment validation and docs/.env.local.example; install missing deps via Bun

P1 – High
- Add security headers middleware (CSP, HSTS, X-CTO, XFO, Referrer, Permissions)
- Implement CSV export route /api/exports/deals.csv with role checks
- Implement UploadThing routes with strict file validation; keep invoice client-only
- Add Sentry instrumentation (client/server) and wrap API handlers
- Referral attribution hardening: httpOnly cookie; server-side read in /api/leads; remove IP from cookie payload

P2 – Medium
- Add Origin/Referer checks and CSRF token for authenticated POSTs (profile update, withdrawals)
- Expand log redaction list and avoid logging emails/PII at info level in prod
- Add analytics/cron/edge function scaffolds with robust logging and retries
- Standardize error responses with correlation ID (requestId) and map authz to 403, validation to 400

P3 – Tests & CI
- Bootstrap Vitest + Playwright; add unit tests for schemas, RBAC, log redaction; integration tests for route handlers; E2E happy paths and negative RLS tests
- Add CI pipeline (lint, typecheck, unit/integration, Playwright)

P4 – UX & Admin polish
- Terms acceptance persisted to profiles; reflect in UI
- Feature flags surfaced in admin with server enforcement

Ownership & Success Criteria
- Each item maps to existing plan phases (M3–M10). Mark complete when:
  - Authz failures return 403 and are logged with requestId
  - Public leads require valid Turnstile and are rate-limited
  - Security headers present; CSP report-only has no violations, then enforced
  - Sentry receiving events from client and server
  - Test suites pass locally and in CI

---

### 12) Immediate Next Steps (this audit)

- Implement P0 group above in the next PR:
  1) Add Turnstile verification util and integrate in app/api/leads/route.ts
  2) Add simple rate limiter (Redis/Upstash or in-memory with caution) and wrap public endpoints
  3) Add admin role guard to app/admin/layout.tsx (server) and verify via lib/rbac
  4) Fix requirePermission error handling to return 403 consistently
  5) Remove ignoreDuringBuilds/ignoreBuildErrors in next.config.mjs and add docs/.env.local.example
  6) Add env validation module (zod) and early fail on missing secrets
- Then proceed with P1 items and testing bootstrap as in plan sections 5–8
