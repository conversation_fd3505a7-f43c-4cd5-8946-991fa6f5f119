# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

IBC Partner Portal - A secure, self-serve portal for partners to submit/track referrals, view commissions, and request payouts. Includes internal backoffice for Sales/Ops/Accounting to manage leads, deals, and payments.

**Key Goals:**
- Accurate referral attribution (>95%) with partner-unique links
- Tier progression automation (Trusted → Elite → Diamond)
- Role-based access control with Row Level Security
- Zero-persistence invoice generation (client-side only)

## Commands

```bash
# Development
bun run dev          # Start Next.js dev server on port 3000
bun run build        # Build production bundle
bun run start        # Start production server
bun run lint         # Run ESLint

# Component Management
bunx shadcn@latest add [component]  # Add new shadcn/ui components

```

## Tech Stack (Committed per PRD)

- **Framework**: Next.js (App Router) with TypeScript
- **Styling**: Tailwind CSS + shadcn/ui
- **Database**: Supabase (Postgres + RLS, Edge Functions)
- **Auth**: Clerk (with email whitelist)
- **Storage**: UploadThing → S3-compatible
- **Referral**: Internal slug system (/referral/{slug}) with first-party cookies
- **Forms**: TanStack Form + Zod validation
- **Tables**: TanStack Table
- **Email**: Resend/Postmark (transactional)
- **Monitoring**: Sentry
- **Hosting**: Vercel
- **Anti-abuse**: Cloudflare Turnstile

## Architecture Patterns

### Authentication Flow
1. Clerk handles email-only sign-in + magic links
2. Email whitelist enforced - unapproved users see "Access pending"
3. Clerk JWT mapped to Supabase RLS via `auth.uid()`

### Referral Attribution (No External Trackers)
- Human-readable slugs: `/referral/{partner-slug}`
- Server-side resolution with first-party cookie (30-day TTL)
- No Dub.co or external shorteners - all attribution is internal

### Invoice Privacy (Critical Requirement)
- **Zero persistence**: No invoice data stored in DB or browser
- Client-side PDF generation with encrypted metadata
- Drag-and-drop reuse with passphrase decryption
- `Cache-Control: no-store` on invoice routes

### Role-Based Access
- **Partner**: Own data only (profiles, leads, deals, earnings)
- **Sales**: Only assigned partners/leads
- **Ops**: Approvals, tier management
- **Accounting**: Payout processing
- **Super Admin**: Full access

## Database Schema Overview

### Core Tables
- `profiles` - Partner accounts (mirrors Clerk user.id)
- `users` - Internal staff directory
- `referral_links` - Partner slugs and attribution
- `leads` - "Connect Us" submissions
- `deals` - Closed deals with commission calculations
- `earnings` - Per-deal partner earnings view
- `withdrawal_requests` - Payout requests
- `tiers` - Trusted (5%), Elite (7.5%), Diamond (10%)

### Key Constraints
- All tables have RLS policies enforcing role-based access
- Soft deletes via `deleted_at` timestamps
- Automatic `updated_at` triggers

## Current Implementation Status

### ✅ Completed (UI Scaffolding)
- Page structure for all routes
- shadcn/ui component integration
- Responsive sidebar navigation
- Theme switching (light/dark)
- Basic layouts and routing

### 🚧 Required Implementations
1. **Auth Setup**: Clerk integration with email whitelist
2. **Database**: Supabase setup, migrations, RLS policies
3. **Referral System**: `/referral/{slug}` landing pages
4. **Lead Forms**: Turnstile protection, attribution tracking
5. **Deal Management**: Commission calculations, tier progression
6. **Withdrawals**: Balance validation, invoice wizard
7. **Admin Tools**: Approval queues, bulk reassignment
8. **API Routes**: Protected endpoints with proper validation

## Critical Security Requirements

- Email whitelist gating before any access
- Row Level Security on all database tables
- Turnstile on public forms
- Rate limiting per IP
- No invoice data persistence (client-only)
- Signed, time-limited URLs for file downloads
- CSRF protection on all mutations

## Environment Variables Required

```env
# Clerk Auth
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up

# Supabase
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Storage
UPLOADTHING_SECRET=
UPLOADTHING_APP_ID=

# Email
RESEND_API_KEY= # or POSTMARK_TOKEN

# Security
TURNSTILE_SECRET_KEY=

# Monitoring
SENTRY_DSN=
```

## Development Milestones

**M0** (2-3 days): Database schema, RLS policies, Clerk setup
**M1** (3-5 days): Partner UI (Dashboard, Deals, Earnings)
**M2** (3-5 days): Lead submission, approval flows
**M3** (3-4 days): Withdrawals, Accounting views
**M4** (3-4 days): Exports, Analytics, Polish
**M5** (3 days): QA/Testing/Documentation

## Page Routes Reference

```
/(public)
  /referral/[slug]     # Partner landing with attribution

/(auth)
  /sign-in, /sign-up   # Clerk-hosted

/(portal)
  /                    # Dashboard home
  /onboarding         # First-time setup
  /lead/new           # Submit referral
  /deals              # View deals
  /earnings           # Commission tracking
  /withdrawals/*      # Payout requests
  /referrals          # Manage referral links
  /account/*          # Settings
  /tier               # Tier progression
  /resources          # Partner resources
  /support            # Contact support

/(admin)
  /admin              # Admin dashboard
  /admin/partners     # Partner management
  /admin/leads        # Approval queue
  /admin/deals        # Deal editor
  /admin/withdrawals  # Payout processing
  /admin/analytics    # Reports
  /admin/tools/*      # Bulk operations
```

## Testing Requirements

- Unit: Zod schemas, commission calculations, tier logic
- Integration: API routes with mocked services
- E2E: Critical flows (lead submission, withdrawal, approvals)
- Security: RLS isolation, cross-tenant protection

## Performance SLOs

- Dashboard TTFB < 500ms (p95)
- API reads < 300ms (p95)
- API writes < 800ms (p95)
- Uptime ≥ 99.9%
- NEVER IMPLEMENT THOSE STUFF ☐ Add document upload to onboarding form (passport/ID for agreements)
     ☐ Build tier progression automation (3 deals/$150K → Elite, 10 deals/$1M → Diamond)
     ☐ Add cash bonus tracking ($500 Elite, $1000 Diamond per token deal)