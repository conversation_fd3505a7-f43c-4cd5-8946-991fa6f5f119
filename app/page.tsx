import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ard, DollarSign, Users, Link2, <PERSON><PERSON>2, MessageCircle, Phone, Mail } from "lucide-react"
import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { format } from 'date-fns'

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { PageHeader } from "./components/page-header"
import { AppShell } from "./components/app-shell"
import { createClient } from '@/lib/supabase/server'
import { getCurrentUserRole } from '@/lib/rbac'
import { log } from '@/lib/log'
import { trackServerEvent } from '@/lib/analytics-server'
import Link from "next/link"
import { CopyReferralButton } from "./components/copy-referral-button"

type Lead = {
  id: string
  company_name: string
  contact_name: string
  contact_email: string
  contact_telegram?: string
  contact_phone?: string
  status: "warm" | "cold" | "won" | "lost"
  created_at: string
}

type DashboardData = {
  profile: {
    full_name: string
    tier: 'trusted' | 'elite' | 'diamond'
  }
  metrics: {
    totalEarnings: number
    earningsChange: number
    leadsCount: number
    leadsChange: number
    dealsCount: number
    dealsChange: number
    pendingPayout: number
  }
  recentDeals: Array<{
    id: string
    client_company: string
    commission_amount: number
    status: string
    created_at: string
  }>
  referredLeads: Lead[]
  referralSlug?: string
  salesRep?: {
    display_name: string
    email: string
  }
}

async function getDashboardData(): Promise<DashboardData> {
  const { userId } = await auth()
  
  if (!userId) {
    redirect('/sign-in')
  }

  try {
    // Check user role first
    const role = await getCurrentUserRole()
    if (!role) {
      redirect('/access-pending')
    }

    // Use service role for profile lookup (needed for role detection)
    // but regular client for actual data queries to respect RLS
    const serviceClient = await createClient(true)
    const client = await createClient()

    // Get profile using service role
    const { data: profile } = await serviceClient
      .from('partners_profiles')
      .select('full_name, tier')
      .eq('id', userId)
      .single()

    if (!profile) {
      log.error('Profile not found for user', { 
        userId, 
        metadata: { role } 
      })
      redirect('/access-pending')
    }

    // Get earnings and deals metrics using regular client (RLS will filter)
    const { data: deals } = await client
      .from('partners_deals')
      .select('commission_amount, status, created_at')
      .eq('partner_id', userId)

    const totalEarnings = deals
      ?.filter(d => d.status === 'paid')
      .reduce((sum, d) => sum + d.commission_amount, 0) || 0

    const dealsCount = deals?.length || 0
    const pendingPayout = deals
      ?.filter(d => d.status === 'confirmed')
      .reduce((sum, d) => sum + d.commission_amount, 0) || 0

    // Get leads count using regular client (RLS will filter)
    const { count: leadsCount } = await client
      .from('partners_leads')
      .select('*', { count: 'exact', head: true })
      .eq('partner_id', userId)

    // Get recent deals using regular client (RLS will filter)
    const { data: recentDeals } = await client
      .from('partners_deals')
      .select('id, client_company, commission_amount, status, created_at')
      .eq('partner_id', userId)
      .order('created_at', { ascending: false })
      .limit(3)

    // Get referral link using service client (to bypass RLS)
    const { data: referralLink } = await serviceClient
      .from('partners_referral_links')
      .select('slug')
      .eq('partner_id', userId)
      .eq('active', true)
      .single()

    // Get referred leads using regular client (RLS will filter)
    const { data: referredLeads } = await client
      .from('partners_leads')
      .select('id, company_name, contact_name, contact_email, contact_telegram, contact_phone, status, created_at')
      .eq('partner_id', userId)
      .order('created_at', { ascending: false })
      .limit(10)

    // Get assigned sales rep using service client (to bypass RLS)
    const { data: salesAssignment } = await serviceClient
      .from('partners_teams_assignments')
      .select(`
        partners_users!sales_user_id(
          display_name,
          email
        )
      `)
      .eq('partner_id', userId)
      .eq('active', true)
      .single()

    return {
      profile,
      metrics: {
        totalEarnings,
        earningsChange: 20.1, // TODO: Calculate actual change
        leadsCount: leadsCount || 0,
        leadsChange: 180.1, // TODO: Calculate actual change
        dealsCount,
        dealsChange: 19, // TODO: Calculate actual change
        pendingPayout
      },
      recentDeals: recentDeals || [],
      referredLeads: referredLeads || [],
      referralSlug: referralLink?.slug,
      salesRep: salesAssignment?.partners_users as { display_name: string; email: string } | undefined
    }

  } catch (error) {
    log.error('Failed to fetch dashboard data', { userId, error })
    throw new Error('Failed to load dashboard data')
  }
}

export default async function Dashboard() {
  const data = await getDashboardData()

  const formatCurrency = (amount: number) =>
    new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount)

  const getTierEmoji = (tier: string) => {
    switch (tier) {
      case 'diamond': return '💎'
      case 'elite': return '⭐'
      case 'trusted': return '🔰'
      default: return '🔰'
    }
  }

  const getTierLabel = (tier: string) => tier.charAt(0).toUpperCase() + tier.slice(1)

  return (
    <AppShell title="Dashboard">
      <div className="flex flex-col gap-4">
        <PageHeader 
          title={`Welcome back, ${data.profile.full_name}!`} 
          subtitle="Here's a summary of your partner activity."
          actions={
            <Badge variant="outline" className="text-base py-1 px-3">
              <span className="mr-2">{getTierEmoji(data.profile.tier)}</span> {getTierLabel(data.profile.tier)} Tier
            </Badge>
          }
        />
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(data.metrics.totalEarnings)}</div>
              <p className="text-xs text-muted-foreground">+{data.metrics.earningsChange}% from last month</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Projects Referred</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.metrics.leadsCount}</div>
              <p className="text-xs text-muted-foreground">+{data.metrics.leadsChange}% from last month</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Closed Deals</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.metrics.dealsCount}</div>
              <p className="text-xs text-muted-foreground">+{data.metrics.dealsChange}% from last month</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Payout</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(data.metrics.pendingPayout)}</div>
              <p className="text-xs text-muted-foreground">Awaiting next cycle</p>
            </CardContent>
          </Card>
        </div>
        <div className="grid auto-rows-fr gap-4 lg:grid-cols-3">
          <Card className="lg:col-span-2">
            <CardHeader className="flex flex-row items-center">
              <div className="grid gap-2">
                <CardTitle>Recent Deals</CardTitle>
                <CardDescription>An overview of your most recent closed deals.</CardDescription>
              </div>
              <Button asChild size="sm" className="ml-auto gap-1">
                <Link href="/deals">
                  View All
                  <ArrowUpRight className="h-4 w-4" />
                </Link>
              </Button>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Client</TableHead>
                    <TableHead className="hidden sm:table-cell">Status</TableHead>
                    <TableHead className="hidden xl:table-column">Date</TableHead>
                    <TableHead className="text-right">Commission</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.recentDeals.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center text-muted-foreground">
                        No deals yet. Start referring clients to earn commissions!
                      </TableCell>
                    </TableRow>
                  ) : (
                    data.recentDeals.map((deal) => (
                      <TableRow key={deal.id}>
                        <TableCell>
                          <div className="font-medium">{deal.client_company}</div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">
                          <Badge className="text-xs capitalize" variant={
                            deal.status === 'paid' ? 'default' : 
                            deal.status === 'confirmed' ? 'secondary' : 
                            deal.status === 'disputed' ? 'destructive' : 'outline'
                          }>
                            {deal.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="hidden md:table-cell lg:hidden xl:table-column">
                          {format(new Date(deal.created_at), 'MMM dd, yyyy')}
                        </TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(deal.commission_amount)}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>My Referral Link</CardTitle>
              <CardDescription>Share this link to start referring projects.</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-4">
              {data.referralSlug ? (
                <>
                  <div className="flex items-center space-x-2 rounded-lg border bg-muted p-2">
                    <Link2 className="h-4 w-4 text-muted-foreground" />
                    <span className="flex-1 text-sm text-muted-foreground truncate">
                      /referral/{data.referralSlug}
                    </span>
                    <CopyReferralButton slug={data.referralSlug} />
                  </div>
                  <div className="text-center text-sm text-muted-foreground">
                    Share this link to start earning commissions
                  </div>
                </>
              ) : (
                <div className="text-center text-sm text-muted-foreground p-4 border rounded-lg">
                  No referral link set up yet. Contact support to get your unique link.
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Sales Rep Contact Section */}
        {data.salesRep && (
          <Card>
            <CardHeader>
              <CardTitle>Your Sales Representative</CardTitle>
              <CardDescription>Contact your assigned sales rep for support and questions.</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-4">
              <div className="flex items-center space-x-4">
                <div className="h-12 w-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-lg">
                  {data.salesRep.display_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-lg">{data.salesRep.display_name}</h3>
                  <p className="text-sm text-muted-foreground">Sales Representative</p>
                </div>
              </div>
              <div className="flex gap-2">
                <Button asChild variant="outline" className="flex-1">
                  <a href={`mailto:${data.salesRep.email}`} className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Email
                  </a>
                </Button>
              </div>
              <div className="text-center text-sm text-muted-foreground">
                Reach out anytime for assistance with your partnerships
              </div>
            </CardContent>
          </Card>
        )}
        
        {/* Portfolio Tracker Section */}
        <Card>
          <CardHeader>
            <CardTitle>Portfolio Tracker</CardTitle>
            <CardDescription>All leads you referred with quick actions.</CardDescription>
          </CardHeader>
          <CardContent>
            {data.referredLeads.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                No referred leads yet. Start using your referral link to track leads here!
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Company</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead className="hidden md:table-cell">Status</TableHead>
                    <TableHead className="hidden lg:table-cell">Date</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.referredLeads.map((lead) => (
                    <TableRow key={lead.id}>
                      <TableCell className="font-medium">{lead.company_name}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium">{lead.contact_name}</span>
                          <span className="text-sm text-muted-foreground">{lead.contact_email}</span>
                        </div>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        <Badge
                          variant={
                            lead.status === "won"
                              ? "default"
                              : lead.status === "lost"
                                ? "destructive"
                                : lead.status === "warm"
                                  ? "secondary"
                                  : "outline"
                          }
                        >
                          {lead.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">
                        {format(new Date(lead.created_at), 'MMM dd, yyyy')}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {lead.contact_telegram && (
                            <Button
                              asChild
                              size="sm"
                              variant="outline"
                              className="h-8 w-8 p-0"
                              aria-label="Open Telegram"
                            >
                              <a 
                                href={`https://t.me/${lead.contact_telegram.replace("@", "")}`} 
                                target="_blank" 
                                rel="noreferrer"
                              >
                                <MessageCircle className="h-4 w-4" />
                              </a>
                            </Button>
                          )}
                          {lead.contact_phone && (
                            <Button
                              asChild
                              size="sm"
                              variant="outline"
                              className="h-8 w-8 p-0"
                              aria-label="Open WhatsApp"
                            >
                              <a 
                                href={`https://wa.me/${lead.contact_phone.replace(/\D/g, "")}`} 
                                target="_blank" 
                                rel="noreferrer"
                              >
                                <Phone className="h-4 w-4" />
                              </a>
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
