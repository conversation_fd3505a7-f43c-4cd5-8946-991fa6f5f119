"use client"

import * as React from "react"
import { CaretSortIcon, ChevronDownIcon, DotsHorizontalIcon } from "@radix-ui/react-icons"
import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { AppShell } from "../components/app-shell"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { File, Filter, LayoutList, Info, Loader2 } from "lucide-react"
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet"
import { useToast } from "@/hooks/use-toast"
import { format } from "date-fns"

type Deal = {
  id: string
  client_company: string
  deal_value: number
  commission_rate: number
  commission_amount: number
  status: "pending" | "confirmed" | "paid" | "disputed"
  payment_date: string | null
  created_at: string
  partners_profiles?: {
    full_name: string
    company_name: string
    tier: string
  }
  partners_leads?: {
    company_name: string
    website: string | null
  }
}

const formatCurrency = (amount: number) =>
  new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }).format(amount)

const columns: ColumnDef<Deal>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all rows"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label={`Select row for ${row.original.client_company}`}
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "client_company",
    header: "Client Company",
    cell: ({ row }) => <div className="truncate font-medium">{row.getValue("client_company")}</div>,
  },
  {
    id: "partner",
    header: "Partner",
    cell: ({ row }) => {
      const partner = row.original.partners_profiles
      return (
        <div className="truncate">
          <div className="font-medium">{partner?.full_name || 'Unknown'}</div>
          <div className="text-sm text-muted-foreground">{partner?.company_name}</div>
        </div>
      )
    },
  },
  {
    id: "tier",
    header: "Tier",
    cell: ({ row }) => {
      const tier = row.original.partners_profiles?.tier
      const variant = tier === 'diamond' ? 'default' : tier === 'elite' ? 'secondary' : 'outline'
      return <Badge variant={variant as any} className="capitalize">{tier || 'N/A'}</Badge>
    },
  },
  {
    accessorKey: "deal_value",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="text-right w-full justify-end"
        aria-label="Sort by deal value"
      >
        Deal Value
        <CaretSortIcon className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const amount = Number.parseFloat(row.getValue("deal_value"))
      return <div className="text-right font-medium">{formatCurrency(amount)}</div>
    },
  },
  {
    accessorKey: "commission_rate",
    header: "Commission %",
    cell: ({ row }) => <div className="text-right">{(row.original.commission_rate * 100).toFixed(2)}%</div>,
  },
  {
    accessorKey: "commission_amount",
    header: () => <div className="text-right">Commission USD</div>,
    cell: ({ row }) => {
      return <div className="text-right font-medium">{formatCurrency(row.original.commission_amount)}</div>
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.original.status
      const variant = {
        paid: "default",
        confirmed: "secondary", 
        pending: "outline",
        disputed: "destructive",
      }[status] ?? "outline"
      return <Badge variant={variant as any} className="capitalize">{status}</Badge>
    },
  },
  {
    accessorKey: "created_at",
    header: "Created",
    cell: ({ row }) => {
      const date = new Date(row.original.created_at)
      return <span className="text-muted-foreground">{format(date, "MMM dd, yyyy")}</span>
    },
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const deal = row.original
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0" aria-label={`Row actions for ${deal.client_company}`}>
              <DotsHorizontalIcon className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => navigator.clipboard.writeText(deal.id)}>Copy Deal ID</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => window.dispatchEvent(new CustomEvent("open-deal", { detail: deal }))}>
              View deal details
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]

export default function DealsPage() {
  const { toast } = useToast()
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})
  const [density, setDensity] = React.useState<"comfortable" | "compact">("comfortable")

  // Data state
  const [deals, setDeals] = React.useState<Deal[]>([])
  const [loading, setLoading] = React.useState(true)

  // Filters
  const [query, setQuery] = React.useState("")
  const [statusFilter, setStatusFilter] = React.useState<"" | "pending" | "confirmed" | "paid" | "disputed">("")
  const [from, setFrom] = React.useState<string>("")
  const [to, setTo] = React.useState<string>("")

  // Drawer state
  const [selectedDeal, setSelectedDeal] = React.useState<Deal | null>(null)
  const [drawerOpen, setDrawerOpen] = React.useState(false)

  // Load deals data
  React.useEffect(() => {
    const fetchDeals = async () => {
      try {
        setLoading(true)
        // Add default query parameters that the API expects
        const url = new URL('/api/deals', window.location.origin)
        url.searchParams.set('page', '1')
        url.searchParams.set('limit', '50')
        
        const response = await fetch(url.toString())
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
          console.error('API Error:', errorData)
          throw new Error(errorData.error || 'Failed to fetch deals')
        }
        const result = await response.json()
        setDeals(result.data || [])
      } catch (error) {
        console.error('Error fetching deals:', error)
        toast({
          title: "Error",
          description: "Failed to load deals. Please try again.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchDeals()
  }, [toast])

  React.useEffect(() => {
    const onOpen = (e: any) => {
      setSelectedDeal(e.detail as Deal)
      setDrawerOpen(true)
    }
    window.addEventListener("open-deal" as any, onOpen)
    return () => window.removeEventListener("open-deal" as any, onOpen)
  }, [])

  // Data filtering
  const filtered = React.useMemo(() => {
    return deals.filter((d) => {
      const matchesQuery = d.client_company.toLowerCase().includes(query.toLowerCase()) ||
                          d.partners_profiles?.full_name?.toLowerCase().includes(query.toLowerCase()) ||
                          d.partners_profiles?.company_name?.toLowerCase().includes(query.toLowerCase())
      const matchesStatus = !statusFilter || d.status === statusFilter
      const afterFrom = !from || new Date(d.created_at) >= new Date(from)
      const beforeTo = !to || new Date(d.created_at) <= new Date(to)
      return matchesQuery && matchesStatus && afterFrom && beforeTo
    })
  }, [deals, query, statusFilter, from, to])

  const table = useReactTable({
    data: filtered,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  })

  const [exporting, setExporting] = React.useState(false)
  const exportCsv = async () => {
    try {
      setExporting(true)
      const rows = table.getRowModel().rows.map((r) => r.original)
      const header = [
        "Client",
        "Type",
        "Closed By",
        "Value USD",
        "Commission %",
        "Commission USD",
        "Status",
        "Updated",
      ]
      const records = rows.map((d) => [
        d.client_company,
        'Referral',
        'Sales Team',
        d.deal_value,
        (d.commission_rate * 100).toFixed(2) + '%',
        d.commission_amount,
        d.status,
        format(new Date(d.created_at), 'MMM dd, yyyy'),
      ])
      const csv = [header, ...records]
        .map((row) => row.map((cell) => `"${String(cell).replace(/"/g, '""')}"`).join(","))
        .join("\n")
      const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = "deals.csv"
      a.click()
      URL.revokeObjectURL(url)
      toast({ title: "Export complete", description: "Your CSV file has been downloaded." })
    } finally {
      setExporting(false)
    }
  }

  const rowPad = density === "compact" ? "py-2" : "py-4"

  return (
    <AppShell title="Deals">
      <Tabs defaultValue="all">
        <div className="flex items-center gap-2 flex-wrap">
          <TabsList>
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="closed">Closed</TabsTrigger>
            <TabsTrigger value="archived" className="hidden sm:flex">
              Archived
            </TabsTrigger>
          </TabsList>
          <div className="ml-auto flex items-center gap-2 flex-wrap">
            <div className="hidden md:flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" aria-hidden />
              <Input
                placeholder="Quick search..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="w-[200px]"
                aria-label="Quick search"
              />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className="h-9 rounded-md border bg-background px-2 text-sm"
                aria-label="Filter by status"
              >
                <option value="">Status</option>
                <option value="pending">Pending</option>
                <option value="confirmed">Confirmed</option>
                <option value="paid">Paid</option>
                <option value="disputed">Disputed</option>
              </select>
              <Input
                type="date"
                value={from}
                onChange={(e) => setFrom(e.target.value)}
                className="w-[160px]"
                aria-label="From date"
              />
              <Input
                type="date"
                value={to}
                onChange={(e) => setTo(e.target.value)}
                className="w-[160px]"
                aria-label="To date"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-8 gap-1 bg-transparent" aria-label="Toggle columns">
                  <ChevronDownIcon className="h-3.5 w-3.5" />
                  <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">Columns</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) => column.toggleVisibility(!!value)}
                      >
                        {column.id}
                      </DropdownMenuCheckboxItem>
                    )
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="sm" variant="outline" className="h-8 gap-1 bg-transparent" aria-label="Density">
                  <LayoutList className="h-3.5 w-3.5" />
                  <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">Density</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Row density</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setDensity("comfortable")}>Comfortable</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setDensity("compact")}>Compact</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button
              size="sm"
              variant="outline"
              className="h-8 gap-1 bg-transparent"
              onClick={exportCsv}
              disabled={exporting}
              aria-label="Export CSV"
            >
              <File className="h-3.5 w-3.5" />
              <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                {exporting ? "Exporting..." : "Export CSV"}
              </span>
            </Button>
          </div>
        </div>
        <TabsContent value="all">
          <Card>
            <CardHeader>
              <CardTitle>Deals</CardTitle>
              <CardDescription>Manage your referred deals and view their status.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    {table.getHeaderGroups().map((headerGroup) => (
                      <TableRow key={headerGroup.id}>
                        {headerGroup.headers.map((header) => (
                          <TableHead key={header.id}>
                            {header.isPlaceholder
                              ? null
                              : flexRender(header.column.columnDef.header, header.getContext())}
                          </TableHead>
                        ))}
                      </TableRow>
                    ))}
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow className={rowPad}>
                        <TableCell colSpan={columns.length} className="h-24 text-center">
                          <div className="flex items-center justify-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            Loading deals...
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : table.getRowModel().rows?.length ? (
                      table.getRowModel().rows.map((row) => (
                        <TableRow key={row.id} data-state={row.getIsSelected() && "selected"} className={rowPad}>
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id}>
                              {flexRender(cell.column.columnDef.cell, cell.getContext())}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    ) : (
                      <TableRow className={rowPad}>
                        <TableCell colSpan={columns.length} className="h-24 text-center">
                          No deals found.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
              <div className="flex items-center justify-end space-x-2 py-4">
                <div className="flex-1 text-sm text-muted-foreground">
                  {table.getFilteredSelectedRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s)
                  selected.
                </div>
                <div className="space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => table.previousPage()}
                    disabled={!table.getCanPreviousPage()}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => table.nextPage()}
                    disabled={!table.getCanNextPage()}
                  >
                    Next
                  </Button>
                </div>
              </div>
              <div className="mt-2 flex items-center gap-2 text-xs text-muted-foreground">
                <Info className="h-3.5 w-3.5" aria-hidden />
                <span>{"Tip: Click the actions menu on a row to open the deal detail drawer."}</span>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Sheet open={drawerOpen} onOpenChange={setDrawerOpen}>
        <SheetContent className="sm:max-w-xl">
          <SheetHeader>
            <SheetTitle>Deal Details</SheetTitle>
          </SheetHeader>
          {selectedDeal ? (
            <div className="mt-4 space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <div className="text-xs text-muted-foreground">Client Company</div>
                  <div className="font-medium">{selectedDeal.client_company}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Partner</div>
                  <div className="font-medium">{selectedDeal.partners_profiles?.full_name || 'Unknown'}</div>
                  <div className="text-xs text-muted-foreground">{selectedDeal.partners_profiles?.company_name}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Partner Tier</div>
                  <Badge className="mt-1 capitalize">{selectedDeal.partners_profiles?.tier}</Badge>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Status</div>
                  <Badge className="mt-1 capitalize">{selectedDeal.status}</Badge>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Deal Value</div>
                  <div className="font-medium">{formatCurrency(selectedDeal.deal_value)}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Commission</div>
                  <div className="font-medium">
                    {(selectedDeal.commission_rate * 100).toFixed(2)}% ({formatCurrency(selectedDeal.commission_amount)})
                  </div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Created</div>
                  <div className="font-medium">{format(new Date(selectedDeal.created_at), "MMM dd, yyyy 'at' h:mm a")}</div>
                </div>
                {selectedDeal.payment_date && (
                  <div>
                    <div className="text-xs text-muted-foreground">Payment Date</div>
                    <div className="font-medium">{format(new Date(selectedDeal.payment_date), "MMM dd, yyyy")}</div>
                  </div>
                )}
              </div>
              
              {selectedDeal.partners_leads && (
                <div>
                  <div className="text-sm font-semibold mb-2">Lead Information</div>
                  <div className="text-sm space-y-1">
                    <div><strong>Original Lead:</strong> {selectedDeal.partners_leads.company_name}</div>
                    {selectedDeal.partners_leads.website && (
                      <div><strong>Website:</strong> {selectedDeal.partners_leads.website}</div>
                    )}
                  </div>
                </div>
              )}
              
              <div className="space-y-2">
                <div className="text-sm font-semibold">Deal Timeline</div>
                <ul className="space-y-1 text-sm">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                    Deal created on {format(new Date(selectedDeal.created_at), "MMM dd, yyyy")}
                  </li>
                  {selectedDeal.status === 'confirmed' && (
                    <li className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-green-500"></div>
                      Deal confirmed
                    </li>
                  )}
                  {selectedDeal.status === 'paid' && (
                    <li className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-green-600"></div>
                      Commission paid
                    </li>
                  )}
                </ul>
              </div>
            </div>
          ) : null}
        </SheetContent>
      </Sheet>
    </AppShell>
  )
}
