"use client"

import { useEffect, useState } from 'react'
import { AppShell } from "@/app/components/app-shell"
import { PageHeader } from "@/app/components/page-header"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Copy, Link2, Loader2 } from "lucide-react"
import { toast } from 'sonner'
import { format } from 'date-fns'

interface ReferralLink {
  id: string
  slug: string
  url: string
  qrCode?: string
  createdAt: string
}

interface ReferralHistory {
  id: string
  slug: string
  active: boolean
  createdAt: string
  updatedAt: string
}

interface PendingRequest {
  id: string
  requested_slug: string
  status: string
  created_at: string
}

interface ReferralData {
  currentLink: ReferralLink | null
  history: ReferralHistory[]
  pendingRequests: PendingRequest[]
}

export default function ReferralsPage() {
  const [data, setData] = useState<ReferralData | null>(null)
  const [loading, setLoading] = useState(true)
  const [vanitySlug, setVanitySlug] = useState('')
  const [submitting, setSubmitting] = useState(false)

  // Fetch referral data
  useEffect(() => {
    const fetchReferralData = async () => {
      try {
        const response = await fetch('/api/referrals')
        if (!response.ok) throw new Error('Failed to fetch referral data')
        
        const referralData = await response.json()
        setData(referralData)
      } catch (error) {
        console.error('Error fetching referral data:', error)
        toast.error('Failed to load referral data')
      } finally {
        setLoading(false)
      }
    }

    fetchReferralData()
  }, [])

  // Handle copy to clipboard
  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast.success('Copied to clipboard!')
    } catch (error) {
      toast.error('Failed to copy to clipboard')
    }
  }

  // Handle vanity slug request
  const handleVanityRequest = async () => {
    if (!vanitySlug.trim()) {
      toast.error('Please enter a preferred slug')
      return
    }

    setSubmitting(true)
    try {
      const response = await fetch('/api/referrals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ preferred_slug: vanitySlug.trim().toLowerCase() })
      })

      const result = await response.json()

      if (!response.ok) {
        toast.error(result.error || 'Failed to submit request')
        return
      }

      toast.success(result.message || 'Request submitted successfully!')
      setVanitySlug('')
      
      // Refresh data to show pending request
      const refreshResponse = await fetch('/api/referrals')
      if (refreshResponse.ok) {
        const refreshedData = await refreshResponse.json()
        setData(refreshedData)
      }
    } catch (error) {
      console.error('Error submitting vanity request:', error)
      toast.error('Failed to submit request')
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <AppShell title="Referrals">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </AppShell>
    )
  }

  const currentSlug = data?.currentLink?.slug
  const currentUrl = data?.currentLink?.url
  const fullUrl = currentUrl ? `${window.location.origin}${currentUrl}` : null

  return (
    <AppShell title="Referrals">
      <div className="flex flex-col gap-4">
        <PageHeader title="Referrals" subtitle="Manage your referral slug and share links." />
        
        {!data?.currentLink && (
          <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
            <CardContent className="pt-6">
              <p className="text-orange-800 dark:text-orange-200">
                No active referral link found. Please contact support to set up your referral link.
              </p>
            </CardContent>
          </Card>
        )}

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Current Slug</CardTitle>
              <CardDescription>Your public referral link.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {currentSlug && currentUrl && fullUrl ? (
                <>
                  <div className="flex items-center space-x-2 rounded-lg border bg-muted p-2">
                    <Link2 className="h-4 w-4 text-muted-foreground" />
                    <span className="flex-1 text-sm text-muted-foreground truncate">{fullUrl}</span>
                    <Button variant="outline" size="sm" onClick={() => handleCopy(fullUrl)}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex items-center justify-center rounded-lg border p-4 min-h-[200px]">
                    {data?.currentLink?.qrCode ? (
                      <img 
                        src={data.currentLink.qrCode} 
                        alt="QR Code for referral link" 
                        className="max-w-full max-h-full"
                      />
                    ) : (
                      <div className="text-muted-foreground text-sm">QR Code generating...</div>
                    )}
                  </div>
                </>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No active referral link
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Request Vanity Slug</CardTitle>
              <CardDescription>We'll review and confirm availability.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Label htmlFor="vanity">Preferred slug</Label>
              <Input 
                id="vanity" 
                placeholder="your-brand" 
                value={vanitySlug}
                onChange={(e) => setVanitySlug(e.target.value)}
                disabled={submitting}
              />
              {data?.pendingRequests && data.pendingRequests.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm text-muted-foreground mb-1">Pending requests:</p>
                  {data.pendingRequests.map((request) => (
                    <div key={request.id} className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {request.requested_slug}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {format(new Date(request.created_at), 'MMM d, yyyy')}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button 
                onClick={handleVanityRequest}
                disabled={submitting || !vanitySlug.trim()}
              >
                {submitting && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                Submit Request
              </Button>
            </CardFooter>
          </Card>
        </div>

        {data?.history && data.history.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Slug History</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Slug</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.history.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-mono">{item.slug}</TableCell>
                      <TableCell>
                        <Badge variant={item.active ? "default" : "secondary"}>
                          {item.active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>{format(new Date(item.createdAt), 'MMM d, yyyy')}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}
      </div>
    </AppShell>
  )
}
