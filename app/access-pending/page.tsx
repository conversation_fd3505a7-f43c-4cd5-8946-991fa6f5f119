import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { AppShell } from "@/app/components/app-shell"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Clock, XCircle, CheckCircle2, UserX } from "lucide-react"
import Link from "next/link"

async function getAccessStatus() {
  const { userId } = await auth()
  
  if (!userId) {
    return { status: 'no_auth', profile: null }
  }

  try {
    const client = await createClient(true)
    const { data: profile } = await client
      .from('partners_profiles')
      .select('status, full_name, company_name, created_at')
      .eq('id', userId)
      .single()

    return { status: profile?.status || 'no_profile', profile }
  } catch (error) {
    return { status: 'error', profile: null }
  }
}

export default async function AccessPendingPage() {
  const { status, profile } = await getAccessStatus()
  
  console.log('ACCESS_PENDING: Page loaded with status', { status, profileExists: !!profile })

  const getStatusConfig = () => {
    switch (status) {
      case 'pending':
        return {
          icon: Clock,
          title: 'Partner Application Under Review',
          description: `Hi ${profile?.full_name || 'there'}! Your partnership application for ${profile?.company_name || 'your company'} has been successfully submitted and is currently being reviewed by our operations team. We typically review applications within 2-3 business days and will notify you via email once approved.`,
          badgeVariant: 'secondary' as const,
          badgeText: 'Under Review',
          showContact: true
        }
      case 'suspended':
        return {
          icon: XCircle,
          title: 'Account Suspended',
          description: 'Your account has been temporarily suspended. Please contact our support team for assistance.',
          badgeVariant: 'destructive' as const,
          badgeText: 'Suspended',
          showContact: true
        }
      case 'deleted':
        return {
          icon: UserX,
          title: 'Account Deactivated',
          description: 'Your account has been deactivated. If you believe this is an error, please contact support.',
          badgeVariant: 'destructive' as const,
          badgeText: 'Deactivated',
          showContact: true
        }
      case 'no_profile':
        return {
          icon: Clock,
          title: 'Complete Your Partner Application',
          description: 'Welcome to IBC Group! Your account has been created, but you need to complete your partner application to gain access to the dashboard. This includes providing your company information, communication preferences, and agreeing to our partnership terms.',
          badgeVariant: 'outline' as const,
          badgeText: 'Application Pending',
          showContact: true
        }
      case 'error':
        return {
          icon: XCircle,
          title: 'Access Error',
          description: 'Unable to verify your access status at this time. Please try again later or contact support.',
          badgeVariant: 'destructive' as const,
          badgeText: 'Error',
          showContact: true
        }
      default:
        return {
          icon: Clock,
          title: 'Access Pending',
          description: 'Please wait while we verify your access.',
          badgeVariant: 'outline' as const,
          badgeText: 'Pending',
          showContact: true
        }
    }
  }

  const config = getStatusConfig()
  const StatusIcon = config.icon

  return (
    <AppShell title="Access Pending">
      <div className="flex items-center justify-center min-h-[60vh]">
        <Card className="mx-auto max-w-2xl">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-16 h-16 bg-muted rounded-full flex items-center justify-center">
              <StatusIcon className="h-8 w-8 text-muted-foreground" />
            </div>
            <CardTitle className="text-2xl">{config.title}</CardTitle>
            <Badge variant={config.badgeVariant} className="mx-auto">
              {config.badgeText}
            </Badge>
          </CardHeader>
          <CardContent className="space-y-6 text-center">
            <p className="text-muted-foreground leading-relaxed">
              {config.description}
            </p>
            
            {profile?.created_at && (
              <div className="bg-muted/50 rounded-lg p-4">
                <p className="text-sm text-muted-foreground">
                  <strong>Application submitted:</strong> {new Date(profile.created_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              </div>
            )}
            
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              {status === 'no_profile' && (
                <Button asChild size="lg">
                  <Link href="/onboarding">Complete Partner Application</Link>
                </Button>
              )}
              {config.showContact && (
                <Button asChild variant="outline">
                  <Link href="/support">Contact Support</Link>
                </Button>
              )}
            </div>
            
            {status === 'pending' && (
              <div className="bg-blue-50 dark:bg-blue-950 rounded-lg p-4 text-sm">
                <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">What happens next?</h4>
                <ul className="text-left text-blue-700 dark:text-blue-300 space-y-1">
                  <li>• Our ops team reviews your application and company details</li>
                  <li>• We verify your partnership fit with our program</li>
                  <li>• You'll receive an email notification once approved</li>
                  <li>• Access to your partner dashboard will be granted immediately</li>
                </ul>
              </div>
            )}
            
            {status === 'no_profile' && (
              <div className="bg-green-50 dark:bg-green-950 rounded-lg p-4 text-sm">
                <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">Application takes 5 minutes</h4>
                <ul className="text-left text-green-700 dark:text-green-300 space-y-1">
                  <li>• Company information and partnership details</li>
                  <li>• Communication preferences (WhatsApp, Telegram, Email)</li>
                  <li>• Billing address and payment method</li>
                  <li>• Partnership terms and conditions agreement</li>
                </ul>
              </div>
            )}
            
            <div className="text-xs text-muted-foreground border-t pt-4">
              <p>Need immediate assistance? Email us at <strong><EMAIL></strong></p>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}