import Link from "next/link"
import { AppShell } from "@/app/components/app-shell"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"

export default function SupportPage() {
  return (
    <AppShell title="Support">
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Assigned Sales</CardTitle>
            <CardDescription>Reach out directly via your preferred channel.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center gap-3">
              <img src="/diverse-avatars.png" width={48} height={48} alt="Sales contact" className="rounded-full" />
              <div>
                <div className="font-medium"><PERSON></div>
                <div className="text-sm text-muted-foreground"><EMAIL></div>
              </div>
            </div>
            <div className="flex gap-2">
              <Button asChild>
                <a href="https://t.me/example" target="_blank" rel="noreferrer">
                  Telegram
                </a>
              </Button>
              <Button variant="secondary" asChild>
                <a href="https://wa.me/15551112222" target="_blank" rel="noreferrer">
                  WhatsApp
                </a>
              </Button>
              <Button variant="outline" asChild className="bg-transparent">
                <a href="mailto:<EMAIL>">Email</a>
              </Button>
            </div>
            <Button variant="outline" asChild className="bg-transparent">
              <Link href="https://calendly.com/" target="_blank">
                Book a call
              </Link>
            </Button>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Contact Form</CardTitle>
            <CardDescription>We’ll get back within 1 business day.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-1.5">
              <Label htmlFor="subject">Subject</Label>
              <Input id="subject" placeholder="How can we help?" />
            </div>
            <div className="space-y-1.5">
              <Label htmlFor="message">Message</Label>
              <Textarea id="message" placeholder="Tell us more..." />
            </div>
            <Button>Send</Button>
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
