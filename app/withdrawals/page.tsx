"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { AppShell } from "@/app/components/app-shell"
import { PageHeader } from "@/app/components/page-header"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Loader2 } from "lucide-react"
import { toast } from "sonner"

type WithdrawalRequest = {
  id: string
  amount: number
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  created_at: string
  transaction_reference?: string
}

export default function WithdrawalsList() {
  const [withdrawals, setWithdrawals] = useState<WithdrawalRequest[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchWithdrawals()
  }, [])

  const fetchWithdrawals = async () => {
    try {
      const response = await fetch('/api/withdrawals')
      if (!response.ok) {
        throw new Error('Failed to fetch withdrawals')
      }
      const result = await response.json()
      setWithdrawals(result.data || [])
    } catch (error) {
      console.error('Error fetching withdrawals:', error)
      toast.error('Failed to load withdrawal history')
    } finally {
      setLoading(false)
    }
  }

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'completed': return 'default'
      case 'pending': return 'outline'
      case 'processing': return 'secondary'
      case 'failed': return 'destructive'
      case 'cancelled': return 'destructive'
      default: return 'outline'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  return (
    <AppShell title="Withdrawals">
      <div className="flex flex-col gap-4">
        <PageHeader
          title="Withdrawals"
          subtitle="Track your payout requests."
          actions={
            <Button asChild>
              <Link href="/withdrawals/new">
                <Plus className="mr-2 h-4 w-4" /> New Withdrawal
              </Link>
            </Button>
          }
        />
        <Card>
          <CardHeader>
            <CardTitle>History</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Tx Ref</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {withdrawals.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                        No withdrawal requests found
                      </TableCell>
                    </TableRow>
                  ) : (
                    withdrawals.map((withdrawal) => (
                      <TableRow key={withdrawal.id}>
                        <TableCell>
                          <Link href={`/withdrawals/${withdrawal.id}`} className="underline">
                            {withdrawal.id}
                          </Link>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getStatusVariant(withdrawal.status)}>
                            {withdrawal.status.charAt(0).toUpperCase() + withdrawal.status.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>{formatDate(withdrawal.created_at)}</TableCell>
                        <TableCell className="font-mono">{withdrawal.transaction_reference || "-"}</TableCell>
                        <TableCell className="text-right">${withdrawal.amount.toFixed(2)}</TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
