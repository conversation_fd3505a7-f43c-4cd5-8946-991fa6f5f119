import Image from "next/image"
import { AppShell } from "@/app/components/app-shell"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

export default async function WithdrawalDetail({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params
  const id = resolvedParams.id
  const txRef = id === "w_1001" ? "0xabc123" : ""
  const status = id === "w_1001" ? "Paid" : "Pending"

  const timeline = [
    { at: "2025-07-01 10:00", text: "Created by partner" },
    { at: "2025-07-01 12:30", text: "Under review" },
    ...(txRef ? [{ at: "2025-07-02 09:15", text: "Paid, tx posted" }] : []),
  ]

  return (
    <AppShell title="Withdrawal Detail">
      <div className="mx-auto max-w-3xl w-full">
        <Card>
          <CardHeader>
            <CardTitle>Withdrawal {id}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center gap-4">
              <Badge variant={status === "Paid" ? "success" : "secondary"}>{status}</Badge>
              <div className="text-sm text-muted-foreground">Amount: $1,200.00</div>
              {txRef ? <div className="text-sm font-mono">tx_ref: {txRef}</div> : null}
            </div>
            <Separator />
            <div>
              <div className="text-sm font-semibold mb-2">Timeline</div>
              <ul className="space-y-2">
                {timeline.map((t) => (
                  <li key={t.at} className="text-sm flex gap-3">
                    <span className="text-muted-foreground w-36">{t.at}</span>
                    <span>{t.text}</span>
                  </li>
                ))}
              </ul>
            </div>
            <Separator />
            <div>
              <div className="text-sm font-semibold mb-2">Proof of Payment</div>
              {txRef ? (
                <Image src="/placeholder-anrzh.png" alt="Proof of payment" width={540} height={300} />
              ) : (
                <div className="text-sm text-muted-foreground">Not available yet.</div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
