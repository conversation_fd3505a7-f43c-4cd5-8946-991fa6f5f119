"use client"

import * as React from "react"
import { AppShell } from "@/app/components/app-shell"
import { PageHeader } from "@/app/components/page-header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useToast } from "@/hooks/use-toast"
import { InvoiceWizard } from "@/app/components/invoice-wizard"

const MIN_WITHDRAWAL = 100

export default function NewWithdrawalPage() {
  const { toast } = useToast()
  const [method, setMethod] = React.useState<"usdt" | "bank">("usdt")
  const [amount, setAmount] = React.useState<number | "">("")
  const isBelowMin = typeof amount === "number" ? amount < MIN_WITHDRAWAL : true
  const [showWizard, setShowWizard] = React.useState(false)

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (isBelowMin) return
    toast({ title: "Request created", description: "We will process your withdrawal shortly." })
  }

  return (
    <AppShell title="New Withdrawal">
      <div className="flex flex-col gap-4">
        <PageHeader title="Create Withdrawal" subtitle="Select a method and enter payout details." />
        <form onSubmit={onSubmit} className="grid gap-6 md:grid-cols-3">
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Withdrawal Details</CardTitle>
              <CardDescription>Choose your payout method and amount.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label>Method</Label>
                <RadioGroup
                  defaultValue="usdt"
                  onValueChange={(v) => setMethod(v as "usdt" | "bank")}
                  className="grid grid-cols-2 gap-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="usdt" id="usdt" />
                    <Label htmlFor="usdt">USDT (TRC20)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="bank" id="bank" />
                    <Label htmlFor="bank">Bank Transfer</Label>
                  </div>
                </RadioGroup>
              </div>
              <div className="space-y-2">
                <Label htmlFor="amount">Amount (USD)</Label>
                <Input
                  id="amount"
                  inputMode="decimal"
                  placeholder="0.00"
                  value={amount === "" ? "" : amount}
                  onChange={(e) => {
                    const v = e.target.value
                    setAmount(v === "" ? "" : Number(v))
                  }}
                />
              </div>
              {method === "usdt" ? (
                <div className="space-y-2">
                  <Label htmlFor="usdtAddress">USDT Address (TRC20)</Label>
                  <Input id="usdtAddress" placeholder="T..." />
                </div>
              ) : (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="bankName">Bank Name</Label>
                    <Input id="bankName" placeholder="Bank of Example" />
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="accountName">Account Name</Label>
                      <Input id="accountName" placeholder="Alex Doe" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="iban">IBAN / Account No.</Label>
                      <Input id="iban" placeholder="CH93 0076 2011 6238 5295 7" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bankNotes">Notes (optional)</Label>
                    <Textarea id="bankNotes" placeholder="Payment reference, additional info..." />
                  </div>
                </>
              )}
              <div className="flex flex-wrap items-center gap-3">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button type="submit" disabled={isBelowMin}>
                        Create Request
                      </Button>
                    </TooltipTrigger>
                    {isBelowMin && (
                      <TooltipContent>
                        Minimum withdrawal is ${MIN_WITHDRAWAL}. See Earnings to increase balance.
                      </TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>
                <Button type="button" variant="secondary" onClick={() => setShowWizard(true)}>
                  Open Invoice Wizard
                </Button>
                <span className="text-xs text-muted-foreground">No invoice data is stored or transmitted.</span>
              </div>
            </CardContent>
            <CardFooter className="justify-between sticky bottom-0 bg-background/60 backdrop-blur supports-[backdrop-filter]:bg-background/50">
              <span className="text-xs text-muted-foreground">Preview and confirm before submitting.</span>
            </CardFooter>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Preview</CardTitle>
              <CardDescription>Summary sent to Accounting (no sensitive data).</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Method</span>
                <span className="font-medium uppercase">{method}</span>
              </div>
              <div className="flex justify-between">
                <span>Amount</span>
                <span className="font-medium">${amount || 0}</span>
              </div>
              <div className="flex justify-between">
                <span>Reference</span>
                <span className="font-mono">auto</span>
              </div>
            </CardContent>
          </Card>
        </form>
      </div>

      <InvoiceWizard open={showWizard} onOpenChange={setShowWizard} />
    </AppShell>
  )
}
