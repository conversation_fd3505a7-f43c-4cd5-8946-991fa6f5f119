import { NextRequest, NextResponse } from 'next/server'
import { 
  parseAttributionCookies, 
  getPrimaryAttribution, 
  trackAttribution 
} from '@/lib/services/attribution'
import { log } from '@/lib/log'
import crypto from 'crypto'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      sessionId: providedSessionId,
      event = 'page_visit',
      metadata = {}
    } = body

    // Generate session ID if not provided
    const sessionId = providedSessionId || crypto.randomUUID()

    // Parse attribution cookies
    const cookieHeader = request.headers.get('cookie')
    const attributionCookies = parseAttributionCookies(cookieHeader || undefined)
    
    // Determine primary attribution
    const primaryAttribution = getPrimaryAttribution(attributionCookies)

    // Extract metadata from request
    const trackingMetadata = {
      userAgent: request.headers.get('user-agent') || undefined,
      ipAddress: request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() || 
                 request.headers.get('x-real-ip') || 
                 'unknown',
      utmSource: metadata.utm_source,
      utmMedium: metadata.utm_medium,
      utmCampaign: metadata.utm_campaign,
      utmTerm: metadata.utm_term,
      utmContent: metadata.utm_content,
    }

    // Track attribution
    await trackAttribution(sessionId, primaryAttribution, trackingMetadata)

    // Return tracking info
    return NextResponse.json({
      success: true,
      sessionId,
      attribution: primaryAttribution ? {
        type: primaryAttribution.type,
        slug: primaryAttribution.slug,
        name: primaryAttribution.type === 'partner' 
          ? primaryAttribution.partnerName 
          : primaryAttribution.salesPersonName
      } : null
    })

  } catch (error) {
    log.error('Failed to track attribution', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Parse attribution cookies and return current attribution status
    const cookieHeader = request.headers.get('cookie')
    const attributionCookies = parseAttributionCookies(cookieHeader || undefined)
    const primaryAttribution = getPrimaryAttribution(attributionCookies)

    return NextResponse.json({
      success: true,
      attribution: primaryAttribution ? {
        type: primaryAttribution.type,
        slug: primaryAttribution.slug,
        name: primaryAttribution.type === 'partner' 
          ? primaryAttribution.partnerName 
          : primaryAttribution.salesPersonName,
        timestamp: primaryAttribution.timestamp
      } : null,
      cookies: {
        hasPartner: !!attributionCookies.partner,
        hasSales: !!attributionCookies.sales,
      }
    })

  } catch (error) {
    log.error('Failed to get attribution status', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}