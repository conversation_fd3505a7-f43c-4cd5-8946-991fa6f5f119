import { NextRequest, NextResponse } from 'next/server'
import { markAttributionConverted } from '@/lib/services/attribution'
import { log } from '@/lib/log'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { sessionId, leadId } = body

    if (!sessionId || !leadId) {
      return NextResponse.json(
        { error: 'Both sessionId and leadId are required' },
        { status: 400 }
      )
    }

    // Mark attribution as converted
    await markAttributionConverted(sessionId, leadId)

    return NextResponse.json({
      success: true,
      message: 'Attribution marked as converted'
    })

  } catch (error) {
    log.error('Failed to mark attribution as converted', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}