import { NextRequest, NextResponse } from 'next/server'
import { auth, currentUser } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user email from Clerk
    const user = await currentUser()
    const email = user?.emailAddresses?.[0]?.emailAddress

    if (!email) {
      return NextResponse.json({ error: 'Email not found' }, { status: 400 })
    }

    // Use service role to get user profile
    const supabase = await createClient(true)
    
    // First try to get from partners_users (internal staff)
    const { data: internalUser, error: internalError } = await supabase
      .from('partners_users')
      .select('id, email, display_name, role')
      .eq('email', email)
      .single()

    if (internalUser) {
      return NextResponse.json({
        id: internalUser.id,
        email: internalUser.email,
        display_name: internalUser.display_name,
        role: internalUser.role,
        user_type: 'internal'
      })
    }

    // If not internal user, check if they're a partner
    const { data: partnerUser, error: partnerError } = await supabase
      .from('partners_profiles')
      .select('id, email, full_name, status, tier')
      .eq('id', userId)
      .single()

    if (partnerUser) {
      return NextResponse.json({
        id: partnerUser.id,
        email: partnerUser.email,
        display_name: partnerUser.full_name,
        role: 'partner',
        status: partnerUser.status,
        tier: partnerUser.tier,
        user_type: 'partner'
      })
    }

    // User not found in either table
    return NextResponse.json({ error: 'User profile not found' }, { status: 404 })

  } catch (error) {
    console.error('Error in user profile GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}