import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { requireRole } from '@/lib/rbac'
import { log } from '@/lib/log'
import { z } from 'zod'

// Export query parameters schema
const exportQuerySchema = z.object({
  format: z.enum(['csv', 'json']).default('csv'),
  status: z.enum(['pending', 'confirmed', 'paid', 'disputed', 'all']).default('all'),
  partner_id: z.string().uuid().optional(),
  date_from: z.string().datetime().optional(),
  date_to: z.string().datetime().optional()
})

export async function GET(request: NextRequest) {
  try {
    // Check permissions - only accounting and super_admin can export deals
    await requireRole(['accounting', 'super_admin'])
    
    const searchParams = request.nextUrl.searchParams
    const queryParams = {
      format: searchParams.get('format') || 'csv',
      status: searchParams.get('status') || 'all',
      partner_id: searchParams.get('partner_id') || undefined,
      date_from: searchParams.get('date_from') || undefined,
      date_to: searchParams.get('date_to') || undefined
    }
    
    const validation = exportQuerySchema.safeParse(queryParams)
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Invalid query parameters',
        details: validation.error.issues 
      }, { status: 400 })
    }
    
    const { format, status, partner_id, date_from, date_to } = validation.data
    
    const client = await createClient()
    let query = client
      .from('partners_deals')
      .select(`
        id,
        client_company,
        client_poc,
        deal_value,
        commission_rate,
        commission_amount,
        status,
        invoice_required,
        invoice_submitted,
        payment_date,
        notes,
        created_at,
        updated_at,
        partners_profiles!partner_id (
          full_name,
          company_name,
          email,
          tier
        ),
        partners_leads!lead_id (
          company_name,
          status
        )
      `)
    
    // Apply filters
    if (status !== 'all') {
      query = query.eq('status', status)
    }
    
    if (partner_id) {
      query = query.eq('partner_id', partner_id)
    }
    
    if (date_from) {
      query = query.gte('created_at', date_from)
    }
    
    if (date_to) {
      query = query.lte('created_at', date_to)
    }
    
    const { data: deals, error } = await query.order('created_at', { ascending: false })
    
    if (error) {
      log.error('Failed to export deals', { error: error.message })
      return NextResponse.json({ error: 'Failed to export data' }, { status: 500 })
    }
    
    if (format === 'json') {
      const filename = `deals_export_${new Date().toISOString().split('T')[0]}.json`
      
      return new NextResponse(JSON.stringify(deals, null, 2), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Cache-Control': 'no-store'
        }
      })
    }
    
    // Generate CSV
    if (!deals || deals.length === 0) {
      return NextResponse.json({ error: 'No data to export' }, { status: 404 })
    }
    
    const headers = [
      'Deal ID', 'Partner Name', 'Partner Company', 'Partner Email', 'Partner Tier',
      'Client Company', 'Client POC Name', 'Client POC Email', 
      'Deal Value', 'Commission Rate', 'Commission Amount', 'Status',
      'Invoice Required', 'Invoice Submitted', 'Payment Date',
      'Lead Company', 'Lead Status', 'Notes', 'Created At', 'Updated At'
    ]
    
    const csvRows = [headers.join(',')]
    
    deals.forEach(deal => {
      const partner = Array.isArray(deal.partners_profiles) ? deal.partners_profiles[0] : deal.partners_profiles
      const lead = Array.isArray(deal.partners_leads) ? deal.partners_leads[0] : deal.partners_leads
      const clientPoc = typeof deal.client_poc === 'string' 
        ? JSON.parse(deal.client_poc) 
        : deal.client_poc
      
      const row = [
        deal.id || '',
        `"${partner?.full_name || ''}"`,
        `"${partner?.company_name || ''}"`,
        partner?.email || '',
        partner?.tier || '',
        `"${deal.client_company || ''}"`,
        `"${clientPoc?.name || ''}"`,
        clientPoc?.email || '',
        deal.deal_value || '0',
        deal.commission_rate || '0',
        deal.commission_amount || '0',
        deal.status || '',
        deal.invoice_required ? 'Yes' : 'No',
        deal.invoice_submitted ? 'Yes' : 'No',
        deal.payment_date || '',
        `"${lead?.company_name || ''}"`,
        lead?.status || '',
        `"${deal.notes || ''}"`,
        deal.created_at || '',
        deal.updated_at || ''
      ]
      
      csvRows.push(row.join(','))
    })
    
    const csvContent = csvRows.join('\n')
    const filename = `deals_export_${new Date().toISOString().split('T')[0]}.csv`
    
    log.info('Deals export completed', { 
      metadata: { recordCount: deals.length, format, filters: { status, partner_id, date_from, date_to } }
    })
    
    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv; charset=utf-8',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-store'
      }
    })
    
  } catch (error) {
    log.error('Deals export error', { error })
    
    if (error instanceof Error && error.message.includes('Access denied')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}