import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { requireRole } from '@/lib/rbac'
import { z } from 'zod'

const bulkReassignSchema = z.object({
  fromSalesUserId: z.string().uuid('Invalid from sales user ID'),
  toSalesUserId: z.string().uuid('Invalid to sales user ID')
})

export async function POST(request: NextRequest) {
  try {
    // Verify user has required permissions
    await requireRole(['ops', 'super_admin'])
    
    const body = await request.json()
    const { fromSalesUserId, toSalesUserId } = bulkReassignSchema.parse(body)
    
    if (fromSalesUserId === toSalesUserId) {
      return NextResponse.json(
        { error: 'From and To sales users cannot be the same' },
        { status: 400 }
      )
    }
    
    const supabase = await createClient(true)
    
    // Verify both sales users exist and have sales role
    const { data: salesUsers, error: salesError } = await supabase
      .from('partners_users')
      .select('id, role, display_name')
      .in('id', [fromSalesUserId, toSalesUserId])
      .eq('role', 'sales')
    
    if (salesError || !salesUsers || salesUsers.length !== 2) {
      return NextResponse.json(
        { error: 'Invalid sales user IDs or users are not sales representatives' },
        { status: 400 }
      )
    }
    
    // Get all assignments for the "from" sales user
    const { data: assignments, error: assignmentsError } = await supabase
      .from('partners_teams_assignments')
      .select('id, partner_id')
      .eq('sales_user_id', fromSalesUserId)
    
    if (assignmentsError) {
      console.error('Error fetching assignments:', assignmentsError)
      return NextResponse.json(
        { error: 'Failed to fetch current assignments' },
        { status: 500 }
      )
    }
    
    if (!assignments || assignments.length === 0) {
      return NextResponse.json({
        success: true,
        updated: 0,
        message: 'No partners assigned to the selected sales representative'
      })
    }
    
    // Update all assignments to the new sales user
    const { error: updateError } = await supabase
      .from('partners_teams_assignments')
      .update({
        sales_user_id: toSalesUserId,
        assigned_by: null,
        updated_at: new Date().toISOString()
      })
      .eq('sales_user_id', fromSalesUserId)
    
    if (updateError) {
      console.error('Error updating assignments:', updateError)
      return NextResponse.json(
        { error: 'Failed to reassign partners' },
        { status: 500 }
      )
    }
    
    const fromUser = salesUsers.find(u => u.id === fromSalesUserId)
    const toUser = salesUsers.find(u => u.id === toSalesUserId)
    
    return NextResponse.json({
      success: true,
      updated: assignments.length,
      message: `Successfully reassigned ${assignments.length} partners from ${fromUser?.display_name} to ${toUser?.display_name}`
    })
    
  } catch (error) {
    console.error('Bulk reassignment error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.issues },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}