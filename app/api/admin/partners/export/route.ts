import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { requirePermission, requireRole } from '@/lib/rbac'
import { log } from '@/lib/log'
import { z } from 'zod'

// Export query parameters schema
const exportQuerySchema = z.object({
  format: z.enum(['csv', 'json']).default('csv'),
  tier: z.enum(['trusted', 'elite', 'diamond', 'all']).default('all'),
  status: z.enum(['active', 'pending', 'suspended', 'all']).default('all'),
  include_billing: z.enum(['true', 'false']).default('false').transform(val => val === 'true')
})

export async function GET(request: NextRequest) {
  try {
    // Check permissions - only ops, accounting, and super_admin can export
    await requireRole(['ops', 'accounting', 'super_admin'])
    
    const searchParams = request.nextUrl.searchParams
    const queryParams = {
      format: searchParams.get('format') || 'csv',
      tier: searchParams.get('tier') || 'all',
      status: searchParams.get('status') || 'all',
      include_billing: searchParams.get('include_billing') || 'false'
    }
    
    const validation = exportQuerySchema.safeParse(queryParams)
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Invalid query parameters',
        details: validation.error.issues 
      }, { status: 400 })
    }
    
    const { format, tier, status, include_billing } = validation.data
    
    const client = await createClient()
    
    // Build select query dynamically
    const baseFields = [
      'id',
      'email', 
      'full_name',
      'company_name',
      'company_type',
      'role',
      'tier',
      'status',
      'telegram',
      'whatsapp',
      'x_profile',
      'referral_slug',
      'internal_poc',
      'created_at',
      'updated_at'
    ]
    
    if (include_billing) {
      baseFields.splice(-2, 0, 'billing_address')
    }
    
    let query = client
      .from('partners_profiles')
      .select(baseFields.join(', '))
    
    // Apply filters
    if (tier !== 'all') {
      query = query.eq('tier', tier)
    }
    
    if (status !== 'all') {
      query = query.eq('status', status)
    }
    
    const { data: partners, error } = await query.order('created_at', { ascending: false })
    
    if (error) {
      log.error('Failed to export partners', { 
        error: error.message,
        metadata: { tier, status, include_billing }
      })
      return NextResponse.json({ error: 'Failed to export data' }, { status: 500 })
    }
    
    if (!partners) {
      return NextResponse.json({ error: 'No data found' }, { status: 404 })
    }
    
    if (format === 'json') {
      const filename = `partners_export_${new Date().toISOString().split('T')[0]}.json`
      
      return new NextResponse(JSON.stringify(partners, null, 2), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Cache-Control': 'no-store'
        }
      })
    }
    
    // Generate CSV
    if (partners.length === 0) {
      return NextResponse.json({ error: 'No data to export' }, { status: 404 })
    }
    
    const headers = [
      'ID', 'Email', 'Full Name', 'Company Name', 'Company Type', 'Role',
      'Tier', 'Status', 'Telegram', 'WhatsApp', 'X Profile', 'Referral Slug',
      'Internal POC', 'Created At', 'Updated At'
    ]
    
    if (include_billing) {
      headers.splice(-2, 0, 'Billing Address')
    }
    
    const csvRows = [headers.join(',')]
    
    partners.forEach((partner: any) => {
      const row = [
        partner.id || '',
        `"${partner.email || ''}"`,
        `"${partner.full_name || ''}"`,
        `"${partner.company_name || ''}"`,
        partner.company_type || '',
        `"${partner.role || ''}"`,
        partner.tier || '',
        partner.status || '',
        partner.telegram || '',
        partner.whatsapp || '',
        partner.x_profile || '',
        partner.referral_slug || '',
        `"${partner.internal_poc || ''}"`,
        partner.created_at || '',
        partner.updated_at || ''
      ]
      
      if (include_billing && partner.billing_address) {
        try {
          const billing = typeof partner.billing_address === 'string' 
            ? JSON.parse(partner.billing_address)
            : partner.billing_address
          
          const billingString = Object.entries(billing)
            .map(([key, value]) => `${key}: ${value}`)
            .join('; ')
          row.splice(-2, 0, `"${billingString}"`)
        } catch (e) {
          row.splice(-2, 0, `"${partner.billing_address}"`)
        }
      } else if (include_billing) {
        row.splice(-2, 0, '""')
      }
      
      csvRows.push(row.join(','))
    })
    
    const csvContent = csvRows.join('\n')
    const filename = `partners_export_${new Date().toISOString().split('T')[0]}.csv`
    
    log.info('Partners export completed', { 
      metadata: {
        recordCount: partners.length,
        format,
        filters: { tier, status, include_billing }
      }
    })
    
    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv; charset=utf-8',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-store'
      }
    })
    
  } catch (error) {
    log.error('Partners export error', { error })
    
    if (error instanceof Error && error.message.includes('Access denied')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}