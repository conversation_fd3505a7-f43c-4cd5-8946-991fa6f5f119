import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { requireRole } from '@/lib/rbac'
import { z } from 'zod'

const assignSalesSchema = z.object({
  partnerId: z.string().uuid('Invalid partner ID'),
  salesUserId: z.string().uuid('Invalid sales user ID').nullable()
})

export async function POST(request: NextRequest) {
  try {
    // Verify user has required permissions
    await requireRole(['ops', 'super_admin'])
    
    const body = await request.json()
    const { partnerId, salesUserId } = assignSalesSchema.parse(body)
    
    const supabase = await createClient(true)
    
    if (salesUserId) {
      // Verify sales user exists and has sales role
      const { data: salesUser, error: salesError } = await supabase
        .from('partners_users')
        .select('id, role')
        .eq('id', salesUserId)
        .eq('role', 'sales')
        .single()
      
      if (salesError || !salesUser) {
        return NextResponse.json(
          { error: 'Invalid sales user ID or user is not a sales representative' },
          { status: 400 }
        )
      }
    }
    
    // Check if assignment already exists
    const { data: existingAssignment, error: checkError } = await supabase
      .from('partners_teams_assignments')
      .select('id')
      .eq('partner_id', partnerId)
      .single()
    
    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking existing assignment:', checkError)
      return NextResponse.json(
        { error: 'Failed to check existing assignment' },
        { status: 500 }
      )
    }
    
    if (salesUserId === null) {
      // Remove assignment (unassign)
      if (existingAssignment) {
        const { error: deleteError } = await supabase
          .from('partners_teams_assignments')
          .delete()
          .eq('partner_id', partnerId)
        
        if (deleteError) {
          console.error('Error removing assignment:', deleteError)
          return NextResponse.json(
            { error: 'Failed to remove sales assignment' },
            { status: 500 }
          )
        }
      }
    } else {
      // Create or update assignment
      if (existingAssignment) {
        // Update existing assignment
        const { error: updateError } = await supabase
          .from('partners_teams_assignments')
          .update({
            sales_user_id: salesUserId,
            updated_at: new Date().toISOString()
          })
          .eq('partner_id', partnerId)
        
        if (updateError) {
          console.error('Error updating assignment:', updateError)
          return NextResponse.json(
            { error: 'Failed to update sales assignment' },
            { status: 500 }
          )
        }
      } else {
        // Create new assignment
        const { error: insertError } = await supabase
          .from('partners_teams_assignments')
          .insert({
            partner_id: partnerId,
            sales_user_id: salesUserId,
            assigned_by: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
        
        if (insertError) {
          console.error('Error creating assignment:', insertError)
          return NextResponse.json(
            { error: 'Failed to create sales assignment' },
            { status: 500 }
          )
        }
      }
    }
    
    return NextResponse.json({
      success: true,
      message: salesUserId ? 'Sales assignment updated successfully' : 'Sales assignment removed successfully'
    })
    
  } catch (error) {
    console.error('Sales assignment error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.issues },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}