import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { partnerStatusUpdateSchema, tierUpdateSchema } from '@/lib/validations'
import { requireRole, getUserContext } from '@/lib/rbac'
import { log } from '@/lib/log'

export async function GET(request: NextRequest) {
  try {
    // Allow sales users but with restricted access
    await requireRole(['sales', 'ops', 'accounting', 'super_admin'])
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const tier = searchParams.get('tier')

    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const client = await createClient()
    
    // Get user role and email for filtering
    const userContext = await getUserContext()
    
    // Build query
    let query = client
      .from('partners_profiles')
      .select(`
        *,
        partners_teams_assignments!left(
          sales_user_id,
          ops_user_id,
          accounting_user_id,
          partners_users!sales_user_id(display_name, email)
        )
      `)

    // Apply role-based filtering
    if (userContext && userContext.role === 'sales') {
      // Sales users only see partners assigned to them
      query = query.eq('partners_teams_assignments.sales_user_id', userContext.userId)
    }

    // Apply filters
    if (search) {
      query = query.or(`
        full_name.ilike.%${search}%,
        company_name.ilike.%${search}%,
        email.ilike.%${search}%
      `)
    }
    if (status) {
      query = query.eq('status', status)
    }
    if (tier) {
      query = query.eq('tier', tier)
    }

    // Get total count
    const { count } = await client
      .from('partners_profiles')
      .select('*', { count: 'exact', head: true })

    // Apply pagination
    const offset = (page - 1) * limit
    query = query
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false })

    const { data: partners, error } = await query

    if (error) {
      log.error('Failed to fetch partners', { userId, error: error.message })
      return NextResponse.json({ error: 'Failed to fetch partners' }, { status: 500 })
    }

    const pagination = {
      page,
      limit,
      total: count || 0,
      pages: Math.ceil((count || 0) / limit)
    }

    log.info('Admin partners fetched', { 
      userId, 
      metadata: { count: partners?.length || 0 } 
    })
    return NextResponse.json({ data: partners, pagination })

  } catch (error) {
    log.error('Admin partners GET error', { error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, ...data } = body

    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const client = await createClient()

    if (action === 'update_status') {
      await requireRole(['ops', 'super_admin'])
      
      const validation = partnerStatusUpdateSchema.safeParse(data)
      if (!validation.success) {
        return NextResponse.json({ 
          error: 'Validation failed',
          details: validation.error.issues 
        }, { status: 400 })
      }

      // Get current partner for audit
      const { data: currentPartner } = await client
        .from('partners_profiles')
        .select('*')
        .eq('id', validation.data.partner_id)
        .single()

      if (!currentPartner) {
        return NextResponse.json({ error: 'Partner not found' }, { status: 404 })
      }

      // Update status
      const { data: updatedPartner, error } = await client
        .from('partners_profiles')
        .update({ 
          status: validation.data.status,
          updated_at: new Date().toISOString()
        })
        .eq('id', validation.data.partner_id)
        .select()
        .single()

      if (error) {
        log.error('Failed to update partner status', { 
          userId, 
          error: error.message,
          metadata: { partnerId: validation.data.partner_id }
        })
        return NextResponse.json({ error: 'Failed to update status' }, { status: 500 })
      }

      // Log audit event
      await client.rpc('log_audit_event', {
        p_user_id: userId,
        p_user_type: 'internal',
        p_action: 'update_partner_status',
        p_resource_type: 'profile',
        p_resource_id: validation.data.partner_id,
        p_old_values: { status: currentPartner.status },
        p_new_values: { status: validation.data.status, reason: validation.data.reason }
      })

      // Send notification if requested
      if (validation.data.notify_partner) {
        await client.from('partners_notifications').insert({
          recipient_id: validation.data.partner_id,
          notification_type: 'system_alert',
          title: `Account Status Updated`,
          message: `Your account status has been changed to ${validation.data.status}. ${validation.data.reason}`
        })
      }

      log.info('Partner status updated', { 
        userId, 
        metadata: {
          partnerId: validation.data.partner_id,
          oldStatus: currentPartner.status,
          newStatus: validation.data.status
        }
      })

      return NextResponse.json({ data: updatedPartner })

    } else if (action === 'update_tier') {
      await requireRole(['ops', 'super_admin'])
      
      const validation = tierUpdateSchema.safeParse(data)
      if (!validation.success) {
        return NextResponse.json({ 
          error: 'Validation failed',
          details: validation.error.issues 
        }, { status: 400 })
      }

      // Get current partner for audit
      const { data: currentPartner } = await client
        .from('partners_profiles')
        .select('*')
        .eq('id', validation.data.partner_id)
        .single()

      if (!currentPartner) {
        return NextResponse.json({ error: 'Partner not found' }, { status: 404 })
      }

      // Update tier
      const { data: updatedPartner, error } = await client
        .from('partners_profiles')
        .update({ 
          tier: validation.data.new_tier,
          updated_at: new Date().toISOString()
        })
        .eq('id', validation.data.partner_id)
        .select()
        .single()

      if (error) {
        log.error('Failed to update partner tier', { 
          userId, 
          error: error.message,
          metadata: { partnerId: validation.data.partner_id }
        })
        return NextResponse.json({ error: 'Failed to update tier' }, { status: 500 })
      }

      // Log audit event
      await client.rpc('log_audit_event', {
        p_user_id: userId,
        p_user_type: 'internal',
        p_action: 'update_partner_tier',
        p_resource_type: 'profile',
        p_resource_id: validation.data.partner_id,
        p_old_values: { tier: currentPartner.tier },
        p_new_values: { tier: validation.data.new_tier, reason: validation.data.reason }
      })

      // Send notification if requested
      if (validation.data.notify_partner) {
        await client.from('partners_notifications').insert({
          recipient_id: validation.data.partner_id,
          notification_type: 'tier_upgrade',
          title: `Tier Updated to ${validation.data.new_tier}`,
          message: `Congratulations! Your tier has been upgraded to ${validation.data.new_tier}. ${validation.data.reason}`
        })
      }

      log.info('Partner tier updated', { 
        userId, 
        metadata: {
          partnerId: validation.data.partner_id,
          oldTier: currentPartner.tier,
          newTier: validation.data.new_tier
        }
      })

      return NextResponse.json({ data: updatedPartner })

    } else {
      return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

  } catch (error) {
    log.error('Admin partners PUT error', { error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}