import { NextRequest, NextResponse } from "next/server"
import { auth, currentUser } from "@clerk/nextjs/server"
import { createClient } from "@/lib/supabase/server"
import { applicationNotificationService } from "@/lib/email/application-notifications"
import { z } from "zod"

// Validation schemas
const updateApplicationSchema = z.object({
  application_id: z.string().uuid(),
  status: z.enum(['pending', 'under_review', 'approved', 'rejected', 'withdrawn']),
  review_notes: z.string().optional(),
  rejection_reason: z.string().optional(),
})

// GET /api/admin/applications - List all applications (with filters)
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Use service role for admin operations to bypass RLS
    const supabase = await createClient(true)
    
    // Get user email and check permissions
    const authResult = await auth()
    let email = authResult.sessionClaims?.email as string

    // If not in session claims, try currentUser as fallback
    if (!email) {
      try {
        const user = await currentUser()
        email = user?.emailAddresses?.[0]?.emailAddress || ''
      } catch (userError) {
        console.error('Error getting current user in applications API:', userError)
      }
    }

    if (!email) {
      return NextResponse.json({ error: "Email not found" }, { status: 400 })
    }

    // Check if user has ops/super_admin permissions
    const { data: userData, error: userError } = await supabase
      .from("partners_users")
      .select("role")
      .eq("email", email)
      .single()

    if (userError || !userData || !['ops', 'super_admin'].includes(userData.role)) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 })
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const status = searchParams.get('status')
    const priority = searchParams.get('priority')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Build query
    let query = supabase
      .from('partners_applications')
      .select('*')

    if (status) {
      query = query.eq('status', status)
    }

    if (priority) {
      query = query.eq('priority', parseInt(priority))
    }

    // Execute query with pagination
    const { data: applications, error } = await query
      .order('priority', { ascending: false })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error("Error fetching applications:", error)
      return NextResponse.json({ error: "Failed to fetch applications" }, { status: 500 })
    }

    return NextResponse.json({ 
      data: applications || [],
      pagination: {
        limit,
        offset,
        total: applications?.length || 0
      }
    })

  } catch (error) {
    console.error("Error in applications GET:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// PUT /api/admin/applications - Update application status
export async function PUT(request: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Use service role for admin operations to bypass RLS
    const supabase = await createClient(true)
    
    // Get user email and check permissions
    const authResult = await auth()
    let email = authResult.sessionClaims?.email as string

    // If not in session claims, try currentUser as fallback
    if (!email) {
      try {
        const user = await currentUser()
        email = user?.emailAddresses?.[0]?.emailAddress || ''
      } catch (userError) {
        console.error('Error getting current user in applications API:', userError)
      }
    }

    if (!email) {
      return NextResponse.json({ error: "Email not found" }, { status: 400 })
    }

    // Check if user has ops/super_admin permissions
    const { data: userData, error: userError } = await supabase
      .from("partners_users")
      .select("role")
      .eq("email", email)
      .single()

    if (userError || !userData || !['ops', 'super_admin'].includes(userData.role)) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 })
    }

    // Parse and validate request body
    const body = await request.json()
    const validation = updateApplicationSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json({ 
        error: "Invalid request data",
        details: validation.error.issues 
      }, { status: 400 })
    }

    const { application_id, status, review_notes, rejection_reason } = validation.data

    // Get the current application
    const { data: currentApp, error: fetchError } = await supabase
      .from("partners_applications")
      .select("*")
      .eq("id", application_id)
      .single()

    if (fetchError || !currentApp) {
      return NextResponse.json({ error: "Application not found" }, { status: 404 })
    }

    // Update the application
    const updateData: any = {
      status,
      reviewed_by: email,
      reviewed_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    if (review_notes) {
      updateData.review_notes = review_notes
    }

    if (rejection_reason && status === 'rejected') {
      updateData.rejection_reason = rejection_reason
    }

    const { data: updatedApp, error: updateError } = await supabase
      .from("partners_applications")
      .update(updateData)
      .eq("id", application_id)
      .select()
      .single()

    if (updateError) {
      console.error("ADMIN APPLICATIONS PUT: Error updating application", updateError)
      return NextResponse.json({ error: "Failed to update application" }, { status: 500 })
    }

    // If approved, activate the partner profile (so middleware stops redirecting)
    if (status === 'approved') {
      console.log('ADMIN APPLICATIONS PUT: Application approved, attempting to activate partner profile', { application_id, applicantEmail: currentApp.email })
      try {
        // Find partner profile by email
        const { data: profile, error: findProfileError } = await supabase
          .from('partners_profiles')
          .select('id, status, email')
          .eq('email', currentApp.email)
          .maybeSingle()

        if (findProfileError) {
          console.error('ADMIN APPLICATIONS PUT: Failed to find profile by email', { application_id, email: currentApp.email, error: findProfileError })
        } else if (!profile) {
          console.warn('ADMIN APPLICATIONS PUT: No partners_profiles row found for approved application email. User will still be redirected until profile exists.', { application_id, email: currentApp.email })
        } else if (profile.status !== 'active') {
          const { error: activateError } = await supabase
            .from('partners_profiles')
            .update({ status: 'active', updated_at: new Date().toISOString() })
            .eq('id', profile.id)

          if (activateError) {
            console.error('ADMIN APPLICATIONS PUT: Failed to activate partner profile', { application_id, partnerId: profile.id, error: activateError })
          } else {
            console.log('ADMIN APPLICATIONS PUT: Partner profile activated successfully', { application_id, partnerId: profile.id })
          }
        } else {
          console.log('ADMIN APPLICATIONS PUT: Partner profile already active', { application_id, partnerId: profile.id })
        }

        // Ensure active referral link exists for this partner
        if (profile) {
          console.log('ADMIN APPLICATIONS PUT: Ensuring active referral link exists', { partnerId: profile.id })
          try {
            const { data: existingActive } = await supabase
              .from('partners_referral_links')
              .select('id, slug, active')
              .eq('partner_id', profile.id)
              .eq('active', true)
              .maybeSingle()

            if (existingActive) {
              console.log('ADMIN APPLICATIONS PUT: Active referral link already present', { partnerId: profile.id, slug: existingActive.slug })
            } else {
              // Build a base slug from company_name/full_name/email local part
              const { data: fullProfile } = await supabase
                .from('partners_profiles')
                .select('company_name, full_name, email, referral_slug')
                .eq('id', profile.id)
                .single()

              const baseRaw = fullProfile?.referral_slug
                || fullProfile?.company_name
                || fullProfile?.full_name
                || (fullProfile?.email ? fullProfile.email.split('@')[0] : 'partner')

              const base = String(baseRaw)
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/^-|-$/g, '')
                .slice(0, 20) || 'partner'

              // Find unique slug
              let slug = base
              let counter = 1
              while (true) {
                const { data: slugExists } = await supabase
                  .from('partners_referral_links')
                  .select('id')
                  .eq('slug', slug)
                  .maybeSingle()
                if (!slugExists) break
                slug = `${base}-${counter++}`
              }

              const { error: createLinkError } = await supabase
                .from('partners_referral_links')
                .insert({
                  partner_id: profile.id,
                  slug,
                  active: true,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                })

              if (createLinkError) {
                console.error('ADMIN APPLICATIONS PUT: Failed to create referral link', { partnerId: profile.id, error: createLinkError })
              } else {
                console.log('ADMIN APPLICATIONS PUT: ✅ Referral link created', { partnerId: profile.id, slug })
              }
            }
          } catch (linkError) {
            console.error('ADMIN APPLICATIONS PUT: Exception ensuring referral link', { partnerId: profile.id, error: linkError })
          }
        }
      } catch (activationException) {
        console.error('ADMIN APPLICATIONS PUT: Exception activating partner profile', { application_id, error: activationException })
      }
    }

    // Send notifications for status changes
    if (status !== currentApp.status) {
      try {
        await applicationNotificationService.sendApplicationNotifications(
          application_id,
          status,
          currentApp.status,
          email
        )
      } catch (notificationError) {
        console.error("ADMIN APPLICATIONS PUT: Error sending notifications", notificationError)
        // Don't fail the main operation for notification errors
      }
    }

    return NextResponse.json({
      success: true,
      application: updatedApp,
      message: `Application ${status} successfully`
    })

  } catch (error) {
    console.error("Error in applications PUT:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// POST /api/admin/applications - Create new application (for testing or manual entry)
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Use service role for admin operations to bypass RLS
    const supabase = await createClient(true)
    
    // Get user email and check permissions
    const authResult = await auth()
    let email = authResult.sessionClaims?.email as string

    // If not in session claims, try currentUser as fallback
    if (!email) {
      try {
        const user = await currentUser()
        email = user?.emailAddresses?.[0]?.emailAddress || ''
      } catch (userError) {
        console.error('Error getting current user in applications API:', userError)
      }
    }

    if (!email) {
      return NextResponse.json({ error: "Email not found" }, { status: 400 })
    }

    // Check if user has ops/super_admin permissions
    const { data: userData, error: userError } = await supabase
      .from("partners_users")
      .select("role")
      .eq("email", email)
      .single()

    if (userError || !userData || !['ops', 'super_admin'].includes(userData.role)) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 })
    }

    const body = await request.json()

    // Insert new application
    const { data: newApp, error } = await supabase
      .from("partners_applications")
      .insert({
        ...body,
        status: body.status || 'pending',
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error("Error creating application:", error)
      return NextResponse.json({ error: "Failed to create application" }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      application: newApp,
      message: "Application created successfully"
    })

  } catch (error) {
    console.error("Error in applications POST:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}