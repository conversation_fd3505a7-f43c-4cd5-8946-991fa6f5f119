import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { getCurrentUserRole, requireRole } from '@/lib/rbac'
import { z } from 'zod'
import { log } from '@/lib/log'

const analyticsQuerySchema = z.object({
  timeframe: z.enum(['7d', '30d', '90d', '1y']).optional().default('30d'),
  metric: z.enum(['overview', 'partners', 'deals', 'earnings', 'leads']).optional().default('overview')
})

export async function GET(request: NextRequest) {
  const { userId } = await auth()
  
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const url = new URL(request.url)
  const queryParams = Object.fromEntries(url.searchParams)
  
  const validation = analyticsQuerySchema.safeParse(queryParams)
  if (!validation.success) {
    return NextResponse.json({ 
      error: 'Invalid query parameters',
      details: validation.error.issues 
    }, { status: 400 })
  }

  const { timeframe, metric } = validation.data

  try {
    // Check admin permissions using RBAC
    const role = await getCurrentUserRole()
    if (!role) {
      return NextResponse.json({ error: 'Access denied' }, { status: 401 })
    }

    // Verify user has admin role
    try {
      await requireRole(['ops', 'accounting', 'super_admin'])
    } catch (error) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get user display name for logging
    const client = await createClient(true)
    const { data: internalUser } = await client
      .from('internal_users')
      .select('display_name')
      .eq('id', userId)
      .single()

    // Calculate date range
    const endDate = new Date()
    const startDate = new Date()
    switch (timeframe) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7)
        break
      case '30d':
        startDate.setDate(startDate.getDate() - 30)
        break
      case '90d':
        startDate.setDate(startDate.getDate() - 90)
        break
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1)
        break
    }

    const dateFilter = startDate.toISOString()

    // Get analytics data based on metric
    let analyticsData = {}

    if (metric === 'overview' || metric === 'partners') {
      // Partner metrics
      const { data: partnerStats } = await client.rpc('get_partner_analytics', {
        p_start_date: dateFilter,
        p_end_date: endDate.toISOString()
      })

      analyticsData = {
        ...analyticsData,
        partners: {
          total: partnerStats?.total_partners || 0,
          active: partnerStats?.active_partners || 0,
          pending: partnerStats?.pending_partners || 0,
          newThisPeriod: partnerStats?.new_partners || 0,
          tierBreakdown: {
            trusted: partnerStats?.trusted_count || 0,
            elite: partnerStats?.elite_count || 0,
            diamond: partnerStats?.diamond_count || 0
          }
        }
      }
    }

    if (metric === 'overview' || metric === 'deals') {
      // Deal metrics
      const { data: dealStats } = await client.rpc('get_deal_analytics', {
        p_start_date: dateFilter,
        p_end_date: endDate.toISOString()
      })

      analyticsData = {
        ...analyticsData,
        deals: {
          total: dealStats?.total_deals || 0,
          confirmed: dealStats?.confirmed_deals || 0,
          pending: dealStats?.pending_deals || 0,
          totalValue: dealStats?.total_value || 0,
          averageValue: dealStats?.average_value || 0,
          statusBreakdown: {
            pending: dealStats?.pending_deals || 0,
            confirmed: dealStats?.confirmed_deals || 0,
            paid: dealStats?.paid_deals || 0,
            disputed: dealStats?.disputed_deals || 0
          }
        }
      }
    }

    if (metric === 'overview' || metric === 'earnings') {
      // Earnings metrics
      const { data: earningsStats } = await client.rpc('get_earnings_analytics', {
        p_start_date: dateFilter,
        p_end_date: endDate.toISOString()
      })

      analyticsData = {
        ...analyticsData,
        earnings: {
          totalCommissions: earningsStats?.total_commissions || 0,
          paidOut: earningsStats?.paid_out || 0,
          pending: earningsStats?.pending_payout || 0,
          averageCommission: earningsStats?.average_commission || 0,
          topEarners: earningsStats?.top_earners || []
        }
      }
    }

    if (metric === 'overview' || metric === 'leads') {
      // Lead metrics
      const { data: leadStats } = await client.rpc('get_lead_analytics', {
        p_start_date: dateFilter,
        p_end_date: endDate.toISOString()
      })

      analyticsData = {
        ...analyticsData,
        leads: {
          total: leadStats?.total_leads || 0,
          approved: leadStats?.approved_leads || 0,
          pending: leadStats?.pending_leads || 0,
          conversionRate: leadStats?.conversion_rate || 0,
          averageValue: leadStats?.average_lead_value || 0
        }
      }
    }

    // Get time series data for charts
    if (metric === 'overview') {
      const { data: timeSeriesData } = await client.rpc('get_analytics_time_series', {
        p_start_date: dateFilter,
        p_end_date: endDate.toISOString(),
        p_interval: timeframe === '7d' ? 'day' : timeframe === '30d' ? 'day' : 'week'
      })

      analyticsData = {
        ...analyticsData,
        timeSeries: timeSeriesData || []
      }
    }

    // Get top performers
    const { data: topPerformers } = await client
      .from('partners_profiles')
      .select(`
        id,
        full_name,
        company_name,
        tier,
        partners_deals!left(
          status,
          deal_value,
          commission_amount
        )
      `)
      .eq('status', 'active')
      .limit(10)

    const topPartners = topPerformers?.map(partner => {
      const deals = partner.partners_deals || []
      const totalDeals = deals.length
      const totalValue = deals.reduce((sum: number, deal: any) => sum + (deal.deal_value || 0), 0)
      const totalCommissions = deals.reduce((sum: number, deal: any) => sum + (deal.commission_amount || 0), 0)
      
      return {
        id: partner.id,
        name: partner.full_name,
        company: partner.company_name,
        tier: partner.tier,
        totalDeals,
        totalValue,
        totalCommissions
      }
    }).sort((a, b) => b.totalCommissions - a.totalCommissions)

    // Get recent pending applications for quick approval
    const { data: pendingPartners } = await client
      .from('partners_applications')
      .select('id, full_name, company_name, company_type, created_at, email')
      .eq('status', 'pending')
      .order('created_at', { ascending: false })
      .limit(5)

    analyticsData = {
      ...analyticsData,
      topPerformers: topPartners || [],
      pendingPartners: pendingPartners || []
    }

    log.businessEvent('analytics_viewed', {
      userId,
      metadata: {
        adminUserId: userId,
        adminName: internalUser?.display_name || 'Unknown Admin',
        metric,
        timeframe
      }
    })

    return NextResponse.json({
      success: true,
      data: analyticsData,
      metadata: {
        timeframe,
        metric,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        generatedAt: new Date().toISOString()
      }
    })

  } catch (error) {
    log.error('Error in analytics API', { 
      error: error instanceof Error ? error.message : String(error),
      userId,
      metadata: {
        metric,
        timeframe
      }
    })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}