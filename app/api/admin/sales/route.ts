import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { requireRole } from '@/lib/rbac'
import { log } from '@/lib/log'
import { SALES_VALIDATION_RULES } from '@/lib/types/sales'

export async function GET(request: NextRequest) {
  try {
    // Only super_admin and ops can view sales team management
    await requireRole(['super_admin', 'ops'])
    
    const { searchParams } = new URL(request.url)
    const includeStats = searchParams.get('include_stats') === 'true'

    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const client = await createClient()
    
    // Get sales team with referral stats
    const { data: salesTeam, error } = await client
      .from('partners_sales')
      .select(`
        id,
        name,
        email,
        referral_slug,
        telegram_handle,
        telegram_link,
        is_active,
        created_at,
        updated_at,
        partners_sales_referral_links!left(
          id,
          slug,
          clicks,
          active
        )
      `)
      .order('name')

    if (error) {
      log.error('Failed to fetch sales team', { error })
      return NextResponse.json(
        { error: 'Failed to fetch sales team' },
        { status: 500 }
      )
    }

    // Get attribution stats if requested
    let stats = null
    if (includeStats) {
      const { data: attributionStats } = await client
        .from('partners_referral_attribution')
        .select('attribution_type, converted, sales_id, partner_id')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Last 30 days

      if (attributionStats) {
        const totalClicks = attributionStats.length
        const totalConversions = attributionStats.filter(a => a.converted).length
        const salesClicks = attributionStats.filter(a => a.attribution_type === 'sales').length
        const salesConversions = attributionStats.filter(a => a.attribution_type === 'sales' && a.converted).length

        stats = {
          totalClicks,
          totalConversions,
          conversionRate: totalClicks > 0 ? totalConversions / totalClicks : 0,
          topPerformers: [], // Would need more complex query for this
          attributionBreakdown: {
            partner: attributionStats.filter(a => a.attribution_type === 'partner').length,
            sales: salesClicks,
            direct: attributionStats.filter(a => a.attribution_type === 'direct').length
          }
        }
      }
    }

    // Transform data to include calculated stats
    const salesTeamWithStats = salesTeam?.map(member => {
      const referralLinks = Array.isArray(member.partners_sales_referral_links) 
        ? member.partners_sales_referral_links 
        : []

      const totalClicks = referralLinks.reduce((sum, link) => sum + (link?.clicks || 0), 0)
      
      // Get attribution data for this sales person
      const memberAttributions = salesTeam ? [] : [] // Would need separate query for accurate conversion count

      return {
        id: member.id,
        name: member.name,
        email: member.email,
        referralSlug: member.referral_slug,
        telegramHandle: member.telegram_handle,
        telegramLink: member.telegram_link,
        isActive: member.is_active,
        createdAt: member.created_at,
        updatedAt: member.updated_at,
        referralLinks: referralLinks.map(link => ({
          id: link?.id || '',
          slug: link?.slug || '',
          clicks: link?.clicks || 0,
          active: link?.active || false
        })),
        totalClicks,
        totalConversions: 0, // Would need separate query to calculate accurately
        activeLinks: referralLinks.filter(link => link?.active).length
      }
    }) || []

    return NextResponse.json({
      success: true,
      data: salesTeamWithStats,
      stats
    })

  } catch (error) {
    log.error('Failed to get sales team', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Only super_admin can create sales team members
    await requireRole(['super_admin'])
    
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { name, email, telegramHandle, telegramLink } = body

    // Validate input
    if (!name || name.length < SALES_VALIDATION_RULES.name.minLength || name.length > SALES_VALIDATION_RULES.name.maxLength) {
      return NextResponse.json(
        { error: `Name must be between ${SALES_VALIDATION_RULES.name.minLength} and ${SALES_VALIDATION_RULES.name.maxLength} characters` },
        { status: 400 }
      )
    }

    if (!email || !SALES_VALIDATION_RULES.email.pattern.test(email)) {
      return NextResponse.json(
        { error: 'Valid email is required' },
        { status: 400 }
      )
    }

    if (telegramHandle && !SALES_VALIDATION_RULES.telegramHandle.pattern.test(telegramHandle)) {
      return NextResponse.json(
        { error: 'Invalid Telegram handle format' },
        { status: 400 }
      )
    }

    // Generate referral slug from email
    const emailPrefix = email.split('@')[0].toLowerCase()
    const referralSlug = emailPrefix.replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '')

    const client = await createClient()

    // Check if email or slug already exists
    const { data: existingMember } = await client
      .from('partners_sales')
      .select('id, email, referral_slug')
      .or(`email.eq.${email},referral_slug.eq.${referralSlug}`)
      .single()

    if (existingMember) {
      if (existingMember.email === email) {
        return NextResponse.json(
          { error: 'A sales member with this email already exists' },
          { status: 409 }
        )
      } else {
        return NextResponse.json(
          { error: 'A sales member with this referral slug already exists' },
          { status: 409 }
        )
      }
    }

    // Create sales team member
    const { data: newMember, error } = await client
      .from('partners_sales')
      .insert({
        name,
        email,
        referral_slug: referralSlug,
        telegram_handle: telegramHandle || null,
        telegram_link: telegramLink || null,
        is_active: true
      })
      .select()
      .single()

    if (error) {
      log.error('Failed to create sales member', { error })
      return NextResponse.json(
        { error: 'Failed to create sales team member' },
        { status: 500 }
      )
    }

    // Create initial referral link
    await client
      .from('partners_sales_referral_links')
      .insert({
        sales_id: newMember.id,
        slug: referralSlug,
        active: true
      })

    log.info('Sales team member created', { 
      metadata: {
        memberId: newMember.id, 
        email: newMember.email,
        referralSlug
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        id: newMember.id,
        name: newMember.name,
        email: newMember.email,
        referralSlug: newMember.referral_slug,
        telegramHandle: newMember.telegram_handle,
        telegramLink: newMember.telegram_link,
        isActive: newMember.is_active,
        createdAt: newMember.created_at,
        updatedAt: newMember.updated_at
      }
    })

  } catch (error) {
    log.error('Failed to create sales team member', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}