import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { requireRole } from '@/lib/rbac'
import { log } from '@/lib/log'
import { SALES_VALIDATION_RULES } from '@/lib/types/sales'

type Props = {
  params: Promise<{ id: string }>
}

export async function GET(request: NextRequest, { params }: Props) {
  try {
    // Only super_admin and ops can view sales team details
    await requireRole(['super_admin', 'ops'])
    
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const resolvedParams = await params
    const { id } = resolvedParams

    const client = await createClient()
    
    // Get sales member with referral stats
    const { data: member, error } = await client
      .from('partners_sales')
      .select(`
        id,
        name,
        email,
        referral_slug,
        telegram_handle,
        telegram_link,
        is_active,
        created_at,
        updated_at,
        partners_sales_referral_links!left(
          id,
          slug,
          clicks,
          active,
          created_at
        )
      `)
      .eq('id', id)
      .single()

    if (error || !member) {
      return NextResponse.json(
        { error: 'Sales team member not found' },
        { status: 404 }
      )
    }

    // Get attribution stats for this member
    const { data: attributions } = await client
      .from('partners_referral_attribution')
      .select('id, converted, created_at, lead_id')
      .eq('sales_id', id)

    const referralLinks = Array.isArray(member.partners_sales_referral_links) 
      ? member.partners_sales_referral_links 
      : []

    const totalClicks = referralLinks.reduce((sum, link) => sum + (link?.clicks || 0), 0)
    const totalConversions = attributions?.filter(a => a.converted).length || 0

    const memberWithStats = {
      id: member.id,
      name: member.name,
      email: member.email,
      referralSlug: member.referral_slug,
      telegramHandle: member.telegram_handle,
      telegramLink: member.telegram_link,
      isActive: member.is_active,
      createdAt: member.created_at,
      updatedAt: member.updated_at,
      referralLinks: referralLinks.map(link => ({
        id: link?.id || '',
        slug: link?.slug || '',
        clicks: link?.clicks || 0,
        active: link?.active || false,
        createdAt: link?.created_at
      })),
      totalClicks,
      totalConversions,
      conversionRate: totalClicks > 0 ? totalConversions / totalClicks : 0,
      activeLinks: referralLinks.filter(link => link?.active).length
    }

    return NextResponse.json({
      success: true,
      data: memberWithStats
    })

  } catch (error) {
    log.error('Failed to get sales member details', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest, { params }: Props) {
  try {
    // Only super_admin can update sales team members
    await requireRole(['super_admin'])
    
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const resolvedParams = await params
    const { id } = resolvedParams

    const body = await request.json()
    const { name, email, telegramHandle, telegramLink, isActive } = body

    const client = await createClient()

    // Verify member exists
    const { data: existingMember } = await client
      .from('partners_sales')
      .select('id, email, referral_slug')
      .eq('id', id)
      .single()

    if (!existingMember) {
      return NextResponse.json(
        { error: 'Sales team member not found' },
        { status: 404 }
      )
    }

    // Validate input if provided
    const updates: any = {}

    if (name !== undefined) {
      if (!name || name.length < SALES_VALIDATION_RULES.name.minLength || name.length > SALES_VALIDATION_RULES.name.maxLength) {
        return NextResponse.json(
          { error: `Name must be between ${SALES_VALIDATION_RULES.name.minLength} and ${SALES_VALIDATION_RULES.name.maxLength} characters` },
          { status: 400 }
        )
      }
      updates.name = name
    }

    if (email !== undefined) {
      if (!email || !SALES_VALIDATION_RULES.email.pattern.test(email)) {
        return NextResponse.json(
          { error: 'Valid email is required' },
          { status: 400 }
        )
      }

      // Check if email is already used by another member
      if (email !== existingMember.email) {
        const { data: emailCheck } = await client
          .from('partners_sales')
          .select('id')
          .eq('email', email)
          .neq('id', id)
          .single()

        if (emailCheck) {
          return NextResponse.json(
            { error: 'Email is already used by another sales team member' },
            { status: 409 }
          )
        }
      }

      updates.email = email

      // If email changed, update referral slug
      if (email !== existingMember.email) {
        const emailPrefix = email.split('@')[0].toLowerCase()
        const newReferralSlug = emailPrefix.replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '')
        
        // Check if new slug conflicts
        const { data: slugCheck } = await client
          .from('partners_sales')
          .select('id')
          .eq('referral_slug', newReferralSlug)
          .neq('id', id)
          .single()

        if (!slugCheck) {
          updates.referral_slug = newReferralSlug
          
          // Update referral links with new slug
          await client
            .from('partners_sales_referral_links')
            .update({ slug: newReferralSlug })
            .eq('sales_id', id)
            .eq('slug', existingMember.referral_slug)
        }
      }
    }

    if (telegramHandle !== undefined) {
      if (telegramHandle && !SALES_VALIDATION_RULES.telegramHandle.pattern.test(telegramHandle)) {
        return NextResponse.json(
          { error: 'Invalid Telegram handle format' },
          { status: 400 }
        )
      }
      updates.telegram_handle = telegramHandle || null
    }

    if (telegramLink !== undefined) {
      updates.telegram_link = telegramLink || null
    }

    if (isActive !== undefined) {
      updates.is_active = Boolean(isActive)
    }

    // Update the member
    const { data: updatedMember, error } = await client
      .from('partners_sales')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      log.error('Failed to update sales member', { 
        error,
        metadata: { memberId: id }
      })
      return NextResponse.json(
        { error: 'Failed to update sales team member' },
        { status: 500 }
      )
    }

    log.info('Sales team member updated', { 
      metadata: {
        memberId: id, 
        updates: Object.keys(updates)
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        id: updatedMember.id,
        name: updatedMember.name,
        email: updatedMember.email,
        referralSlug: updatedMember.referral_slug,
        telegramHandle: updatedMember.telegram_handle,
        telegramLink: updatedMember.telegram_link,
        isActive: updatedMember.is_active,
        updatedAt: updatedMember.updated_at
      }
    })

  } catch (error) {
    log.error('Failed to update sales team member', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: Props) {
  try {
    // Only super_admin can delete sales team members
    await requireRole(['super_admin'])
    
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const resolvedParams = await params
    const { id } = resolvedParams

    const client = await createClient()

    // Check if member exists and get their info
    const { data: member } = await client
      .from('partners_sales')
      .select('id, name, email')
      .eq('id', id)
      .single()

    if (!member) {
      return NextResponse.json(
        { error: 'Sales team member not found' },
        { status: 404 }
      )
    }

    // Check if member has any active assignments or conversions
    const { data: attributions } = await client
      .from('partners_referral_attribution')
      .select('id')
      .eq('sales_id', id)
      .eq('converted', true)
      .limit(1)

    if (attributions && attributions.length > 0) {
      // Instead of deleting, just deactivate
      const { error } = await client
        .from('partners_sales')
        .update({ is_active: false })
        .eq('id', id)

      if (error) {
        log.error('Failed to deactivate sales member', { 
          error,
          metadata: { memberId: id }
        })
        return NextResponse.json(
          { error: 'Failed to deactivate sales team member' },
          { status: 500 }
        )
      }

      log.info('Sales team member deactivated (has conversions)', { 
        metadata: {
          memberId: id,
          memberName: member.name
        }
      })

      return NextResponse.json({
        success: true,
        message: 'Sales team member deactivated (cannot delete due to existing conversions)'
      })
    }

    // Safe to delete - no conversions
    const { error } = await client
      .from('partners_sales')
      .delete()
      .eq('id', id)

    if (error) {
      log.error('Failed to delete sales member', { 
        error,
        metadata: { memberId: id }
      })
      return NextResponse.json(
        { error: 'Failed to delete sales team member' },
        { status: 500 }
      )
    }

    log.info('Sales team member deleted', { 
      metadata: {
        memberId: id,
        memberName: member.name
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Sales team member deleted successfully'
    })

  } catch (error) {
    log.error('Failed to delete sales team member', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}