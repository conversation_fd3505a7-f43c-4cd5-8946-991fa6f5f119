import { NextRequest, NextResponse } from 'next/server'
import { auth, clerkClient } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { requireRole } from '@/lib/rbac'
import { addAdminUserSchema, updateUserRoleSchema, removeAdminAccessSchema, userFiltersSchema, type AdminUser } from '@/lib/validations/users'
import { log } from '@/lib/log'
import { z } from 'zod'

// GET /api/admin/users - List all users with optional role filtering
export async function GET(request: NextRequest) {
  try {
    // Check for different permission levels based on role filter
    const { searchParams } = new URL(request.url)
    const roleFilter = searchParams.get('role')
    
    if (roleFilter === 'sales') {
      // Allow ops, super_admin to see sales users (for assignment purposes)
      await requireRole(['ops', 'super_admin', 'sales'])
    } else {
      // Only super_admin can access full user management
      await requireRole(['super_admin'])
    }

    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const client = await createClient(true) // Service role for admin operations

    // Simple role-based filtering for sales assignment feature
    if (roleFilter && roleFilter !== 'all') {
      const { data: users, error } = await client
        .from('partners_users')
        .select('id, email, display_name, role')
        .eq('role', roleFilter)
        .order('display_name', { ascending: true })
      
      if (error) {
        console.error('Error fetching users by role:', error)
        return NextResponse.json(
          { error: 'Failed to fetch users' },
          { status: 500 }
        )
      }
      
      return NextResponse.json({
        success: true,
        data: users || []
      })
    }

    // For full user management (existing code continues below)
    const filters = Object.fromEntries(searchParams.entries())
    const validation = userFiltersSchema.safeParse(filters)
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Invalid filters',
        details: validation.error.issues 
      }, { status: 400 })
    }

    // Get all admin users from partners_users table
    let query = client
      .from('partners_users')
      .select('*')
      .order('created_at', { ascending: false })

    // Apply role filter
    if (validation.data.role && validation.data.role !== 'all') {
      query = query.eq('role', validation.data.role)
    }

    // Apply search filter
    if (validation.data.search) {
      query = query.or(`
        email.ilike.%${validation.data.search}%,
        display_name.ilike.%${validation.data.search}%
      `)
    }

    const { data: adminUsers, error: adminError } = await query
    
    if (adminError) {
      log.error('Failed to fetch admin users', { userId, error: adminError.message })
      return NextResponse.json({ error: 'Failed to fetch admin users' }, { status: 500 })
    }

    // Get additional user info from Clerk for each admin user
    const enrichedUsers: AdminUser[] = []

    for (const adminUser of adminUsers || []) {
      try {
        // Try to find the Clerk user by email
        const clerkUsers = await (await clerkClient()).users.getUserList({
          emailAddress: [adminUser.email],
          limit: 1
        })

        const clerkUser = clerkUsers.data[0]
        
        enrichedUsers.push({
          id: adminUser.id,
          email: adminUser.email,
          display_name: adminUser.display_name,
          role: adminUser.role,
          created_at: adminUser.created_at,
          updated_at: adminUser.updated_at,
          last_sign_in_at: clerkUser?.lastSignInAt ? new Date(clerkUser.lastSignInAt).toISOString() : undefined,
          first_name: clerkUser?.firstName || null,
          last_name: clerkUser?.lastName || null,
          profile_image_url: clerkUser?.profileImageUrl || null,
          clerk_user_id: clerkUser?.id || null
        })
      } catch (clerkError) {
        // If we can't find the user in Clerk, still include the admin user data
        enrichedUsers.push({
          id: adminUser.id,
          email: adminUser.email,
          display_name: adminUser.display_name,
          role: adminUser.role,
          created_at: adminUser.created_at,
          updated_at: adminUser.updated_at,
          last_sign_in_at: null,
          first_name: null,
          last_name: null,
          profile_image_url: null,
          clerk_user_id: null
        })
      }
    }

    // Also get recently active users from Clerk who don't have admin roles
    try {
      const recentClerkUsers = await (await clerkClient()).users.getUserList({
        limit: 50,
        orderBy: '-last_sign_in_at'
      })

      // Find users who have logged in but don't have admin roles
      const adminEmails = new Set(adminUsers?.map(u => u.email) || [])
      
      const recentNonAdminUsers = recentClerkUsers.data
        .filter(clerkUser => {
          const email = clerkUser.emailAddresses[0]?.emailAddress
          return email && !adminEmails.has(email)
        })
        .slice(0, 20) // Limit to 20 recent non-admin users
        .map(clerkUser => ({
          id: `clerk_${clerkUser.id}`,
          email: clerkUser.emailAddresses[0]?.emailAddress || '',
          display_name: `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || null,
          role: null,
          created_at: new Date(clerkUser.createdAt).toISOString(),
          updated_at: new Date(clerkUser.updatedAt).toISOString(),
          last_sign_in_at: clerkUser.lastSignInAt ? new Date(clerkUser.lastSignInAt).toISOString() : null,
          first_name: clerkUser.firstName || null,
          last_name: clerkUser.lastName || null,
          profile_image_url: clerkUser.profileImageUrl || null,
          clerk_user_id: clerkUser.id
        }))

      // Combine admin users and recent non-admin users
      const allUsers = [...enrichedUsers, ...recentNonAdminUsers]

      // Apply pagination
      const offset = (validation.data.page - 1) * validation.data.limit
      const paginatedUsers = allUsers.slice(offset, offset + validation.data.limit)

      const pagination = {
        page: validation.data.page,
        limit: validation.data.limit,
        total: allUsers.length,
        pages: Math.ceil(allUsers.length / validation.data.limit)
      }

      log.info('Admin users fetched', { 
        userId, 
        metadata: { count: paginatedUsers.length }
      })
      return NextResponse.json({ 
        data: paginatedUsers, 
        pagination,
        metadata: {
          adminUsersCount: enrichedUsers.length,
          totalClerkUsers: recentClerkUsers.totalCount
        }
      })

    } catch (clerkError) {
      log.warn('Failed to fetch Clerk users', { userId, error: clerkError })
      // Return just admin users if Clerk fails
      return NextResponse.json({ 
        data: enrichedUsers,
        pagination: {
          page: 1,
          limit: enrichedUsers.length,
          total: enrichedUsers.length,
          pages: 1
        }
      })
    }

  } catch (error) {
    log.error('Admin users GET error', { error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/admin/users - Add new admin user
export async function POST(request: NextRequest) {
  try {
    await requireRole(['super_admin'])

    const body = await request.json()
    const validation = addAdminUserSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Validation failed',
        details: validation.error.issues 
      }, { status: 400 })
    }

    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const client = await createClient(true)

    // Check if user already exists in partners_users
    const { data: existingUser } = await client
      .from('partners_users')
      .select('id')
      .eq('email', validation.data.email)
      .single()

    if (existingUser) {
      return NextResponse.json({ 
        error: 'User already has admin access' 
      }, { status: 400 })
    }

    // Verify user exists in Clerk
    try {
      const clerkUsers = await (await clerkClient()).users.getUserList({
        emailAddress: [validation.data.email],
        limit: 1
      })

      if (clerkUsers.data.length === 0) {
        return NextResponse.json({ 
          error: 'User not found. They must log in at least once before being granted admin access.' 
        }, { status: 400 })
      }
    } catch (clerkError) {
      return NextResponse.json({ 
        error: 'Failed to verify user existence' 
      }, { status: 500 })
    }

    // Create admin user
    const { data: newAdminUser, error } = await client
      .from('partners_users')
      .insert({
        email: validation.data.email,
        display_name: validation.data.display_name,
        role: validation.data.role
      })
      .select()
      .single()

    if (error) {
      log.error('Failed to create admin user', { userId, error: error.message })
      return NextResponse.json({ error: 'Failed to create admin user' }, { status: 500 })
    }

    // Log the action
    log.businessEvent('admin_user_created', {
      userId,
      metadata: {
        createdUserId: newAdminUser.id,
        createdUserEmail: newAdminUser.email,
        assignedRole: newAdminUser.role,
        createdByUserId: userId
      }
    })

    return NextResponse.json({ data: newAdminUser }, { status: 201 })

  } catch (error) {
    log.error('Admin users POST error', { error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT /api/admin/users - Update user role
export async function PUT(request: NextRequest) {
  try {
    await requireRole(['super_admin'])

    const body = await request.json()
    const validation = updateUserRoleSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Validation failed',
        details: validation.error.issues 
      }, { status: 400 })
    }

    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const client = await createClient(true)

    // Prevent super_admin from changing their own role if they're the only super_admin
    const { data: superAdmins } = await client
      .from('partners_users')
      .select('id')
      .eq('role', 'super_admin')

    if (superAdmins?.length === 1 && superAdmins[0].id === validation.data.user_id) {
      return NextResponse.json({ 
        error: 'Cannot change role of the last super admin' 
      }, { status: 400 })
    }

    // Get current user data for logging
    const { data: currentUser } = await client
      .from('partners_users')
      .select('email, role')
      .eq('id', validation.data.user_id)
      .single()

    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Update user role
    const { data: updatedUser, error } = await client
      .from('partners_users')
      .update({
        role: validation.data.role,
        updated_at: new Date().toISOString()
      })
      .eq('id', validation.data.user_id)
      .select()
      .single()

    if (error) {
      log.error('Failed to update user role', { userId, error: error.message })
      return NextResponse.json({ error: 'Failed to update user role' }, { status: 500 })
    }

    // Log the action
    log.businessEvent('admin_user_role_changed', {
      userId,
      metadata: {
        targetUserId: validation.data.user_id,
        targetUserEmail: currentUser.email,
        oldRole: currentUser.role,
        newRole: validation.data.role,
        reason: validation.data.reason,
        changedByUserId: userId
      }
    })

    return NextResponse.json({ data: updatedUser })

  } catch (error) {
    log.error('Admin users PUT error', { error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE /api/admin/users - Remove admin access
export async function DELETE(request: NextRequest) {
  try {
    await requireRole(['super_admin'])

    const body = await request.json()
    const validation = removeAdminAccessSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Validation failed',
        details: validation.error.issues 
      }, { status: 400 })
    }

    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const client = await createClient(true)

    // Get user data before deletion
    const { data: userToDelete } = await client
      .from('partners_users')
      .select('email, role')
      .eq('id', validation.data.user_id)
      .single()

    if (!userToDelete) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Prevent deletion of last super_admin
    if (userToDelete.role === 'super_admin') {
      const { data: superAdmins } = await client
        .from('partners_users')
        .select('id')
        .eq('role', 'super_admin')

      if (superAdmins?.length === 1) {
        return NextResponse.json({ 
          error: 'Cannot remove the last super admin' 
        }, { status: 400 })
      }
    }

    // Remove admin access
    const { error } = await client
      .from('partners_users')
      .delete()
      .eq('id', validation.data.user_id)

    if (error) {
      log.error('Failed to remove admin access', { userId, error: error.message })
      return NextResponse.json({ error: 'Failed to remove admin access' }, { status: 500 })
    }

    // Log the action
    log.businessEvent('admin_access_revoked', {
      userId,
      metadata: {
        revokedUserId: validation.data.user_id,
        revokedUserEmail: userToDelete.email,
        previousRole: userToDelete.role,
        reason: validation.data.reason,
        revokedByUserId: userId
      }
    })

    return NextResponse.json({ success: true })

  } catch (error) {
    log.error('Admin users DELETE error', { error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}