import { NextResponse } from "next/server"
import { auth, currentUser } from "@clerk/nextjs/server"
import { createClient } from "@/lib/supabase/server"

export async function GET() {
  try {
    const auth_result = await auth()
    const { userId } = auth_result
    
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Try to get email from multiple sources
    let email = auth_result.sessionClaims?.email as string

    // If not in session claims, try currentUser
    if (!email) {
      try {
        const user = await currentUser()
        email = user?.emailAddresses?.[0]?.emailAddress || ''
      } catch (userError) {
        console.error('Error getting current user:', userError)
      }
    }

    // Debug logging
    console.log('Check role API debug:', {
      userId: userId,
      email: email,
      sessionClaims: auth_result.sessionClaims,
      hasCurrentUserEmail: !!email
    })

    if (!email) {
      return NextResponse.json({ error: "Email not found in session or user data" }, { status: 400 })
    }

    // Use service role for admin operations to bypass RLS
    const supabase = await createClient(true)

    // Check user role in database
    console.log('Querying database for email:', email)
    const { data: userData, error } = await supabase
      .from("internal_users")
      .select("role, email, full_name")
      .eq("email", email)
      .single()

    console.log('Database query result:', {
      userData: userData,
      error: error,
      hasData: !!userData
    })

    if (error || !userData) {
      console.log('No user found or error:', error?.message)
      return NextResponse.json({ 
        role: null, 
        isAdmin: false,
        isSuperAdmin: false,
        debug: { email, error: error?.message }
      })
    }

    const result = {
      role: userData.role,
      isAdmin: ["sales", "ops", "accounting", "super_admin"].includes(userData.role),
      isSuperAdmin: userData.role === "super_admin",
      debug: { email, foundUser: userData.full_name }
    }

    console.log('Returning role result:', result)
    return NextResponse.json(result)
  } catch (error) {
    console.error("Error checking user role:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}