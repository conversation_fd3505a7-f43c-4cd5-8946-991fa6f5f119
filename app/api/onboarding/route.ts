import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { onboardingSchema } from '@/lib/validations'
import { log } from '@/lib/log'
import { trackServerEvent } from '@/lib/analytics-server'

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validation = onboardingSchema.safeParse(body)

    if (!validation.success) {
      log.warn('Onboarding validation failed', {
        userId,
        errors: validation.error.issues
      })
      return NextResponse.json({
        error: 'Validation failed',
        details: validation.error.issues
      }, { status: 400 })
    }

    // Use authenticated client with proper RLS context
    const client = await createClient()

    // Set RLS context to current user for policies
    try {
      const { error: setCtxError } = await client.rpc('set_auth_user_id', { user_id: userId })
      if (setCtxError) {
        console.warn('API ONBOARDING: Failed to set RLS context', { userId, error: setCtxError.message })
      } else {
        console.log('API ONBOARDING: RLS context set', { userId })
      }
    } catch (e) {
      console.warn('API ONBOARDING: Exception setting RLS context', { userId, error: (e as any)?.message })
    }

    // Debug: Verify auth context is set
    const { data: authContext } = await client
      .rpc('current_user_id')
      .single()

    console.log('API ONBOARDING: Auth context check', {
      clerkUserId: userId,
      dbAuthContext: authContext,
      match: authContext === userId
    })

    // Check if profile already exists
    const { data: existingProfile, error: existingProfileError } = await client
      .from('partners_profiles')
      .select('id, status, email')
      .eq('id', userId)
      .maybeSingle()

    if (existingProfileError) {
      console.warn('API ONBOARDING: existingProfile check error', { userId, error: existingProfileError.message })
    }

    if (existingProfile) {
      console.log('API ONBOARDING: Profile already exists', { userId, status: existingProfile.status })
      return NextResponse.json({
        error: 'Profile already exists',
        profile: existingProfile
      }, { status: 409 })
    }

    // Get user email from Clerk using the SDK
    const { currentUser } = await import('@clerk/nextjs/server')
    const user = await currentUser()

    const userEmail = user?.emailAddresses?.[0]?.emailAddress || user?.primaryEmailAddress?.emailAddress

    if (!userEmail) {
      console.log('API ONBOARDING: No email found for user', {
        userId,
        hasUser: !!user,
        emailAddresses: user?.emailAddresses?.length || 0
      })
      return NextResponse.json({ error: 'Email not found in Clerk profile' }, { status: 400 })
    }

    console.log('API ONBOARDING: Found user email', { userId, email: userEmail })

    // Generate referral slug from company name
    const baseSlug = validation.data.company_name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-|-$/g, '')
      .slice(0, 20)

    // Find unique slug
    let referralSlug = baseSlug
    let counter = 1
    while (true) {
      const { data: existingSlug } = await client
        .from('partners_referral_links')
        .select('id')
        .eq('slug', referralSlug)
        .maybeSingle()

      if (!existingSlug) break
      referralSlug = `${baseSlug}-${counter}`
      counter++
    }

    // Build the application object (unified review process)
    const applicationData: any = {
      email: userEmail,
      full_name: validation.data.full_name,
      company_name: validation.data.company_name,
      company_type: validation.data.company_type,
      role: validation.data.role,
      status: 'pending',
      priority: 2, // Medium priority for onboarding submissions
      preferred_communication: validation.data.preferred_communication || [],
      preferred_payment_method: validation.data.preferred_payment_method,
      terms_accepted: validation.data.terms_accepted,
      privacy_accepted: validation.data.privacy_accepted,
      referral_source: 'onboarding_form',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // Add optional fields only if they have values
    if (validation.data.company_type_other) {
      applicationData.company_type_other = validation.data.company_type_other
    }
    if (validation.data.role_other) {
      applicationData.role_other = validation.data.role_other
    }
    if (validation.data.telegram) {
      applicationData.telegram = validation.data.telegram
    }
    if (validation.data.whatsapp) {
      applicationData.whatsapp = validation.data.whatsapp
    }
    if (validation.data.x_profile) {
      applicationData.x_profile = validation.data.x_profile
    }

    console.log('API ONBOARDING: Creating application with data', {
      userId,
      hasEmail: !!applicationData.email,
      fieldCount: Object.keys(applicationData).length
    })

    // Use service role to create application record (bypassing RLS)
    const serviceClient = await createClient(true)
    
    // Create application for review (unified process)
    const { data: application, error: applicationError } = await serviceClient
      .from('partners_applications')
      .insert(applicationData)
      .select()
      .single()

    if (applicationError) {
      log.error('Failed to create application', {
        userId,
        error: applicationError.message,
        code: applicationError.code,
        details: applicationError.details,
        hint: applicationError.hint
      })
      console.error('API ONBOARDING: Application creation failed', {
        error: applicationError,
        applicationData: {
          email: applicationData.email,
          full_name: applicationData.full_name
        }
      })
      if (applicationError.code === '23505' && String(applicationError.message).includes('email')) {
        console.warn('API ONBOARDING: Email already has pending application', { userId, email: applicationData.email })
        return NextResponse.json({
          error: 'Application already exists for this email',
          details: applicationError.details
        }, { status: 409 })
      }
      return NextResponse.json({
        error: 'Failed to create application',
        details: applicationError.message
      }, { status: 500 })
    }

    console.log('API ONBOARDING: Application created successfully', {
      userId,
      applicationId: application?.id || 'unknown'
    })

    // Create a pending partners_profiles row so middleware treats user as pending (not no_profile)
    console.log('API ONBOARDING: Creating pending partner profile', { userId })
    try {
      const { error: profileError } = await serviceClient
        .from('partners_profiles')
        .insert({
          id: userId,
          email: userEmail,
          full_name: validation.data.full_name,
          company_name: validation.data.company_name,
          company_type: validation.data.company_type,
          role: validation.data.role,
          status: 'pending',
          tier: 'trusted',
          telegram: validation.data.telegram || null,
          whatsapp: validation.data.whatsapp || null,
          x_profile: validation.data.x_profile || null,
          referral_slug: referralSlug,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (profileError) {
        console.error('API ONBOARDING: Failed to create pending profile', { userId, error: profileError })
        log.warn('Failed to create pending profile after application', { userId, error: profileError.message })
      } else {
        console.log('API ONBOARDING: Pending profile created successfully', { userId })
      }
    } catch (profileException) {
      console.error('API ONBOARDING: Exception while creating pending profile', { userId, error: profileException })
      log.warn('Exception creating pending profile after application', { userId, error: (profileException as any)?.message || 'Unknown error' })
    }

    console.log('API ONBOARDING: Skipping referral link creation - will be created upon approval', { userId, referralSlug })

    console.log('API ONBOARDING: Starting audit log', { userId })
    // Log audit event
    try {
      await serviceClient.rpc('log_audit_event', {
        p_user_id: userId,
        p_user_type: 'applicant',
        p_action: 'submit_application_via_onboarding',
        p_resource_type: 'application',
        p_resource_id: application.id,
        p_old_values: null,
        p_new_values: application
      })
      console.log('API ONBOARDING: Audit log completed successfully', { userId })
    } catch (auditError) {
      console.error('API ONBOARDING: Audit log failed', { userId, error: auditError })
      log.warn('Failed to log audit event', { userId, error: (auditError as any)?.message || 'Unknown error' })
    }

    console.log('API ONBOARDING: Starting analytics tracking', { userId })
    // Track analytics event
    try {
      await trackServerEvent(
        'application_submitted_via_onboarding',
        {
          company_type: validation.data.company_type,
          application_id: application.id,
          referral_source: 'onboarding_form'
        },
        userId
      )
      console.log('API ONBOARDING: Analytics tracking completed successfully', { userId })
    } catch (analyticsError) {
      console.error('API ONBOARDING: Analytics tracking failed', { userId, error: analyticsError })
      log.warn('Failed to track analytics event', { userId, error: (analyticsError as any)?.message || 'Unknown error' })
    }

    console.log('API ONBOARDING: Preparing final response', { userId })
    log.info('Application submitted via onboarding', {
      userId,
      companyName: application.company_name,
      applicationId: application.id,
      status: 'pending'
    })

    console.log('API ONBOARDING: About to return success response', { userId })
    return NextResponse.json({
      data: {
        application,
        message: 'Application submitted successfully. Your application is pending review by our team.'
      }
    }, { status: 201 })

  } catch (error) {
    console.error('API ONBOARDING: Caught error in main try-catch', {
      error,
      errorMessage: (error as any)?.message,
      errorStack: (error as any)?.stack,
      errorName: (error as any)?.name,
      errorToString: String(error)
    })
    log.error('Onboarding POST error', { 
      error,
      errorMessage: (error as any)?.message,
      errorStack: (error as any)?.stack 
    })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('API ONBOARDING GET: Starting request')
    const { userId } = await auth()
    console.log('API ONBOARDING GET: userId:', userId ? 'present' : 'missing')

    if (!userId) {
      console.log('API ONBOARDING GET: Returning 401 unauthorized')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const client = await createClient()

    // Get onboarding status
    const { data: profile } = await client
      .from('partners_profiles')
      .select('id, status, full_name, company_name, tier, created_at')
      .eq('id', userId)
      .single()

    if (!profile) {
      return NextResponse.json({
        data: {
          completed: false,
          status: 'not_started'
        }
      })
    }

    return NextResponse.json({
      data: {
        completed: true,
        status: profile.status,
        profile: {
          full_name: profile.full_name,
          company_name: profile.company_name,
          tier: profile.tier,
          created_at: profile.created_at
        }
      }
    })

  } catch (error) {
    log.error('Onboarding GET error', { error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, { status: 200 })
}