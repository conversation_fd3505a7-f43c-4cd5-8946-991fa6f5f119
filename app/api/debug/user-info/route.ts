import { NextResponse } from "next/server"
import { auth, currentUser } from "@clerk/nextjs/server"
import { createClient } from "@/lib/supabase/server"
import { getCurrentUserRole, roleLabels, tierLabels, type UserRole } from "@/lib/rbac"

export async function GET() {
  try {
    console.log('=== DEBUG USER INFO ENDPOINT ===')
    
    // Get Clerk authentication info
    const auth_result = await auth()
    const { userId } = auth_result
    
    console.log('Auth result:', {
      userId: userId,
      sessionClaims: auth_result.sessionClaims
    })

    if (!userId) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }

    // Get current user from Clerk
    let clerkUser = null
    let clerkEmail = null
    
    try {
      clerkUser = await currentUser()
      clerkEmail = clerkUser?.emailAddresses?.[0]?.emailAddress || ''
      console.log('Clerk user:', {
        id: clerkUser?.id,
        email: clerkEmail,
        emailAddressesCount: clerkUser?.emailAddresses?.length
      })
    } catch (error) {
      console.error('Error getting Clerk user:', error)
    }

    // Get email from session claims as fallback
    const sessionEmail = auth_result.sessionClaims?.email as string
    const finalEmail = clerkEmail || sessionEmail

    console.log('Email detection:', {
      clerkEmail,
      sessionEmail,
      finalEmail
    })

    // Check role using our RBAC system
    const userRole = await getCurrentUserRole()
    console.log('RBAC role detection:', userRole)

    // Use service role to check database directly
    const supabase = await createClient(true)
    
    // Check internal_users table
    const { data: internalUser, error: internalError } = await supabase
      .from("internal_users")
      .select("*")
      .eq("email", finalEmail)
      .maybeSingle()

    console.log('Internal users query:', {
      email: finalEmail,
      result: internalUser,
      error: internalError?.message
    })

    // Check partners_profiles table
    const { data: partnerProfile, error: partnerError } = await supabase
      .from("partners_profiles")
      .select("*")
      .eq("id", userId)
      .maybeSingle()

    console.log('Partner profile query:', {
      userId,
      result: partnerProfile,
      error: partnerError?.message
    })

    // Get role label for display
    const roleLabel = userRole ? roleLabels[userRole] : 'Unknown'

    // Test some key permissions
    const testPermissions = [
      'manage_users',
      'view_all_partners', 
      'manage_system_settings',
      'view_audit_logs'
    ]

    const permissionResults: Record<string, boolean> = {}
    
    for (const permission of testPermissions) {
      try {
        const { hasPermission } = await import('@/lib/rbac')
        permissionResults[permission] = await hasPermission(permission as any)
      } catch (error) {
        permissionResults[permission] = false
      }
    }

    const debugInfo = {
      timestamp: new Date().toISOString(),
      clerk: {
        userId: userId,
        email: clerkEmail,
        sessionEmail: sessionEmail,
        finalEmail: finalEmail,
        hasClerkUser: !!clerkUser
      },
      database: {
        internalUser: internalUser,
        partnerProfile: partnerProfile,
        internalUserError: internalError?.message,
        partnerProfileError: partnerError?.message
      },
      rbac: {
        detectedRole: userRole,
        roleLabel: roleLabel,
        isValidRole: userRole ? Object.keys(roleLabels).includes(userRole) : false
      },
      permissions: permissionResults,
      expectedForSuperAdmin: {
        shouldSeeUserManagement: userRole === 'super_admin',
        shouldHaveAllPermissions: userRole === 'super_admin',
        properRoleDisplay: userRole === 'super_admin' ? 'Super Admin' : roleLabel
      }
    }

    console.log('Debug info compiled:', debugInfo)

    return NextResponse.json(debugInfo, { status: 200 })

  } catch (error) {
    console.error("Debug endpoint error:", error)
    return NextResponse.json({ 
      error: "Internal server error", 
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}