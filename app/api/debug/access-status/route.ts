import { NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  const { userId } = await auth()
  
  if (!userId) {
    return NextResponse.json({ error: 'No auth', userId })
  }

  try {
    const client = await createClient(true)
    const { data: profile, error } = await client
      .from('partners_profiles')
      .select('status, full_name, company_name, created_at')
      .eq('id', userId)
      .single()

    return NextResponse.json({ 
      userId,
      profile,
      error: error?.message,
      detectedStatus: profile?.status || 'no_profile',
      profileExists: !!profile
    })
  } catch (error) {
    return NextResponse.json({ 
      userId,
      error: error instanceof Error ? error.message : String(error),
      detectedStatus: 'error'
    })
  }
}