import { NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'No user ID from <PERSON>' })
    }

    const client = await createClient(true)
    
    // Check if profile exists
    const { data: profile, error: profileError } = await client
      .from('partners_profiles')
      .select('*')
      .eq('id', userId)
      .single()

    // Also check all profiles to see what IDs exist
    const { data: allProfiles } = await client
      .from('partners_profiles')
      .select('id, email, status, tier')
      .limit(10)

    return NextResponse.json({
      clerkUserId: userId,
      profile: profile || null,
      profileError: profileError?.message || null,
      allProfiles: allProfiles || []
    })

  } catch (error) {
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : String(error) 
    })
  }
}