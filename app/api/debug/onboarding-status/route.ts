import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ 
        step: 'auth_check',
        status: 'error',
        message: 'No userId from <PERSON>',
        userId: null
      })
    }

    console.log('DEBUG: Checking onboarding status for userId:', userId)

    const client = await createClient()
    
    // Check if profile exists
    const { data: profile, error: profileError } = await client
      .from('partners_profiles')
      .select('id, status, full_name, company_name, created_at')
      .eq('id', userId)
      .single()

    if (profileError && profileError.code !== 'PGRST116') { // PGRST116 = no rows
      console.log('DEBUG: Database error:', profileError)
      return NextResponse.json({
        step: 'database_query',
        status: 'error',
        message: 'Database error',
        error: profileError.message,
        userId
      })
    }

    if (!profile) {
      console.log('DEBUG: No profile found, user should go to onboarding')
      return NextResponse.json({
        step: 'profile_check',
        status: 'success',
        message: 'No profile found - should show onboarding form',
        profileExists: false,
        shouldRedirectTo: '/onboarding',
        userId
      })
    }

    console.log('DEBUG: Profile found:', profile)
    return NextResponse.json({
      step: 'profile_check',
      status: 'success', 
      message: 'Profile exists',
      profileExists: true,
      profile: {
        status: profile.status,
        full_name: profile.full_name,
        company_name: profile.company_name,
        created_at: profile.created_at
      },
      shouldRedirectTo: profile.status === 'active' ? '/' : '/access-pending',
      userId
    })

  } catch (error) {
    console.error('DEBUG: Critical error in onboarding status check:', error)
    return NextResponse.json({
      step: 'exception_handler',
      status: 'error',
      message: 'Critical error',
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}