import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { email } from '@/lib/email'
import { log } from '@/lib/log'
import { z } from 'zod'

const schema = z.object({
  partnerId: z.string().uuid(),
  status: z.enum(['active', 'rejected']),
  reason: z.string().optional(),
  tier: z.enum(['trusted', 'elite', 'diamond']).optional()
})

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const client = await createClient()
    
    // Check if user has admin access
    const { data: user } = await client
      .from('partners_users')
      .select('role')
      .eq('clerk_user_id', userId)
      .single()

    if (!user || !['super_admin', 'ops'].includes(user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const { partnerId, status, reason, tier } = schema.parse(body)

    // Get partner details
    const { data: partner } = await client
      .from('partners_profiles')
      .select('email, full_name, status as current_status')
      .eq('id', partnerId)
      .single()

    if (!partner) {
      return NextResponse.json({ error: 'Partner not found' }, { status: 404 })
    }

    // Update partner status
    const { error: updateError } = await client
      .from('partners_profiles')
      .update({ 
        status, 
        tier: status === 'active' ? tier : partner.tier,
        updated_at: new Date().toISOString()
      })
      .eq('id', partnerId)

    if (updateError) {
      throw updateError
    }

    // Send notification email
    const loginUrl = `${process.env.NEXT_PUBLIC_APP_URL}/sign-in`
    
    if (status === 'active') {
      // Ensure active referral link exists for this partner
      try {
        const { data: existingActive } = await client
          .from('partners_referral_links')
          .select('id, slug, active')
          .eq('partner_id', partnerId)
          .eq('active', true)
          .maybeSingle()

        if (!existingActive) {
          const baseRaw = partner.full_name || (partner.email ? partner.email.split('@')[0] : 'partner')
          const base = String(baseRaw)
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-|-$/g, '')
            .slice(0, 20) || 'partner'

          let slug = base
          let counter = 1
          while (true) {
            const { data: slugExists } = await client
              .from('partners_referral_links')
              .select('id')
              .eq('slug', slug)
              .maybeSingle()
            if (!slugExists) break
            slug = `${base}-${counter++}`
          }

          const { error: createLinkError } = await client
            .from('partners_referral_links')
            .insert({
              partner_id: partnerId,
              slug,
              active: true,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
          if (createLinkError) {
            log.error('Failed to create referral link on activation', { partnerId, error: createLinkError.message })
          } else {
            log.info('Referral link created on activation', { partnerId, slug })
          }
        }
      } catch (linkError) {
        log.error('Exception ensuring referral link on activation', { partnerId, error: (linkError as any)?.message })
      }

      const template = email.templates.partnerApplicationApproved({
        partnerName: partner.full_name,
        loginUrl,
        tier: tier || 'trusted'
      })

      await email.send({
        to: partner.email,
        subject: template.subject,
        html: template.html
      })
    } else if (status === 'rejected') {
      const template = email.templates.partnerApplicationRejected({
        partnerName: partner.full_name,
        reason
      })
      
      await email.send({
        to: partner.email,
        subject: template.subject,
        html: template.html
      })
    }

    log.info('Partner status updated and notification sent', {
      partnerId,
      status,
      email: partner.email,
      previousStatus: partner.current_status
    })

    return NextResponse.json({ 
      success: true, 
      message: 'Status updated and notification sent' 
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    log.error('Failed to update partner status', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}