import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { email } from '@/lib/email'
import { log } from '@/lib/log'
import { z } from 'zod'

const schema = z.object({
  dealId: z.string().uuid(),
  status: z.enum(['pending', 'confirmed', 'rejected', 'paid']),
  statusMessage: z.string().optional()
})

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const client = await createClient()
    
    // Check if user has admin access
    const { data: user } = await client
      .from('partners_users')
      .select('role')
      .eq('clerk_user_id', userId)
      .single()

    if (!user || !['super_admin', 'ops', 'sales'].includes(user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const { dealId, status, statusMessage } = schema.parse(body)

    // Get deal and partner details
    const { data: deal } = await client
      .from('partners_deals')
      .select(`
        id,
        client_company,
        deal_value,
        commission_amount,
        status as current_status,
        partners_profiles (
          email,
          full_name
        )
      `)
      .eq('id', dealId)
      .single()

    if (!deal) {
      return NextResponse.json({ error: 'Deal not found' }, { status: 404 })
    }

    const partner = Array.isArray(deal.partners_profiles) 
      ? deal.partners_profiles[0] 
      : deal.partners_profiles

    if (!partner) {
      return NextResponse.json({ error: 'Partner not found for deal' }, { status: 404 })
    }

    // Update deal status
    const { error: updateError } = await client
      .from('partners_deals')
      .update({ 
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', dealId)

    if (updateError) {
      throw updateError
    }

    // Send notification email only for significant status changes
    const shouldNotify = status !== deal.current_status && 
      ['confirmed', 'rejected', 'paid'].includes(status)

    if (shouldNotify) {
      const template = email.templates.dealStatusUpdate({
        partnerName: partner.full_name,
        clientCompany: deal.client_company,
        dealValue: deal.deal_value,
        commissionAmount: deal.commission_amount,
        status,
        statusMessage: statusMessage || getDefaultStatusMessage(status)
      })
      
      await email.send({
        to: partner.email,
        subject: template.subject,
        html: template.html
      })

      log.info('Deal status updated and notification sent', {
        dealId,
        status,
        previousStatus: deal.current_status,
        partnerEmail: partner.email,
        clientCompany: deal.client_company
      })
    } else {
      log.info('Deal status updated (no notification sent)', {
        dealId,
        status,
        previousStatus: deal.current_status,
        reason: shouldNotify ? 'Status unchanged' : 'Non-notifiable status'
      })
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Status updated' + (shouldNotify ? ' and notification sent' : ''),
      notificationSent: shouldNotify
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    log.error('Failed to update deal status', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function getDefaultStatusMessage(status: string): string {
  switch (status) {
    case 'confirmed':
      return 'Your referral has been confirmed! Commission will be processed according to payment terms.'
    case 'rejected':
      return 'Unfortunately, this deal did not proceed. Thank you for the referral.'
    case 'paid':
      return 'Commission has been processed and paid. Thank you for your successful referral!'
    default:
      return 'Deal status has been updated.'
  }
}