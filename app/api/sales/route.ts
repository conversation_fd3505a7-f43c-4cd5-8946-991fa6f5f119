import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { log } from '@/lib/log'

export async function GET(request: NextRequest) {
  try {
    const client = await createClient(true) // Use service role for public access

    // Get all active sales team members for the onboarding form
    const { data: salesTeam, error } = await client
      .from('partners_sales')
      .select(`
        id,
        name,
        email,
        telegram_handle,
        telegram_link
      `)
      .eq('is_active', true)
      .order('name')

    if (error) {
      log.error('Failed to fetch sales team', { error })
      return NextResponse.json(
        { error: 'Failed to fetch sales team' },
        { status: 500 }
      )
    }

    // Return public information only (no sensitive data)
    const publicSalesData = salesTeam.map(member => ({
      id: member.id,
      name: member.name,
      telegramHandle: member.telegram_handle,
      // Only include first part of email for display purposes
      emailPrefix: member.email.split('@')[0]
    }))

    return NextResponse.json({
      success: true,
      data: publicSalesData
    })

  } catch (error) {
    log.error('Failed to fetch sales team', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Admin-only endpoints for managing sales team
export async function POST(request: NextRequest) {
  try {
    // This would be for creating new sales team members
    // Implementing admin authentication would be needed
    return NextResponse.json(
      { error: 'Admin endpoint - authentication required' },
      { status: 401 }
    )
  } catch (error) {
    log.error('Failed to create sales team member', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}