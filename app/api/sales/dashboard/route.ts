import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { log } from '@/lib/log'
import { SalesDashboardData } from '@/lib/types/sales'

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30d'

    // Calculate date range based on period
    const now = new Date()
    let startDate: Date
    
    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      default: // 30d
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    const client = await createClient()

    // Get sales person info by matching user's email
    const { data: user } = await client.auth.getUser()
    if (!user.user?.email) {
      return NextResponse.json({ error: 'User email not found' }, { status: 403 })
    }

    const { data: salesPerson } = await client
      .from('partners_sales')
      .select('id, name, email, referral_slug, is_active')
      .eq('email', user.user.email)
      .eq('is_active', true)
      .single()

    if (!salesPerson) {
      return NextResponse.json({ 
        error: 'Access denied: You are not registered as an active sales team member' 
      }, { status: 403 })
    }

    // Get referral links and their stats
    const { data: referralLinks } = await client
      .from('partners_sales_referral_links')
      .select('id, slug, clicks, active, created_at')
      .eq('sales_id', salesPerson.id)

    // Get attribution data for the period
    const { data: attributions } = await client
      .from('partners_referral_attribution')
      .select(`
        id, 
        converted, 
        created_at, 
        lead_id,
        sales_referral_slug
      `)
      .eq('sales_id', salesPerson.id)
      .gte('created_at', startDate.toISOString())

    // Get monthly data (last 30 days for comparison)
    const monthStart = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    const { data: monthlyAttributions } = await client
      .from('partners_referral_attribution')
      .select('id, converted, created_at')
      .eq('sales_id', salesPerson.id)
      .gte('created_at', monthStart.toISOString())

    // Get assigned partners
    const { data: assignedPartners } = await client
      .from('partners_teams_assignments')
      .select(`
        partners_profiles!inner(
          id,
          full_name,
          company_name,
          tier,
          status
        )
      `)
      .eq('sales_user_id', salesPerson.id)
      .eq('active', true)

    // Calculate personal stats
    const totalClicks = attributions?.length || 0
    const totalConversions = attributions?.filter(a => a.converted).length || 0
    const monthlyClicks = monthlyAttributions?.length || 0
    const monthlyConversions = monthlyAttributions?.filter(a => a.converted).length || 0

    const personalStats = {
      totalClicks,
      totalConversions,
      conversionRate: totalClicks > 0 ? totalConversions / totalClicks : 0,
      monthlyClicks,
      monthlyConversions
    }

    // Process referral links
    const referralLinksData = referralLinks?.map(link => ({
      slug: link.slug,
      clicks: link.clicks,
      conversions: attributions?.filter(a => a.sales_referral_slug === link.slug && a.converted).length || 0,
      isActive: link.active,
      createdAt: link.created_at
    })) || []

    // Generate recent activity (last 20 items)
    const recentActivity = attributions
      ?.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 20)
      .map(attribution => ({
        type: attribution.converted ? 'conversion' as const : 'click' as const,
        timestamp: attribution.created_at,
        slug: attribution.sales_referral_slug || 'unknown',
        metadata: {
          leadId: attribution.lead_id
        }
      })) || []

    // Process assigned partners with placeholder deal stats (would need proper aggregation)
    const partnersData = assignedPartners?.map(assignment => ({
      id: assignment.partners_profiles.id,
      name: assignment.partners_profiles.full_name,
      company: assignment.partners_profiles.company_name,
      tier: assignment.partners_profiles.tier as 'trusted' | 'elite' | 'diamond',
      status: assignment.partners_profiles.status,
      totalDeals: 0, // Would need proper aggregation from deals table
      totalCommission: 0 // Would need proper aggregation from deals/earnings table
    })) || []

    const dashboardData: SalesDashboardData = {
      personalStats,
      referralLinks: referralLinksData,
      recentActivity,
      assignedPartners: partnersData
    }

    return NextResponse.json({
      success: true,
      data: dashboardData
    })

  } catch (error) {
    log.error('Failed to get sales dashboard data', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}