import { NextRequest, NextResponse } from 'next/server'
import { Webhook } from 'svix'
import { headers } from 'next/headers'
import { createClient } from '@/lib/supabase/server'
import { log } from '@/lib/log'

interface ClerkWebhookEvent {
  type: string
  data: {
    id: string
    email_addresses?: Array<{
      email_address: string
      verification?: {
        status: string
      }
    }>
    first_name?: string
    last_name?: string
    created_at: number
    updated_at: number
    // Session-specific fields
    user_id?: string
    client_id?: string
    status?: string
    expire_at?: number
    abandon_at?: number
    last_active_at?: number
    actor?: any
    user?: {
      id: string
      email_addresses: Array<{
        email_address: string
      }>
      first_name?: string
      last_name?: string
    }
  }
}

export async function POST(req: NextRequest) {
  const headerPayload = await headers()
  const svixId = headerPayload.get('svix-id')
  const svixTimestamp = headerPayload.get('svix-timestamp')
  const svixSignature = headerPayload.get('svix-signature')

  if (!svixId || !svixTimestamp || !svixSignature) {
    log.error('Clerk webhook: Missing svix headers', {
      action: 'webhook_validation',
      hasId: !!svixId,
      hasTimestamp: !!svixTimestamp,
      hasSignature: !!svixSignature
    })
    return NextResponse.json({ error: 'Missing svix headers' }, { status: 400 })
  }

  const payload = await req.text()
  
  // Verify webhook signature
  const wh = new Webhook(process.env.CLERK_WEBHOOK_SECRET!)
  let evt: ClerkWebhookEvent

  try {
    evt = wh.verify(payload, {
      'svix-id': svixId,
      'svix-timestamp': svixTimestamp,
      'svix-signature': svixSignature,
    }) as ClerkWebhookEvent
  } catch (err) {
    log.error('Clerk webhook: Signature verification failed', { 
      error: err instanceof Error ? err.message : String(err),
      action: 'webhook_verification'
    })
    return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
  }

  const { id, email_addresses, first_name, last_name } = evt.data
  const email = email_addresses?.[0]?.email_address

  log.info('Clerk webhook received', {
    action: 'webhook_received',
    eventType: evt.type,
    userId: id,
    email: email
  })

  try {
    switch (evt.type) {
      case 'user.created':
        await handleUserCreated(id, email, first_name, last_name)
        break
      
      case 'user.updated':
        await handleUserUpdated(id, email, first_name, last_name)
        break
        
      case 'user.deleted':
        await handleUserDeleted(id)
        break

      // Session events for sign-in tracking
      case 'session.created':
        await handleSessionCreated(evt.data)
        break

      case 'session.ended':
        await handleSessionEnded(evt.data)
        break

      case 'session.revoked':
        await handleSessionRevoked(evt.data)
        break

      case 'session.removed':
        await handleSessionRemoved(evt.data)
        break
        
      default:
        log.debug('Unhandled webhook event type', { 
          eventType: evt.type,
          sessionId: evt.data.id,
          userId: evt.data.user_id || id 
        })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    log.error('Error processing Clerk webhook', {
      error: error instanceof Error ? error.message : String(error),
      eventType: evt.type,
      userId: id,
      email: email
    })
    return NextResponse.json({ error: 'Processing failed' }, { status: 500 })
  }
}

async function handleUserCreated(userId: string, email: string, firstName?: string, lastName?: string) {
  log.info('Processing user.created event', { 
    userId, 
    metadata: { email }
  })
  
  if (!email) {
    log.warn('User created without email', { userId })
    return
  }

  try {
    const client = await createClient(true) // Use service role

    // Check if email is whitelisted
    const { data: whitelist } = await client
      .from('partners_email_whitelist')
      .select('*')
      .eq('email', email.toLowerCase())
      .single()

    if (!whitelist || !whitelist.active) {
      log.info('User not whitelisted or inactive', { 
        userId, 
        metadata: { email }
      })
      
      // Create pending profile
      await client
        .from('partners_profiles')
        .insert({
          id: userId,
          email: email,
          full_name: `${firstName || ''} ${lastName || ''}`.trim(),
          status: 'pending',
          created_at: new Date().toISOString()
        })

      return
    }

    // Create active profile for whitelisted email
    const { error } = await client
      .from('partners_profiles')
      .insert({
        id: userId,
        email: email,
        full_name: `${firstName || ''} ${lastName || ''}`.trim(),
        status: 'active',
        tier: 'trusted', // Default tier
        created_at: new Date().toISOString()
      })

    if (error) {
      log.error('Failed to create partner profile', { userId, error: error.message })
      return
    }

    // Ensure referral link exists for this active profile
    try {
      const { data: existingActive } = await client
        .from('partners_referral_links')
        .select('id, slug, active')
        .eq('partner_id', userId)
        .eq('active', true)
        .maybeSingle()

      if (!existingActive) {
        const baseRaw = `${firstName || ''}${lastName ? '-' + lastName : ''}`.trim() || (email ? email.split('@')[0] : 'partner')
        const base = String(baseRaw)
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/^-|-$/g, '')
          .slice(0, 20) || 'partner'

        let slug = base
        let counter = 1
        while (true) {
          const { data: slugExists } = await client
            .from('partners_referral_links')
            .select('id')
            .eq('slug', slug)
            .maybeSingle()
          if (!slugExists) break
          slug = `${base}-${counter++}`
        }

        const { error: createLinkError } = await client
          .from('partners_referral_links')
          .insert({
            partner_id: userId,
            slug,
            active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })

        if (createLinkError) {
          log.error('Failed to create referral link on whitelisted activation', { userId, error: createLinkError.message })
        } else {
          log.info('Referral link created on whitelisted activation', { userId, slug })
        }
      }
    } catch (linkError) {
      log.error('Exception ensuring referral link on whitelisted activation', { userId, error: (linkError as any)?.message })
    }

    // Send welcome notification
    await client
      .from('partners_notifications')
      .insert({
        recipient_id: userId,
        notification_type: 'welcome',
        title: 'Welcome to IBC Partner Portal',
        message: 'Your account has been activated. Start by creating your first referral link!'
      })

    log.businessEvent('user_created_and_activated', {
      userId,
      email,
      firstName,
      lastName
    })
    
  } catch (error) {
    log.error('Error in handleUserCreated', { userId, email, error })
  }
}

async function handleUserUpdated(userId: string, email: string, firstName?: string, lastName?: string) {
  log.info('Processing user.updated event', { userId, email })
  
  try {
    const client = await createClient(true)

    await client
      .from('partners_profiles')
      .update({
        email: email,
        full_name: `${firstName || ''} ${lastName || ''}`.trim(),
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)

    log.businessEvent('user_updated', {
      userId,
      email,
      firstName,
      lastName
    })
    
  } catch (error) {
    log.error('Error in handleUserUpdated', { userId, email, error })
  }
}

async function handleUserDeleted(userId: string) {
  log.warn('Processing user.deleted event', { userId })
  
  try {
    const client = await createClient(true)

    // Soft delete the profile
    await client
      .from('partners_profiles')
      .update({
        status: 'deleted',
        deleted_at: new Date().toISOString()
      })
      .eq('id', userId)

    log.businessEvent('user_deleted', { userId })
    
  } catch (error) {
    log.error('Error in handleUserDeleted', { userId, error })
  }
}

async function handleSessionCreated(data: ClerkWebhookEvent['data']) {
  log.info('Processing session.created event', { 
    sessionId: data.id, 
    userId: data.user_id 
  })
  
  try {
    const client = await createClient(true)
    
    const userId = data.user_id || data.user?.id
    const email = data.user?.email_addresses?.[0]?.email_address
    
    if (!userId || !email) {
      log.warn('Session created without user ID or email', { 
        sessionId: data.id,
        userId,
        email 
      })
      return
    }

    // Extract client info from actor if available
    const clientInfo = data.actor ? extractClientInfo(data.actor) : {}
    
    const signInRecord = {
      user_id: userId,
      email: email,
      session_id: data.id,
      expires_at: data.expire_at ? new Date(data.expire_at).toISOString() : null,
      status: 'active',
      sign_in_method: clientInfo.method || 'unknown',
      client_name: clientInfo.client_name,
      client_version: clientInfo.client_version,
      device_type: clientInfo.device_type,
      ip_address: clientInfo.ip_address,
      user_agent: clientInfo.user_agent,
      country: clientInfo.country,
      city: clientInfo.city,
      created_at: new Date().toISOString()
    }

    const { error } = await client
      .from('partners_sign_ins')
      .insert(signInRecord)

    if (error) {
      log.error('Failed to record sign-in', { 
        sessionId: data.id, 
        userId, 
        error: error.message 
      })
      return
    }

    log.businessEvent('user_signed_in', {
      userId,
      sessionId: data.id,
      email,
      method: signInRecord.sign_in_method,
      device: signInRecord.device_type
    })
    
  } catch (error) {
    log.error('Error in handleSessionCreated', { 
      sessionId: data.id, 
      error 
    })
  }
}

async function handleSessionEnded(data: ClerkWebhookEvent['data']) {
  log.info('Processing session.ended event', { sessionId: data.id })
  
  try {
    const client = await createClient(true)

    const { error } = await client
      .from('partners_sign_ins')
      .update({ 
        status: 'ended',
        updated_at: new Date().toISOString()
      })
      .eq('session_id', data.id)

    if (error) {
      log.error('Failed to update session status to ended', { 
        sessionId: data.id, 
        error: error.message 
      })
      return
    }

    log.businessEvent('user_session_ended', {
      sessionId: data.id,
      userId: data.user_id
    })
    
  } catch (error) {
    log.error('Error in handleSessionEnded', { 
      sessionId: data.id, 
      error 
    })
  }
}

async function handleSessionRevoked(data: ClerkWebhookEvent['data']) {
  log.info('Processing session.revoked event', { sessionId: data.id })
  
  try {
    const client = await createClient(true)

    const { error } = await client
      .from('partners_sign_ins')
      .update({ 
        status: 'revoked',
        updated_at: new Date().toISOString()
      })
      .eq('session_id', data.id)

    if (error) {
      log.error('Failed to update session status to revoked', { 
        sessionId: data.id, 
        error: error.message 
      })
      return
    }

    log.businessEvent('user_session_revoked', {
      sessionId: data.id,
      userId: data.user_id
    })
    
  } catch (error) {
    log.error('Error in handleSessionRevoked', { 
      sessionId: data.id, 
      error 
    })
  }
}

async function handleSessionRemoved(data: ClerkWebhookEvent['data']) {
  log.info('Processing session.removed event', { sessionId: data.id })
  
  try {
    const client = await createClient(true)

    const { error } = await client
      .from('partners_sign_ins')
      .update({ 
        status: 'removed',
        updated_at: new Date().toISOString()
      })
      .eq('session_id', data.id)

    if (error) {
      log.error('Failed to update session status to removed', { 
        sessionId: data.id, 
        error: error.message 
      })
      return
    }

    log.businessEvent('user_session_removed', {
      sessionId: data.id,
      userId: data.user_id
    })
    
  } catch (error) {
    log.error('Error in handleSessionRemoved', { 
      sessionId: data.id, 
      error 
    })
  }
}

function extractClientInfo(actor: any): {
  method?: string
  client_name?: string
  client_version?: string
  device_type?: string
  ip_address?: string
  user_agent?: string
  country?: string
  city?: string
} {
  if (!actor) return {}
  
  return {
    method: actor.strategy || 'unknown',
    client_name: actor.client?.name || 'unknown',
    client_version: actor.client?.version,
    device_type: actor.device?.type || detectDeviceType(actor.user_agent),
    ip_address: actor.ip_address,
    user_agent: actor.user_agent,
    country: actor.geo?.country,
    city: actor.geo?.city
  }
}

function detectDeviceType(userAgent?: string): string {
  if (!userAgent) return 'unknown'
  
  const ua = userAgent.toLowerCase()
  
  if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
    return 'mobile'
  } else if (ua.includes('tablet') || ua.includes('ipad')) {
    return 'tablet'
  } else {
    return 'desktop'
  }
}