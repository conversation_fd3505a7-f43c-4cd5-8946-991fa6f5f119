import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { email } from '@/lib/email'
import { log } from '@/lib/log'
import { z } from 'zod'

const webhookSchema = z.object({
  type: z.enum(['INSERT', 'UPDATE', 'DELETE']),
  table: z.string(),
  record: z.record(z.any()),
  old_record: z.record(z.any()).optional(),
  schema: z.string()
})

export async function POST(request: NextRequest) {
  try {
    // Verify webhook signature (if configured)
    const signature = request.headers.get('x-supabase-signature')
    const expectedSignature = process.env.SUPABASE_WEBHOOK_SECRET
    
    if (expectedSignature && signature !== expectedSignature) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
    }

    const body = await request.json()
    const webhook = webhookSchema.parse(body)

    log.info('Received Supabase webhook', {
      type: webhook.type,
      table: webhook.table,
      recordId: webhook.record?.id
    })

    // Handle partner profile status changes
    if (webhook.table === 'partners_profiles' && webhook.type === 'UPDATE') {
      await handlePartnerStatusChange(webhook)
    }

    // Handle deal status changes
    if (webhook.table === 'partners_deals' && webhook.type === 'UPDATE') {
      await handleDealStatusChange(webhook)
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    log.error('Webhook processing failed', { error })
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    )
  }
}

async function handlePartnerStatusChange(webhook: any) {
  const oldStatus = webhook.old_record?.status
  const newStatus = webhook.record?.status
  
  // Only send notifications for status changes
  if (oldStatus === newStatus) return

  const partner = webhook.record
  const loginUrl = `${process.env.NEXT_PUBLIC_APP_URL}/sign-in`

  try {
    if (newStatus === 'active' && oldStatus === 'pending') {
      const template = email.templates.partnerApplicationApproved({
        partnerName: partner.full_name,
        loginUrl,
        tier: partner.tier || 'trusted'
      })
      
      await email.send({
        to: partner.email,
        subject: template.subject,
        html: template.html
      })

      log.info('Partner approval notification sent', {
        partnerId: partner.id,
        email: partner.email
      })
    } else if (newStatus === 'rejected' && oldStatus === 'pending') {
      const template = email.templates.partnerApplicationRejected({
        partnerName: partner.full_name
      })
      
      await email.send({
        to: partner.email,
        subject: template.subject,
        html: template.html
      })

      log.info('Partner rejection notification sent', {
        partnerId: partner.id,
        email: partner.email
      })
    }
  } catch (error) {
    log.error('Failed to send partner status notification', {
      partnerId: partner.id,
      oldStatus,
      newStatus,
      error
    })
  }
}

async function handleDealStatusChange(webhook: any) {
  const oldStatus = webhook.old_record?.status
  const newStatus = webhook.record?.status
  
  // Only notify for significant status changes
  if (oldStatus === newStatus || !['confirmed', 'rejected', 'paid'].includes(newStatus)) {
    return
  }

  const deal = webhook.record

  try {
    // Get partner details
    const client = await createClient(true) // Use service role
    const { data: partner } = await client
      .from('partners_profiles')
      .select('email, full_name')
      .eq('id', deal.partner_id)
      .single()

    if (!partner) {
      log.error('Partner not found for deal notification', { dealId: deal.id })
      return
    }

    const template = email.templates.dealStatusUpdate({
      partnerName: partner.full_name,
      clientCompany: deal.client_company,
      dealValue: deal.deal_value,
      commissionAmount: deal.commission_amount,
      status: newStatus,
      statusMessage: getDefaultStatusMessage(newStatus)
    })
    
    await email.send({
      to: partner.email,
      subject: template.subject,
      html: template.html
    })

    log.info('Deal status notification sent', {
      dealId: deal.id,
      partnerEmail: partner.email,
      oldStatus,
      newStatus
    })

  } catch (error) {
    log.error('Failed to send deal status notification', {
      dealId: deal.id,
      oldStatus,
      newStatus,
      error
    })
  }
}

function getDefaultStatusMessage(status: string): string {
  switch (status) {
    case 'confirmed':
      return 'Your referral has been confirmed! Commission will be processed according to payment terms.'
    case 'rejected':
      return 'Unfortunately, this deal did not proceed. Thank you for the referral.'
    case 'paid':
      return 'Commission has been processed and paid. Thank you for your successful referral!'
    default:
      return 'Deal status has been updated.'
  }
}