import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { withdrawalRequestSchema, withdrawalFilterSchema } from '@/lib/validations'
import { requirePermission, canAccess<PERSON>artner, getUserContext } from '@/lib/rbac'
import { log } from '@/lib/log'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const filters = Object.fromEntries(searchParams.entries())
    
    const validation = withdrawalFilterSchema.safeParse(filters)
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Invalid filters',
        details: validation.error.issues 
      }, { status: 400 })
    }

    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userContext = await getUserContext()
    const client = await createClient()

    // Build query based on user role
    let query = client
      .from('partners_withdrawals')
      .select(`
        *,
        partners_profiles(full_name, company_name, email),
        partners_withdrawal_methods(method_type)
      `)

    // Apply role-based filtering
    if (userContext?.role === 'partner') {
      await requirePermission('view_own_withdrawals')
      query = query.eq('partner_id', userId)
    } else {
      await requirePermission('view_financial_data')
      
      if (userContext?.role === 'sales') {
        // Sales users see withdrawals from their assigned partners
        const { data: assignments } = await client
          .from('partners_teams_assignments')
          .select('partner_id')
          .eq('sales_user_id', userId)
          .eq('active', true)

        const partnerIds = assignments?.map(a => a.partner_id) || []
        if (partnerIds.length > 0) {
          query = query.in('partner_id', partnerIds)
        } else {
          return NextResponse.json({ data: [], pagination: { page: 1, total: 0, pages: 0 } })
        }
      }
    }

    // Apply user filters
    if (validation.data.partner_id) {
      if (await canAccessPartner(validation.data.partner_id)) {
        query = query.eq('partner_id', validation.data.partner_id)
      } else {
        return NextResponse.json({ error: 'Access denied to partner data' }, { status: 403 })
      }
    }
    if (validation.data.status) {
      query = query.eq('status', validation.data.status)
    }
    if (validation.data.method_type) {
      query = query.eq('partners_withdrawal_methods.method_type', validation.data.method_type)
    }
    if (validation.data.min_amount) {
      query = query.gte('amount', validation.data.min_amount)
    }
    if (validation.data.max_amount) {
      query = query.lte('amount', validation.data.max_amount)
    }
    if (validation.data.search) {
      query = query.or(`
        partners_profiles.company_name.ilike.%${validation.data.search}%,
        notes.ilike.%${validation.data.search}%
      `)
    }
    if (validation.data.date_from) {
      query = query.gte('created_at', validation.data.date_from)
    }
    if (validation.data.date_to) {
      query = query.lte('created_at', validation.data.date_to)
    }

    // Get total count for pagination - clone the query for count
    const countQuery = client
      .from('partners_withdrawals')
      .select('*', { count: 'exact', head: true })
    
    // Apply the same filters to count query as main query  
    if (userContext?.role === 'partner') {
      countQuery.eq('partner_id', userId)
    } else if (userContext?.role === 'sales') {
      const { data: assignments } = await client
        .from('partners_teams_assignments')
        .select('partner_id')
        .eq('sales_user_id', userId)
        .eq('active', true)
      
      const partnerIds = assignments?.map(a => a.partner_id) || []
      if (partnerIds.length > 0) {
        countQuery.in('partner_id', partnerIds)
      }
    }
    
    if (validation.data.partner_id) {
      countQuery.eq('partner_id', validation.data.partner_id)
    }
    if (validation.data.status) {
      countQuery.eq('status', validation.data.status)
    }
    if (validation.data.min_amount) {
      countQuery.gte('amount', validation.data.min_amount)
    }
    if (validation.data.max_amount) {
      countQuery.lte('amount', validation.data.max_amount)
    }
    if (validation.data.date_from) {
      countQuery.gte('created_at', validation.data.date_from)
    }
    if (validation.data.date_to) {
      countQuery.lte('created_at', validation.data.date_to)
    }
    
    const { count } = await countQuery

    // Apply pagination
    const offset = (validation.data.page - 1) * validation.data.limit
    query = query
      .range(offset, offset + validation.data.limit - 1)
      .order('created_at', { ascending: false })

    const { data: withdrawals, error } = await query

    if (error) {
      log.error('Failed to fetch withdrawals', { userId, error: error.message })
      return NextResponse.json({ error: 'Failed to fetch withdrawals' }, { status: 500 })
    }

    const pagination = {
      page: validation.data.page,
      limit: validation.data.limit,
      total: count || 0,
      pages: Math.ceil((count || 0) / validation.data.limit)
    }

    log.info('Withdrawals fetched', { 
      userId, 
      metadata: { count: withdrawals?.length || 0 }
    })
    return NextResponse.json({ data: withdrawals, pagination })

  } catch (error) {
    log.error('Withdrawals GET error', { error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    await requirePermission('create_withdrawal_request')
    
    const body = await request.json()
    const validation = withdrawalRequestSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Validation failed',
        details: validation.error.issues 
      }, { status: 400 })
    }

    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const client = await createClient()

    // Verify withdrawal method belongs to user
    const { data: withdrawalMethod } = await client
      .from('partners_withdrawal_methods')
      .select('*')
      .eq('id', validation.data.withdrawal_method_id)
      .eq('partner_id', userId)
      .single()

    if (!withdrawalMethod) {
      return NextResponse.json({ error: 'Invalid withdrawal method' }, { status: 400 })
    }

    // Check minimum withdrawal amount (from system settings)
    const { data: minAmountSetting } = await client
      .from('partners_system_settings')
      .select('setting_value')
      .eq('setting_key', 'min_withdrawal_amount')
      .single()

    const minAmount = minAmountSetting?.setting_value || 100
    if (validation.data.amount < minAmount) {
      return NextResponse.json({ 
        error: `Minimum withdrawal amount is $${minAmount}` 
      }, { status: 400 })
    }

    // Calculate fees
    const { data: feePercentageSetting } = await client
      .from('partners_system_settings')
      .select('setting_value')
      .eq('setting_key', 'withdrawal_fee_percentage')
      .single()

    const feePercentage = feePercentageSetting?.setting_value || 0.02
    const feeAmount = Math.round(validation.data.amount * feePercentage * 100) / 100
    const netAmount = validation.data.amount - feeAmount

    // Check available balance
    const { data: availableBalance } = await client.rpc('get_partner_earnings', {
      partner_id: userId
    })

    const pendingBalance = availableBalance?.total_earned - availableBalance?.total_withdrawn || 0
    if (validation.data.amount > pendingBalance) {
      return NextResponse.json({ 
        error: `Insufficient balance. Available: $${pendingBalance}` 
      }, { status: 400 })
    }

    // Create withdrawal request
    const { data: withdrawal, error } = await client
      .from('partners_withdrawals')
      .insert({
        partner_id: userId,
        withdrawal_method_id: validation.data.withdrawal_method_id,
        amount: validation.data.amount,
        currency: validation.data.currency,
        fee_amount: feeAmount,
        net_amount: netAmount
      })
      .select()
      .single()

    if (error) {
      log.error('Failed to create withdrawal', { userId, error: error.message })
      return NextResponse.json({ error: 'Failed to create withdrawal' }, { status: 500 })
    }

    // Create withdrawal items if specific deals were selected
    if (validation.data.deal_ids && validation.data.deal_ids.length > 0) {
      const { data: deals } = await client
        .from('partners_deals')
        .select('id, commission_amount')
        .in('id', validation.data.deal_ids)
        .eq('partner_id', userId)
        .eq('status', 'paid')

      if (deals) {
        const withdrawalItems = deals.map(deal => ({
          withdrawal_id: withdrawal.id,
          deal_id: deal.id,
          commission_amount: deal.commission_amount
        }))

        await client
          .from('partners_withdrawal_items')
          .insert(withdrawalItems)
      }
    }

    // Log audit event
    await client.rpc('log_audit_event', {
      p_user_id: userId,
      p_user_type: 'partner',
      p_action: 'create_withdrawal',
      p_resource_type: 'withdrawal',
      p_resource_id: withdrawal.id,
      p_old_values: null,
      p_new_values: withdrawal
    })

    log.info('Withdrawal requested', { 
      userId,
      metadata: {
        withdrawalId: withdrawal.id,
        amount: withdrawal.amount,
        netAmount: withdrawal.net_amount
      }
    })

    return NextResponse.json({ data: withdrawal }, { status: 201 })

  } catch (error) {
    log.error('Withdrawals POST error', { error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}