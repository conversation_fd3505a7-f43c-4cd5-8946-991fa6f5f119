import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { dealFilterSchema } from '@/lib/validations'
import { getCurrentUserRole, requirePermission } from '@/lib/rbac'
import { log } from '@/lib/log'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const filters = Object.fromEntries(searchParams.entries())
    
    // Apply default values for pagination
    const filtersWithDefaults = {
      page: '1',
      limit: '20',
      ...filters
    }
    
    const validation = dealFilterSchema.safeParse(filtersWithDefaults)
    if (!validation.success) {
      log.error('Deal filter validation failed', {
        error: validation.error.issues,
        metadata: { filters: filtersWithDefaults }
      })
      return NextResponse.json({ 
        error: 'Invalid filters',
        details: validation.error.issues 
      }, { status: 400 })
    }

    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check user permissions
    const role = await getCurrentUserRole()
    if (!role) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Verify user has permission to view deals
    try {
      if (role === 'partner') {
        await requirePermission('view_own_deals')
      } else {
        await requirePermission('view_partner_analytics')
      }
    } catch (error) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Use service role for deals queries (role check already done above)
    const client = await createClient(true)
    
    log.debug('Fetching deals for user', { 
      userId,
      metadata: { role, filters: validation.data }
    })

    // RLS policies will automatically filter based on user role
    let query = client
      .from('partners_deals')
      .select(`
        id,
        partner_id,
        lead_id,
        client_company,
        client_poc,
        deal_value,
        commission_amount,
        commission_rate,
        status,
        payment_date,
        invoice_required,
        invoice_submitted,
        notes,
        created_at,
        updated_at
      `)

    // Apply user filters
    if (validation.data.status) {
      query = query.eq('status', validation.data.status)
    }
    if (validation.data.min_value) {
      query = query.gte('deal_value', validation.data.min_value)
    }
    if (validation.data.max_value) {
      query = query.lte('deal_value', validation.data.max_value)
    }
    if (validation.data.search) {
      query = query.ilike('client_company', `%${validation.data.search}%`)
    }
    if (validation.data.date_from) {
      query = query.gte('created_at', validation.data.date_from)
    }
    if (validation.data.date_to) {
      query = query.lte('created_at', validation.data.date_to)
    }

    // Get total count for pagination
    const { count, error: countError } = await client
      .from('partners_deals')
      .select('*', { count: 'exact', head: true })
    
    if (countError) {
      log.error('Failed to count deals', { 
        userId, 
        error: countError.message,
        metadata: { code: countError.code }
      })
      return NextResponse.json({ error: 'Failed to count deals' }, { status: 500 })
    }

    // Apply pagination
    const offset = (validation.data.page - 1) * validation.data.limit
    query = query
      .range(offset, offset + validation.data.limit - 1)
      .order('created_at', { ascending: false })

    const { data: deals, error } = await query

    if (error) {
      log.error('Failed to fetch deals', { 
        userId, 
        error: error.message,
        metadata: { 
          code: error.code,
          hint: error.hint,
          details: error.details
        }
      })
      return NextResponse.json({ error: 'Failed to fetch deals' }, { status: 500 })
    }

    const pagination = {
      page: validation.data.page,
      limit: validation.data.limit,
      total: count || 0,
      pages: Math.ceil((count || 0) / validation.data.limit)
    }

    log.info('Deals fetched successfully', { 
      userId, 
      metadata: {
        count: deals?.length || 0,
        total: count || 0,
        page: validation.data.page
      }
    })
    
    return NextResponse.json({ data: deals || [], pagination })

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    log.error('Deals GET error', { 
      error: errorMessage,
      metadata: {
        stack: error instanceof Error ? error.stack : undefined,
        name: error instanceof Error ? error.name : undefined
      }
    })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // For now, return 501 Not Implemented since this is complex functionality
    // that requires proper role management and database setup
    log.info('Deal creation attempted', { userId })
    return NextResponse.json({ 
      error: 'Deal creation not yet implemented',
      message: 'This feature will be available once role management is fully configured'
    }, { status: 501 })

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    log.error('Deals POST error', { 
      error: errorMessage,
      metadata: {
        stack: error instanceof Error ? error.stack : undefined
      }
    })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}