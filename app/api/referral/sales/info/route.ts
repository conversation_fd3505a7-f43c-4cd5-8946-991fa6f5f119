import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { log } from '@/lib/log'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const slug = searchParams.get('slug')

    if (!slug) {
      return NextResponse.json(
        { error: 'Slug parameter is required' },
        { status: 400 }
      )
    }

    const client = await createClient(true) // Use service role for public access

    // Get sales person info from slug
    const { data: salesPerson } = await client
      .from('partners_sales')
      .select(`
        id,
        name,
        email,
        referral_slug,
        telegram_handle,
        telegram_link,
        is_active
      `)
      .eq('referral_slug', slug)
      .eq('is_active', true)
      .single()

    if (!salesPerson) {
      return NextResponse.json(
        { error: 'Sales referral link not found or inactive' },
        { status: 404 }
      )
    }

    // Return only public information
    return NextResponse.json({
      id: salesPerson.id,
      name: sales<PERSON>erson.name,
      telegramHandle: salesPerson.telegram_handle,
      telegramLink: salesPerson.telegram_link,
      slug: salesPerson.referral_slug,
      type: 'sales'
    })

  } catch (error) {
    log.error('Failed to fetch sales person info', { error, slug })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}