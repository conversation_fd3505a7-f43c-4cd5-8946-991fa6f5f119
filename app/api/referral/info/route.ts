import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { log } from '@/lib/log'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const slug = searchParams.get('slug')

    if (!slug) {
      return NextResponse.json(
        { error: 'Slug parameter is required' },
        { status: 400 }
      )
    }

    const client = await createClient(true) // Use service role for public access

    // Get partner info from slug
    const { data: referralLink } = await client
      .from('partners_referral_links')
      .select(`
        id,
        slug,
        active,
        partners_profiles (
          full_name,
          company_name
        )
      `)
      .eq('slug', slug)
      .eq('active', true)
      .single()

    if (!referralLink) {
      return NextResponse.json(
        { error: 'Referral link not found or inactive' },
        { status: 404 }
      )
    }

    const partner = Array.isArray(referralLink.partners_profiles) 
      ? referralLink.partners_profiles[0] 
      : referralLink.partners_profiles

    if (!partner) {
      return NextResponse.json(
        { error: 'Partner not found' },
        { status: 404 }
      )
    }

    // Return only public information
    return NextResponse.json({
      name: partner.full_name,
      company: partner.company_name,
      slug: referralLink.slug
    })

  } catch (error) {
    log.error('Failed to fetch partner info', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}