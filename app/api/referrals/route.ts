import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { requirePermission, getUserContext } from '@/lib/rbac'
import { log } from '@/lib/log'
import * as QRCode from 'qrcode'
import { z } from 'zod'

// Validation schema for vanity slug requests
const vanitySlugSchema = z.object({
  preferred_slug: z.string()
    .min(3, 'Slug must be at least 3 characters')
    .max(50, 'Slug cannot exceed 50 characters')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens')
    .refine((slug) => !slug.startsWith('-') && !slug.endsWith('-'), 'Slug cannot start or end with hyphens')
})

export async function GET() {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const client = await createClient(true) // Use service role for database access
    
    // Debug: Log the user ID
    log.info('Referrals API called', { userId })
    
    // First verify user exists in partners_profiles
    const { data: profile, error: profileError } = await client
      .from('partners_profiles')
      .select('id, full_name, company_name')
      .eq('id', userId)
      .single()

    if (profileError || !profile) {
      log.error('Partner profile not found', { userId, error: profileError })
      return NextResponse.json({ error: 'Partner profile not found' }, { status: 404 })
    }
    
    // Get user's current referral links
    const { data: referralLinks, error: linksError } = await client
      .from('partners_referral_links')
      .select(`
        id,
        slug,
        active,
        created_at,
        updated_at
      `)
      .eq('partner_id', userId)
      .order('created_at', { ascending: false })

    if (linksError) {
      log.error('Failed to fetch referral links', { userId, error: linksError.message })
      return NextResponse.json({ error: 'Failed to fetch referral links' }, { status: 500 })
    }

    // Debug logging
    log.info('Referral links query result', { 
      userId, 
      referralLinksCount: referralLinks?.length || 0,
      links: referralLinks?.map(link => ({ slug: link.slug, active: link.active })) || []
    })

    // Get the active referral link
    const activeLink = referralLinks?.find(link => link.active)
    
    // If user has an active link, generate QR code
    let qrCodeDataUrl = null
    if (activeLink) {
      const referralUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/referral/${activeLink.slug}`
      try {
        qrCodeDataUrl = await QRCode.toDataURL(referralUrl, {
          width: 200,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#ffffff'
          }
        })
      } catch (qrError) {
        log.error('Failed to generate QR code', { userId, error: qrError })
      }
    }

    // Check for pending vanity slug requests
    const { data: pendingRequests } = await client
      .from('partners_slug_requests')
      .select('*')
      .eq('partner_id', userId)
      .eq('status', 'pending')
      .order('created_at', { ascending: false })

    const response = {
      currentLink: activeLink ? {
        id: activeLink.id,
        slug: activeLink.slug,
        url: `/referral/${activeLink.slug}`,
        qrCode: qrCodeDataUrl,
        createdAt: activeLink.created_at
      } : null,
      history: referralLinks?.map(link => ({
        id: link.id,
        slug: link.slug,
        active: link.active,
        createdAt: link.created_at,
        updatedAt: link.updated_at
      })) || [],
      pendingRequests: pendingRequests || [],
      // Debug info
      debug: {
        userId,
        profileFound: !!profile,
        referralLinksCount: referralLinks?.length || 0,
        activeLinksCount: referralLinks?.filter(link => link.active).length || 0
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    log.error('Referrals GET error', { 
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined
    })
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validation = vanitySlugSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Validation failed',
        details: validation.error.issues 
      }, { status: 400 })
    }

    const { preferred_slug } = validation.data
    const client = await createClient(true) // Use service role for database access

    // Check if slug is already taken
    const { data: existingSlug } = await client
      .from('partners_referral_links')
      .select('slug')
      .eq('slug', preferred_slug)
      .single()

    if (existingSlug) {
      return NextResponse.json({ 
        error: 'Slug is already taken',
        details: 'Please choose a different slug' 
      }, { status: 409 })
    }

    // Check if user already has a pending request
    const { data: existingRequest } = await client
      .from('partners_slug_requests')
      .select('id')
      .eq('partner_id', userId)
      .eq('status', 'pending')
      .single()

    if (existingRequest) {
      return NextResponse.json({ 
        error: 'You already have a pending slug request',
        details: 'Please wait for your current request to be processed' 
      }, { status: 409 })
    }

    // Create the slug request (this will need the table to exist)
    try {
      const { data: slugRequest, error: requestError } = await client
        .from('partners_slug_requests')
        .insert({
          partner_id: userId,
          current_slug: null, // We'll get this from existing link
          requested_slug: preferred_slug,
          status: 'pending',
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (requestError) {
        // Table might not exist yet, create a simple log entry instead
        log.info('Vanity slug request submitted', { 
          userId, 
          preferredSlug: preferred_slug 
        })
        
        return NextResponse.json({ 
          message: 'Slug request submitted successfully',
          details: 'Your request will be reviewed by our team'
        })
      }

      return NextResponse.json({ 
        message: 'Slug request submitted successfully',
        requestId: slugRequest.id 
      })

    } catch (error) {
      // Fallback if table doesn't exist
      log.info('Vanity slug request submitted (fallback)', { 
        userId, 
        preferredSlug: preferred_slug 
      })
      
      return NextResponse.json({ 
        message: 'Slug request submitted successfully',
        details: 'Your request will be reviewed by our team'
      })
    }

  } catch (error) {
    log.error('Referrals POST error', { error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}