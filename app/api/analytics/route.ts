import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { analyticsQuerySchema } from '@/lib/validations'
import { requirePermission, canAccess<PERSON><PERSON><PERSON>, getUserContext } from '@/lib/rbac'
import { log } from '@/lib/log'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = Object.fromEntries(searchParams.entries())
    
    const validation = analyticsQuerySchema.safeParse({
      ...query,
      metrics: query.metrics?.split(',') || ['referral_clicks', 'leads_submitted']
    })
    
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Invalid query parameters',
        details: validation.error.issues 
      }, { status: 400 })
    }

    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userContext = await getUserContext()
    const client = await createClient()

    // Check permissions and partner access
    if (userContext?.role === 'partner') {
      await requirePermission('view_own_analytics')
      if (validation.data.partner_id && validation.data.partner_id !== userId) {
        return NextResponse.json({ error: 'Access denied' }, { status: 403 })
      }
      validation.data.partner_id = userId // force own data
    } else {
      await requirePermission('view_system_analytics')
      if (validation.data.partner_id && !(await canAccessPartner(validation.data.partner_id))) {
        return NextResponse.json({ error: 'Access denied to partner data' }, { status: 403 })
      }
    }

    // Determine partner IDs to query
    let partnerIds: string[] = []
    if (validation.data.partner_id) {
      partnerIds = [validation.data.partner_id]
    } else if (userContext?.role === 'sales') {
      // Sales users see analytics for their assigned partners
      const { data: assignments } = await client
        .from('partners_teams_assignments')
        .select('partner_id')
        .eq('sales_user_id', userId)
        .eq('active', true)
      partnerIds = assignments?.map(a => a.partner_id) || []
    }

    // Build analytics query
    let analyticsQuery = client
      .from('partners_analytics_daily_stats')
      .select('*')
      .gte('date', validation.data.date_from)
      .lte('date', validation.data.date_to)

    if (partnerIds.length > 0) {
      analyticsQuery = analyticsQuery.in('partner_id', partnerIds)
    }

    const { data: dailyStats, error } = await analyticsQuery.order('date', { ascending: true })

    if (error) {
      log.error('Failed to fetch analytics', { userId, error: error.message })
      return NextResponse.json({ error: 'Failed to fetch analytics' }, { status: 500 })
    }

    // Process and aggregate data based on requested metrics and grouping
    const processedData = processAnalyticsData(dailyStats || [], validation.data)

    // Get summary statistics
    const summary = calculateAnalyticsSummary(dailyStats || [], validation.data.metrics)

    log.info('Analytics fetched', { 
      userId, 
      metadata: {
        partnerId: validation.data.partner_id,
        dateRange: `${validation.data.date_from} to ${validation.data.date_to}`,
        dataPoints: processedData.length
      }
    })

    return NextResponse.json({ 
      data: processedData,
      summary,
      partner_id: validation.data.partner_id
    })

  } catch (error) {
    log.error('Analytics GET error', { error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

function processAnalyticsData(dailyStats: any[], query: any) {
  // Group data by the requested period
  const grouped = dailyStats.reduce((acc, stat) => {
    let groupKey: string
    const date = new Date(stat.date)
    
    switch (query.group_by) {
      case 'week':
        // Get Monday of the week
        const monday = new Date(date)
        monday.setDate(date.getDate() - date.getDay() + 1)
        groupKey = monday.toISOString().split('T')[0]
        break
      case 'month':
        groupKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
        break
      default: // day
        groupKey = stat.date
    }

    if (!acc[groupKey]) {
      acc[groupKey] = {
        date: groupKey,
        referral_clicks: 0,
        leads_submitted: 0,
        deals_confirmed: 0,
        commission_earned: 0,
        page_views: 0,
        unique_visitors: 0,
        conversion_rate: 0
      }
    }

    // Aggregate metrics
    acc[groupKey].referral_clicks += stat.referral_clicks || 0
    acc[groupKey].leads_submitted += stat.leads_submitted || 0
    acc[groupKey].deals_confirmed += stat.deals_confirmed || 0
    acc[groupKey].commission_earned += parseFloat(stat.commission_earned || 0)
    acc[groupKey].page_views += stat.page_views || 0
    acc[groupKey].unique_visitors += stat.unique_visitors || 0

    return acc
  }, {} as Record<string, any>)

  // Calculate conversion rates and return only requested metrics
  return Object.values(grouped).map((group: any) => {
    group.conversion_rate = group.referral_clicks > 0 
      ? group.leads_submitted / group.referral_clicks 
      : 0

    // Filter to only include requested metrics
    const filtered: any = { date: group.date }
    query.metrics.forEach((metric: string) => {
      filtered[metric] = group[metric]
    })
    
    return filtered
  })
}

function calculateAnalyticsSummary(dailyStats: any[], metrics: string[]) {
  const summary: any = {}
  
  metrics.forEach(metric => {
    const values = dailyStats.map(stat => parseFloat(stat[metric] || 0))
    
    summary[metric] = {
      total: values.reduce((sum, val) => sum + val, 0),
      average: values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0,
      max: Math.max(...values, 0),
      min: Math.min(...values, 0)
    }
  })

  // Calculate overall conversion rate
  const totalClicks = summary.referral_clicks?.total || 0
  const totalLeads = summary.leads_submitted?.total || 0
  summary.overall_conversion_rate = totalClicks > 0 ? totalLeads / totalClicks : 0

  return summary
}

// POST handler for tracking events
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { event, properties, userId, sessionId, timestamp } = body

    if (!event) {
      return NextResponse.json({ error: 'Event name is required' }, { status: 400 })
    }

    // Get authenticated user if available
    const { userId: authUserId } = await auth()
    const finalUserId = authUserId || userId

    // Get client IP for tracking
    const ip = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    const client = await createClient()

    // Store analytics event
    const { error } = await client
      .from('analytics_events')
      .insert({
        event,
        properties: {
          ...properties,
          ip,
          user_agent: userAgent,
          session_id: sessionId,
        },
        user_id: finalUserId,
        created_at: timestamp || new Date().toISOString()
      })

    if (error) {
      log.error('Failed to store analytics event', { 
        error, 
        metadata: { event } 
      })
      return NextResponse.json({ error: 'Failed to store event' }, { status: 500 })
    }

    // For referral attribution events, also update attribution cookies
    if (event === 'referral_link_visited' && properties?.referral_slug) {
      const response = NextResponse.json({ success: true })
      
      // Set attribution cookie
      const attribution = {
        slug: properties.referral_slug,
        partner_name: properties.partner_name,
        timestamp: new Date().toISOString(),
        utm_source: properties.utm_source,
        utm_medium: properties.utm_medium,
        utm_campaign: properties.utm_campaign,
        utm_content: properties.utm_content,
        utm_term: properties.utm_term,
      }

      response.cookies.set('ibc_partner_ref', JSON.stringify(attribution), {
        maxAge: 30 * 24 * 60 * 60, // 30 days
        httpOnly: false, // Allow client-side access
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
      })

      return response
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    log.error('Analytics POST error', { error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}