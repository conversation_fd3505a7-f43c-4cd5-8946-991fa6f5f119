import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { leadSubmissionSchema, leadFilterSchema } from '@/lib/validations'
import { requirePermission, canAccessPartner, getUserContext } from '@/lib/rbac'
import { log } from '@/lib/log'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const filters = Object.fromEntries(searchParams.entries())
    
    const validation = leadFilterSchema.safeParse(filters)
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Invalid filters',
        details: validation.error.issues 
      }, { status: 400 })
    }

    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userContext = await getUserContext()
    const client = await createClient()

    // Build query based on user role and filters
    let query = client
      .from('partners_leads')
      .select(`
        *,
        partners_profiles(full_name, company_name, tier)
      `)

    // Apply role-based filtering
    if (userContext?.role === 'partner') {
      query = query.eq('partner_id', userId)
    } else if (userContext?.role === 'sales') {
      // Sales users see leads from their assigned partners
      const { data: assignments } = await client
        .from('partners_teams_assignments')
        .select('partner_id')
        .eq('sales_user_id', userId)
        .eq('active', true)

      const partnerIds = assignments?.map(a => a.partner_id) || []
      if (partnerIds.length > 0) {
        query = query.in('partner_id', partnerIds)
      } else {
        // No assigned partners
        return NextResponse.json({ data: [], pagination: { page: 1, total: 0, pages: 0 } })
      }
    }
    // Ops, accounting, and super_admin see all leads (no additional filtering)

    // Apply user filters
    if (validation.data.status) {
      query = query.eq('status', validation.data.status)
    }
    if (validation.data.partner_id) {
      if (await canAccessPartner(validation.data.partner_id)) {
        query = query.eq('partner_id', validation.data.partner_id)
      } else {
        return NextResponse.json({ error: 'Access denied to partner data' }, { status: 403 })
      }
    }
    if (validation.data.search) {
      query = query.ilike('company_name', `%${validation.data.search}%`)
    }
    if (validation.data.date_from) {
      query = query.gte('created_at', validation.data.date_from)
    }
    if (validation.data.date_to) {
      query = query.lte('created_at', validation.data.date_to)
    }

    // Get total count for pagination
    const { count } = await client
      .from('partners_leads')
      .select('*', { count: 'exact', head: true })

    // Apply pagination
    const offset = (validation.data.page - 1) * validation.data.limit
    query = query
      .range(offset, offset + validation.data.limit - 1)
      .order('created_at', { ascending: false })

    const { data: leads, error } = await query

    if (error) {
      log.error('Failed to fetch leads', { userId: userId || undefined, error: error.message })
      return NextResponse.json({ error: 'Failed to fetch leads' }, { status: 500 })
    }

    const pagination = {
      page: validation.data.page,
      limit: validation.data.limit,
      total: count || 0,
      pages: Math.ceil((count || 0) / validation.data.limit)
    }

    log.info('Leads fetched', { 
      userId: userId || undefined, 
      metadata: { count: leads?.length || 0 }
    })
    return NextResponse.json({ data: leads, pagination })

  } catch (error) {
    log.error('Leads GET error', { error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validation = leadSubmissionSchema.safeParse(body)
    
    if (!validation.success) {
      log.warn('Lead submission validation failed', { 
        metadata: { errors: validation.error.issues }
      })
      return NextResponse.json({ 
        error: 'Validation failed',
        details: validation.error.issues 
      }, { status: 400 })
    }

    const { userId } = await auth()
    
    // Lead submission can be done by partners or public (referral form)
    if (userId) {
      await requirePermission('submit_leads')
    }

    const client = await createClient(true) // use service role for public submissions
    
    // Create lead
    const { data: lead, error } = await client
      .from('partners_leads')
      .insert({
        partner_id: userId || undefined, // null for public submissions
        ...validation.data
      })
      .select()
      .single()

    if (error) {
      log.error('Failed to create lead', { userId: userId || undefined, error: error.message })
      return NextResponse.json({ error: 'Failed to create lead' }, { status: 500 })
    }

    // Track analytics event
    await client.from('partners_analytics_events').insert({
      partner_id: userId || undefined,
      event_type: 'lead_submitted',
      event_data: {
        lead_id: lead.id,
        company_name: lead.company_name,
        source: 'direct_submission'
      }
    })

    log.info('Lead created', { 
      userId: userId || undefined, 
      metadata: {
        leadId: lead.id, 
        companyName: lead.company_name
      }
    })

    return NextResponse.json({ data: lead }, { status: 201 })

  } catch (error) {
    log.error('Leads POST error', { error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}