import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { leadReviewSchema } from '@/lib/validations'
import { canAccessPartner, getUserContext } from '@/lib/rbac'
import { log } from '@/lib/log'

interface Params {
  id: string
}

export async function GET(request: NextRequest, { params }: { params: Promise<Params> }) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const resolvedParams = await params
    const client = await createClient()
    const { data: lead, error } = await client
      .from('partners_leads')
      .select(`
        *,
        partners_profiles(full_name, company_name, tier, email)
      `)
      .eq('id', resolvedParams.id)
      .single()

    if (error) {
      log.error('Failed to fetch lead', { userId, leadId: resolvedParams.id, error: error.message })
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    // Check access permissions
    if (lead.partner_id && !(await canAccessPartner(lead.partner_id))) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    log.info('Lead fetched', { userId, leadId: resolvedParams.id })
    return NextResponse.json({ data: lead })

  } catch (error) {
    log.error('Lead GET error', { leadId: resolvedParams.id, error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<Params> }) {
  try {
    const body = await request.json()
    const validation = leadReviewSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Validation failed',
        details: validation.error.issues 
      }, { status: 400 })
    }

    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userContext = await getUserContext()
    if (!userContext || !['sales', 'ops', 'super_admin'].includes(userContext.role || '')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const resolvedParams = await params
    const client = await createClient()

    // Get current lead for access check and audit
    const { data: currentLead, error: fetchError } = await client
      .from('partners_leads')
      .select('*')
      .eq('id', resolvedParams.id)
      .single()

    if (fetchError) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    // Check partner access for sales users
    if (userContext.role === 'sales' && currentLead.partner_id) {
      if (!(await canAccessPartner(currentLead.partner_id))) {
        return NextResponse.json({ error: 'Access denied' }, { status: 403 })
      }
    }

    // Update lead
    const { data: updatedLead, error } = await client
      .from('partners_leads')
      .update({
        ...validation.data,
        updated_at: new Date().toISOString()
      })
      .eq('id', resolvedParams.id)
      .select()
      .single()

    if (error) {
      log.error('Failed to update lead', { userId, leadId: resolvedParams.id, error: error.message })
      return NextResponse.json({ error: 'Failed to update lead' }, { status: 500 })
    }

    // Log audit event
    await client.rpc('log_audit_event', {
      p_user_id: userId,
      p_user_type: 'internal',
      p_action: 'update_lead',
      p_resource_type: 'lead',
      p_resource_id: resolvedParams.id,
      p_old_values: currentLead,
      p_new_values: updatedLead
    })

    // Track analytics event if status changed
    if (validation.data.status && validation.data.status !== currentLead.status) {
      await client.from('partners_analytics_events').insert({
        partner_id: currentLead.partner_id,
        event_type: validation.data.status === 'approved' ? 'deal_view' : 'lead_submitted',
        event_data: {
          lead_id: resolvedParams.id,
          old_status: currentLead.status,
          new_status: validation.data.status,
          reviewed_by: userId
        }
      })
    }

    log.info('Lead updated', { 
      userId, 
      leadId: resolvedParams.id, 
      changes: Object.keys(validation.data) 
    })

    return NextResponse.json({ data: updatedLead })

  } catch (error) {
    log.error('Lead PUT error', { leadId: resolvedParams.id, error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<Params> }) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userContext = await getUserContext()
    if (!userContext || !['ops', 'super_admin'].includes(userContext.role || '')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const resolvedParams = await params
    const client = await createClient()

    // Get lead for audit log
    const { data: lead } = await client
      .from('partners_leads')
      .select('*')
      .eq('id', resolvedParams.id)
      .single()

    if (!lead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    // Delete lead
    const { error } = await client
      .from('partners_leads')
      .delete()
      .eq('id', resolvedParams.id)

    if (error) {
      log.error('Failed to delete lead', { userId, leadId: resolvedParams.id, error: error.message })
      return NextResponse.json({ error: 'Failed to delete lead' }, { status: 500 })
    }

    // Log audit event
    await client.rpc('log_audit_event', {
      p_user_id: userId,
      p_user_type: 'internal',
      p_action: 'delete_lead',
      p_resource_type: 'lead',
      p_resource_id: resolvedParams.id,
      p_old_values: lead,
      p_new_values: null
    })

    log.info('Lead deleted', { userId, leadId: resolvedParams.id })
    return NextResponse.json({ success: true })

  } catch (error) {
    log.error('Lead DELETE error', { leadId: resolvedParams.id, error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}