import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { profileUpdateSchema } from '@/lib/validations'
import { requirePermission, getUserContext } from '@/lib/rbac'
import { log } from '@/lib/log'

export async function GET() {
  try {
    await requirePermission('view_own_profile')
    
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const client = await createClient()
    const { data: profile, error } = await client
      .from('partners_profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (error) {
      log.error('Failed to fetch profile', { userId, error: error.message })
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 })
    }

    log.info('Profile fetched', { userId, profileId: profile.id })
    return NextResponse.json({ data: profile })

  } catch (error) {
    log.error('Profile GET error', { error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    await requirePermission('update_own_profile')
    
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validation = profileUpdateSchema.safeParse(body)
    
    if (!validation.success) {
      log.warn('Profile update validation failed', { 
        userId, 
        errors: validation.error.issues 
      })
      return NextResponse.json({ 
        error: 'Validation failed',
        details: validation.error.issues 
      }, { status: 400 })
    }

    const userContext = await getUserContext()
    const client = await createClient()

    // Get current profile for audit log
    const { data: currentProfile } = await client
      .from('partners_profiles')
      .select('*')
      .eq('id', userId)
      .single()

    // Update profile
    const { data: updatedProfile, error } = await client
      .from('partners_profiles')
      .update({
        ...validation.data,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select()
      .single()

    if (error) {
      log.error('Failed to update profile', { userId, error: error.message })
      return NextResponse.json({ error: 'Failed to update profile' }, { status: 500 })
    }

    // Log audit event
    if (userContext) {
      await client.rpc('log_audit_event', {
        p_user_id: userId,
        p_user_type: 'partner',
        p_action: 'update_profile',
        p_resource_type: 'profile',
        p_resource_id: userId,
        p_old_values: currentProfile,
        p_new_values: updatedProfile
      })
    }

    log.info('Profile updated', { userId, changes: Object.keys(validation.data) })
    return NextResponse.json({ data: updatedProfile })

  } catch (error) {
    log.error('Profile PUT error', { error })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}