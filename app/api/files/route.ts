import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { z } from 'zod'
import { log } from '@/lib/log'

const fileQuerySchema = z.object({
  partner_id: z.string().optional(),
  upload_type: z.enum(['company_material', 'support_document']).optional(),
  status: z.enum(['uploaded', 'approved', 'rejected']).optional(),
  limit: z.string().transform(Number).optional(),
  offset: z.string().transform(Number).optional()
})

export async function GET(request: NextRequest) {
  const { userId } = await auth()
  
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const url = new URL(request.url)
  const queryParams = Object.fromEntries(url.searchParams)
  
  const validation = fileQuerySchema.safeParse(queryParams)
  if (!validation.success) {
    return NextResponse.json({ 
      error: 'Invalid query parameters',
      details: validation.error.issues 
    }, { status: 400 })
  }

  const { partner_id, upload_type, status, limit = 50, offset = 0 } = validation.data

  try {
    const client = await createClient(true)
    
    // Check if user has permission to view files
    const { data: userProfile } = await client
      .from('partners_users')
      .select('role')
      .eq('id', userId)
      .single()

    let query = client
      .from('partners_file_uploads')
      .select(`
        id,
        partner_id,
        file_name,
        file_key,
        file_url,
        file_size,
        file_type,
        upload_type,
        status,
        is_public,
        created_at,
        updated_at,
        partners_profiles!partner_id(full_name, company_name)
      `)

    // Apply access control
    if (!userProfile || !['ops', 'accounting', 'super_admin'].includes(userProfile.role)) {
      // Partner can only see their own files or public company materials
      query = query.or(`partner_id.eq.${userId},and(upload_type.eq.company_material,is_public.eq.true)`)
    }

    // Apply filters
    if (partner_id) {
      query = query.eq('partner_id', partner_id)
    }
    if (upload_type) {
      query = query.eq('upload_type', upload_type)
    }
    if (status) {
      query = query.eq('status', status)
    }

    const { data: files, error } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      log.error('Failed to fetch files', { error: error.message, userId })
      return NextResponse.json({ error: 'Failed to fetch files' }, { status: 500 })
    }

    log.businessEvent('files_viewed', { 
      userId,
      metadata: {
        fileCount: files?.length || 0,
        filters: validation.data
      }
    })

    return NextResponse.json({
      files: files || [],
      total: files?.length || 0
    })

  } catch (error) {
    log.error('Error in files GET', { 
      error: error instanceof Error ? error.message : String(error),
      userId 
    })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

const fileUpdateSchema = z.object({
  file_id: z.string(),
  status: z.enum(['uploaded', 'approved', 'rejected']),
  notes: z.string().optional()
})

export async function PATCH(request: NextRequest) {
  const { userId } = await auth()
  
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const body = await request.json()
    const validation = fileUpdateSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Invalid data',
        details: validation.error.issues 
      }, { status: 400 })
    }

    const { file_id, status, notes } = validation.data
    const client = await createClient(true)

    // Check admin permissions
    const { data: userProfile } = await client
      .from('partners_users')
      .select('role, display_name')
      .eq('id', userId)
      .single()

    if (!userProfile || !['ops', 'accounting', 'super_admin'].includes(userProfile.role)) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get file info for audit
    const { data: currentFile } = await client
      .from('partners_file_uploads')
      .select('*')
      .eq('id', file_id)
      .single()

    if (!currentFile) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 })
    }

    // Update file status
    const { data: updatedFile, error } = await client
      .from('partners_file_uploads')
      .update({
        status,
        admin_notes: notes,
        reviewed_at: new Date().toISOString(),
        reviewed_by: userId
      })
      .eq('id', file_id)
      .select()
      .single()

    if (error) {
      log.error('Failed to update file status', { 
        error: error.message, 
        fileId: file_id, 
        adminUserId: userId 
      })
      return NextResponse.json({ error: 'Failed to update file' }, { status: 500 })
    }

    // Create notification for partner
    if (status === 'approved' || status === 'rejected') {
      const notificationMessage = status === 'approved' 
        ? `Your uploaded document "${currentFile.file_name}" has been approved.`
        : `Your uploaded document "${currentFile.file_name}" has been rejected. ${notes || 'Please contact support for more information.'}`

      await client
        .from('partners_notifications')
        .insert({
          recipient_id: currentFile.partner_id,
          notification_type: 'document_review',
          title: `Document ${status === 'approved' ? 'Approved' : 'Rejected'}`,
          message: notificationMessage,
          metadata: {
            file_id,
            file_name: currentFile.file_name,
            status,
            reviewed_by: userId,
            admin_notes: notes
          }
        })
    }

    log.businessEvent('file_status_updated', {
      adminUserId: userId,
      adminName: userProfile.display_name,
      fileId: file_id,
      fileName: currentFile.file_name,
      partnerId: currentFile.partner_id,
      previousStatus: currentFile.status,
      newStatus: status,
      notes
    })

    return NextResponse.json({ 
      success: true, 
      file: updatedFile 
    })

  } catch (error) {
    log.error('Error in files PATCH', { 
      error: error instanceof Error ? error.message : String(error),
      userId 
    })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  const { userId } = await auth()
  
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const url = new URL(request.url)
  const fileId = url.searchParams.get('file_id')

  if (!fileId) {
    return NextResponse.json({ error: 'File ID required' }, { status: 400 })
  }

  try {
    const client = await createClient(true)
    
    // Get file info and check permissions
    const { data: file } = await client
      .from('partners_file_uploads')
      .select('*')
      .eq('id', fileId)
      .single()

    if (!file) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 })
    }

    // Check if user can delete this file
    const { data: userProfile } = await client
      .from('partners_users')
      .select('role')
      .eq('id', userId)
      .single()

    const canDelete = (
      file.partner_id === userId || // Owner
      (userProfile && ['ops', 'super_admin'].includes(userProfile.role)) // Admin
    )

    if (!canDelete) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 })
    }

    // Soft delete the file record
    const { error } = await client
      .from('partners_file_uploads')
      .update({
        status: 'deleted',
        deleted_at: new Date().toISOString(),
        deleted_by: userId
      })
      .eq('id', fileId)

    if (error) {
      log.error('Failed to delete file', { 
        error: error.message, 
        fileId, 
        userId 
      })
      return NextResponse.json({ error: 'Failed to delete file' }, { status: 500 })
    }

    // Note: We don't actually delete from UploadThing storage 
    // to maintain audit trail. Files can be cleaned up separately.

    log.businessEvent('file_deleted', {
      userId,
      fileId,
      fileName: file.file_name,
      partnerId: file.partner_id
    })

    return NextResponse.json({ success: true })

  } catch (error) {
    log.error('Error in files DELETE', { 
      error: error instanceof Error ? error.message : String(error),
      userId 
    })
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}