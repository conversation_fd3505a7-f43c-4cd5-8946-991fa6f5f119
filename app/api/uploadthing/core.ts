import { createUploadthing, type FileRouter } from "uploadthing/next";
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@/lib/supabase/server'
import { log } from '@/lib/log'

const f = createUploadthing();

// File router for company materials and support documents only
export const ourFileRouter = {

  // Company Materials (for resource library)
  companyMaterials: f({ 
    image: { maxFileSize: "8MB", maxFileCount: 10 },
    pdf: { maxFileSize: "16MB", maxFileCount: 10 }
  })
    .middleware(async ({ req }) => {
      const { userId } = await auth()
      if (!userId) {
        throw new Error("Unauthorized")
      }

      // Check if user is admin (for uploading company materials)
      const client = await createClient(true)
      const { data: user } = await client
        .from('partners_users')
        .select('role, display_name')
        .eq('id', userId)
        .single()

      if (!user || !['ops', 'super_admin'].includes(user.role)) {
        throw new Error("Admin access required")
      }

      log.businessEvent('admin_file_upload_initiated', {
        userId,
        uploadType: 'company_materials',
        adminName: user.display_name,
        adminRole: user.role
      })

      return { 
        userId, 
        uploadType: 'company_materials',
        adminName: user.display_name 
      }
    })
    .onUploadComplete(async ({ metadata, file }) => {
      try {
        const client = await createClient(true)
        
        await client
          .from('partners_file_uploads')
          .insert({
            partner_id: metadata.userId,
            file_name: file.name,
            file_key: file.key,
            file_url: file.url,
            file_size: file.size,
            file_type: file.type,
            upload_type: 'company_material',
            status: 'uploaded',
            is_public: true // Company materials are publicly accessible to partners
          })

        log.businessEvent('admin_file_upload_completed', {
          userId: metadata.userId,
          fileName: file.name,
          fileSize: file.size,
          uploadType: 'company_materials',
          fileUrl: file.url
        })

        return { 
          uploadedBy: metadata.userId,
          fileName: file.name,
          fileUrl: file.url 
        }
      } catch (error) {
        log.error('Failed to store admin file upload record', {
          userId: metadata.userId,
          fileName: file.name,
          error: error instanceof Error ? error.message : String(error)
        })
        throw error
      }
    }),

  // Support Documents (for partner queries/support)
  supportDocuments: f({ 
    image: { maxFileSize: "4MB", maxFileCount: 3 },
    pdf: { maxFileSize: "8MB", maxFileCount: 3 }
  })
    .middleware(async ({ req }) => {
      const { userId } = await auth()
      if (!userId) {
        throw new Error("Unauthorized")
      }

      const client = await createClient(true)
      const { data: profile } = await client
        .from('partners_profiles')
        .select('id, status, full_name')
        .eq('id', userId)
        .single()

      if (!profile || profile.status !== 'active') {
        throw new Error("Active partner profile required")
      }

      log.businessEvent('support_file_upload_initiated', {
        userId,
        uploadType: 'support_documents',
        partnerName: profile.full_name
      })

      return { 
        userId, 
        uploadType: 'support_documents',
        partnerName: profile.full_name 
      }
    })
    .onUploadComplete(async ({ metadata, file }) => {
      try {
        const client = await createClient(true)
        
        await client
          .from('partners_file_uploads')
          .insert({
            partner_id: metadata.userId,
            file_name: file.name,
            file_key: file.key,
            file_url: file.url,
            file_size: file.size,
            file_type: file.type,
            upload_type: 'support_document',
            status: 'uploaded'
          })

        log.businessEvent('support_file_upload_completed', {
          userId: metadata.userId,
          fileName: file.name,
          fileSize: file.size,
          uploadType: 'support_documents',
          fileUrl: file.url
        })

        return { 
          uploadedBy: metadata.userId,
          fileName: file.name,
          fileUrl: file.url 
        }
      } catch (error) {
        log.error('Failed to store support file upload record', {
          userId: metadata.userId,
          fileName: file.name,
          error: error instanceof Error ? error.message : String(error)
        })
        throw error
      }
    })
} satisfies FileRouter;

export type OurFileRouter = typeof ourFileRouter;