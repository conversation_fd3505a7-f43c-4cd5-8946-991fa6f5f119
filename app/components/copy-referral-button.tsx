"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Copy } from "lucide-react"
import { useAnalytics } from '@/lib/use-analytics'
import { useToast } from "@/hooks/use-toast"

interface CopyReferralButtonProps {
  slug: string
}

export function CopyReferralButton({ slug }: CopyReferralButtonProps) {
  const analytics = useAnalytics()
  const { toast } = useToast()

  const handleCopy = async () => {
    try {
      const url = `${window.location.origin}/referral/${slug}`
      await navigator.clipboard.writeText(url)
      
      // Track the referral link copy event
      await analytics.trackReferralLinkCopy(slug)
      
      toast({
        title: "Link copied!",
        description: "Your referral link has been copied to clipboard.",
      })
    } catch (error) {
      console.error('Failed to copy link:', error)
      toast({
        title: "Copy failed",
        description: "Unable to copy link to clipboard.",
        variant: "destructive",
      })
    }
  }

  return (
    <Button variant="outline" size="sm" onClick={handleCopy}>
      <Copy className="h-4 w-4" />
    </Button>
  )
}