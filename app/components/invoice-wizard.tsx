"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { PDFDocument, StandardFonts, rgb } from "pdf-lib"
import { Loader2, Download, Shield } from "lucide-react"

type Deal = {
  id: string
  client_company: string
  deal_value: number
  commission_amount: number
  status: string
  created_at: string
}

type Props = { 
  open: boolean
  onOpenChange: (open: boolean) => void
  partnerId?: string
}

export function InvoiceWizard({ open, onOpenChange, partnerId }: Props) {
  const { toast } = useToast()
  const [deals, setDeals] = React.useState<Deal[]>([])
  const [loading, setLoading] = React.useState(false)
  const [selectedDeals, setSelectedDeals] = React.useState<string[]>([])
  const [inv, setInv] = React.useState({
    number: `INV-${Date.now()}`,
    date: new Date().toISOString().split('T')[0],
    amount: "",
    payee: "",
    notes: "",
    bankDetails: "",
    usdtAddress: "",
    paymentMethod: "usdt" as "usdt" | "bank",
    billingAddress: {
      street1: "",
      street2: "",
      city: "",
      state: "",
      postal_code: "",
      country: "",
      tax_id: ""
    }
  })
  const [previewFile, setPreviewFile] = React.useState<File | null>(null)

  // Fetch confirmed deals for the partner
  React.useEffect(() => {
    if (open && partnerId) {
      fetchDeals()
    }
  }, [open, partnerId])

  const fetchDeals = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/deals')
      if (response.ok) {
        const result = await response.json()
        const confirmedDeals = result.data.filter((deal: Deal) => 
          deal.status === 'confirmed' || deal.status === 'paid'
        )
        setDeals(confirmedDeals)
        
        // Auto-select confirmed deals for withdrawal
        const confirmedIds = confirmedDeals
          .filter((deal: Deal) => deal.status === 'confirmed')
          .map((deal: Deal) => deal.id)
        setSelectedDeals(confirmedIds)
        
        // Calculate total amount from confirmed deals
        const totalAmount = confirmedDeals
          .filter((deal: Deal) => deal.status === 'confirmed')
          .reduce((sum: number, deal: Deal) => sum + deal.commission_amount, 0)
        setInv(prev => ({ ...prev, amount: totalAmount.toFixed(2) }))
      }
    } catch (error) {
      console.error('Failed to fetch deals:', error)
      toast({ title: "Error", description: "Failed to load deal data", variant: "destructive" })
    } finally {
      setLoading(false)
    }
  }

  const toggleDeal = (dealId: string) => {
    setSelectedDeals(prev => {
      const newSelection = prev.includes(dealId)
        ? prev.filter(id => id !== dealId)
        : [...prev, dealId]
      
      // Recalculate total amount
      const selectedDealObjects = deals.filter(deal => newSelection.includes(deal.id))
      const totalAmount = selectedDealObjects.reduce((sum: number, deal: Deal) => sum + deal.commission_amount, 0)
      setInv(prevInv => ({ ...prevInv, amount: totalAmount.toFixed(2) }))
      
      return newSelection
    })
  }

  const onDrop = async (file: File | undefined) => {
    if (!file) return
    setPreviewFile(file)
    toast({ title: "Invoice loaded", description: "Previewing dropped PDF (client-only)." })
  }

  const downloadPdf = async () => {
    if (selectedDeals.length === 0) {
      toast({ title: "No deals selected", description: "Please select at least one deal for the invoice.", variant: "destructive" })
      return
    }

    // Validate required billing address fields
    const { street1, city, state, postal_code, country } = inv.billingAddress
    if (!street1 || !city || !state || !postal_code || !country) {
      toast({ 
        title: "Billing address required", 
        description: "Please fill in all required billing address fields.", 
        variant: "destructive" 
      })
      return
    }

    try {
      // Client-only generation; no storage/transmission.
      const pdfDoc = await PDFDocument.create()
      const page = pdfDoc.addPage([595.28, 841.89]) // A4
      const font = await pdfDoc.embedFont(StandardFonts.Helvetica)
      const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold)
      
      const draw = (text: string, x: number, y: number, size = 12, isBold = false) => {
        page.drawText(text, { 
          x, y, size, 
          font: isBold ? boldFont : font, 
          color: rgb(0, 0, 0) 
        })
      }

      let y = 800
      
      // Header
      draw("COMMISSION WITHDRAWAL INVOICE", 50, y, 18, true)
      y -= 40
      
      // Invoice details
      draw(`Invoice #: ${inv.number}`, 50, y, 12, true)
      draw(`Date: ${inv.date}`, 300, y)
      y -= 20
      draw(`Total Amount: $${inv.amount}`, 50, y, 14, true)
      y -= 40

      // Billing Address
      draw("Bill To:", 50, y, 12, true)
      y -= 15
      draw(inv.billingAddress.street1, 50, y, 10)
      y -= 12
      if (inv.billingAddress.street2) {
        draw(inv.billingAddress.street2, 50, y, 10)
        y -= 12
      }
      draw(`${inv.billingAddress.city}, ${inv.billingAddress.state} ${inv.billingAddress.postal_code}`, 50, y, 10)
      y -= 12
      draw(inv.billingAddress.country, 50, y, 10)
      if (inv.billingAddress.tax_id) {
        y -= 12
        draw(`Tax ID: ${inv.billingAddress.tax_id}`, 50, y, 10)
      }
      y -= 30
      
      // Payment details
      draw("Payment Details:", 50, y, 12, true)
      y -= 20
      if (inv.paymentMethod === 'usdt') {
        draw(`USDT Address: ${inv.usdtAddress || 'To be provided'}`, 50, y)
      } else {
        draw(`Bank Details: ${inv.bankDetails || 'To be provided'}`, 50, y)
      }
      y -= 30
      
      // Deal breakdown
      draw("Commission Breakdown:", 50, y, 12, true)
      y -= 25
      
      const selectedDealObjects = deals.filter(deal => selectedDeals.includes(deal.id))
      
      // Table headers
      draw("Client", 50, y, 10, true)
      draw("Deal Value", 200, y, 10, true)
      draw("Commission", 300, y, 10, true)
      draw("Status", 400, y, 10, true)
      y -= 5
      
      // Draw line
      page.drawLine({
        start: { x: 50, y },
        end: { x: 500, y },
        thickness: 1,
        color: rgb(0, 0, 0)
      })
      y -= 15
      
      // Deal rows
      selectedDealObjects.forEach(deal => {
        draw(deal.client_company.slice(0, 25), 50, y, 10)
        draw(`$${deal.deal_value.toLocaleString()}`, 200, y, 10)
        draw(`$${deal.commission_amount.toFixed(2)}`, 300, y, 10)
        draw(deal.status.charAt(0).toUpperCase() + deal.status.slice(1), 400, y, 10)
        y -= 15
      })
      
      y -= 10
      
      // Total line
      page.drawLine({
        start: { x: 280, y },
        end: { x: 380, y },
        thickness: 1,
        color: rgb(0, 0, 0)
      })
      y -= 15
      
      draw(`Total: $${inv.amount}`, 300, y, 12, true)
      y -= 30
      
      // Notes
      if (inv.notes) {
        draw("Notes:", 50, y, 12, true)
        y -= 20
        draw(inv.notes, 50, y, 10)
        y -= 20
      }
      
      // Footer
      y = 100
      draw("This invoice was generated client-side. No sensitive data was transmitted or stored.", 50, y, 8)
      y -= 15
      draw("Generated on: " + new Date().toLocaleString(), 50, y, 8)
      
      // Encrypt metadata for reuse (optional)
      const metadata = {
        deals: selectedDeals,
        amount: inv.amount,
        timestamp: Date.now()
      }
      
      // Add encrypted metadata as invisible text (for reuse feature)
      const encryptedMetadata = btoa(JSON.stringify(metadata))
      page.drawText(encryptedMetadata, { x: 0, y: 0, size: 1, color: rgb(1, 1, 1) }) // Nearly invisible
      
      const bytes = await pdfDoc.save()
      const blob = new Blob([bytes as BlobPart], { type: "application/pdf" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `commission_invoice_${inv.number}.pdf`
      a.click()
      URL.revokeObjectURL(url)
      
      // Clear sensitive data from memory
      setTimeout(() => {
        setInv(prev => ({ 
          ...prev, 
          bankDetails: "", 
          usdtAddress: "",
          billingAddress: {
            street1: "",
            street2: "",
            city: "",
            state: "",
            postal_code: "",
            country: "",
            tax_id: ""
          }
        }))
      }, 100)
      
      toast({ 
        title: "Invoice downloaded", 
        description: "PDF generated locally. No sensitive data stored on servers." 
      })
    } catch (error) {
      console.error('PDF generation failed:', error)
      toast({ 
        title: "Generation failed", 
        description: "Failed to create PDF. Please try again.", 
        variant: "destructive" 
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent aria-describedby="invoice-desc" className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-green-600" />
            Commission Invoice Generator
          </DialogTitle>
        </DialogHeader>
        <p id="invoice-desc" className="text-sm text-muted-foreground flex items-center gap-2">
          <Shield className="h-4 w-4" />
          Zero-persistence invoice generation. No sensitive data is stored or transmitted to servers.
        </p>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading commission data...</span>
          </div>
        ) : (
          <div className="grid gap-6">
            {/* Deal Selection */}
            {deals.length > 0 && (
              <div className="space-y-3">
                <Label className="text-base font-semibold">Select Deals for Invoice</Label>
                <div className="border rounded-lg p-4 max-h-48 overflow-y-auto">
                  {deals.map(deal => (
                    <div key={deal.id} className="flex items-center justify-between py-2 border-b last:border-b-0">
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={selectedDeals.includes(deal.id)}
                          onChange={() => toggleDeal(deal.id)}
                          className="rounded border-gray-300"
                        />
                        <div>
                          <div className="font-medium">{deal.client_company}</div>
                          <div className="text-sm text-muted-foreground">
                            ${deal.deal_value.toLocaleString()} → ${deal.commission_amount.toFixed(2)} commission
                          </div>
                        </div>
                      </div>
                      <Badge variant={deal.status === 'confirmed' ? 'default' : 'secondary'}>
                        {deal.status.charAt(0).toUpperCase() + deal.status.slice(1)}
                      </Badge>
                    </div>
                  ))}
                </div>
                <div className="text-sm text-muted-foreground">
                  Selected: {selectedDeals.length} deals • Total: ${deals.filter(d => selectedDeals.includes(d.id)).reduce((sum: number, d: Deal) => sum + d.commission_amount, 0).toFixed(2)}
                </div>
              </div>
            )}

            <Separator />

            {/* Invoice Details */}
            <div className="grid gap-4">
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-1.5">
                  <Label htmlFor="number">Invoice Number</Label>
                  <Input id="number" value={inv.number} onChange={(e) => setInv({ ...inv, number: e.target.value })} />
                </div>
                <div className="space-y-1.5">
                  <Label htmlFor="date">Date</Label>
                  <Input
                    id="date"
                    type="date"
                    value={inv.date}
                    onChange={(e) => setInv({ ...inv, date: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-1.5">
                <Label htmlFor="amount">Total Amount (USD)</Label>
                <Input
                  id="amount"
                  inputMode="decimal"
                  value={inv.amount}
                  onChange={(e) => setInv({ ...inv, amount: e.target.value })}
                  className="font-mono text-lg"
                  readOnly={selectedDeals.length > 0}
                />
              </div>

              {/* Payment Method Selection */}
              <div className="space-y-3">
                <Label>Payment Method</Label>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="usdt"
                      name="payment"
                      checked={inv.paymentMethod === 'usdt'}
                      onChange={() => setInv({ ...inv, paymentMethod: 'usdt' })}
                    />
                    <Label htmlFor="usdt">USDT (TRC20)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="bank"
                      name="payment"
                      checked={inv.paymentMethod === 'bank'}
                      onChange={() => setInv({ ...inv, paymentMethod: 'bank' })}
                    />
                    <Label htmlFor="bank">Bank Transfer</Label>
                  </div>
                </div>

                {inv.paymentMethod === 'usdt' ? (
                  <div className="space-y-1.5">
                    <Label htmlFor="usdtAddress">USDT Address (TRC20)</Label>
                    <Input
                      id="usdtAddress"
                      placeholder="T..."
                      value={inv.usdtAddress}
                      onChange={(e) => setInv({ ...inv, usdtAddress: e.target.value })}
                      className="font-mono"
                    />
                  </div>
                ) : (
                  <div className="space-y-1.5">
                    <Label htmlFor="bankDetails">Bank Details</Label>
                    <Textarea
                      id="bankDetails"
                      placeholder="Bank name, Account name, IBAN/Account number, SWIFT code, etc."
                      value={inv.bankDetails}
                      onChange={(e) => setInv({ ...inv, bankDetails: e.target.value })}
                      rows={3}
                    />
                  </div>
                )}
              </div>

              <div className="space-y-1.5">
                <Label htmlFor="notes">Additional Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  placeholder="Special instructions, references, etc."
                  value={inv.notes}
                  onChange={(e) => setInv({ ...inv, notes: e.target.value })}
                  rows={2}
                />
              </div>
            </div>

            <Separator />

            {/* Billing Address */}
            <div className="space-y-4">
              <Label className="text-base font-semibold">Billing Address</Label>
              <p className="text-sm text-muted-foreground">
                This information will be included in the invoice for payment processing. Not stored permanently.
              </p>
              
              <div className="grid gap-4">
                <div className="space-y-1.5">
                  <Label htmlFor="street1">Street Address *</Label>
                  <Input
                    id="street1"
                    placeholder="123 Main Street"
                    value={inv.billingAddress.street1}
                    onChange={(e) => setInv({ 
                      ...inv, 
                      billingAddress: { ...inv.billingAddress, street1: e.target.value }
                    })}
                    required
                  />
                </div>

                <div className="space-y-1.5">
                  <Label htmlFor="street2">Street Address 2 (Optional)</Label>
                  <Input
                    id="street2"
                    placeholder="Apartment, suite, etc."
                    value={inv.billingAddress.street2}
                    onChange={(e) => setInv({ 
                      ...inv, 
                      billingAddress: { ...inv.billingAddress, street2: e.target.value }
                    })}
                  />
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div className="space-y-1.5">
                    <Label htmlFor="city">City *</Label>
                    <Input
                      id="city"
                      placeholder="New York"
                      value={inv.billingAddress.city}
                      onChange={(e) => setInv({ 
                        ...inv, 
                        billingAddress: { ...inv.billingAddress, city: e.target.value }
                      })}
                      required
                    />
                  </div>
                  <div className="space-y-1.5">
                    <Label htmlFor="state">State/Province *</Label>
                    <Input
                      id="state"
                      placeholder="NY"
                      value={inv.billingAddress.state}
                      onChange={(e) => setInv({ 
                        ...inv, 
                        billingAddress: { ...inv.billingAddress, state: e.target.value }
                      })}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div className="space-y-1.5">
                    <Label htmlFor="postal_code">ZIP/Postal Code *</Label>
                    <Input
                      id="postal_code"
                      placeholder="10001"
                      value={inv.billingAddress.postal_code}
                      onChange={(e) => setInv({ 
                        ...inv, 
                        billingAddress: { ...inv.billingAddress, postal_code: e.target.value }
                      })}
                      required
                    />
                  </div>
                  <div className="space-y-1.5">
                    <Label htmlFor="country">Country *</Label>
                    <Input
                      id="country"
                      placeholder="United States"
                      value={inv.billingAddress.country}
                      onChange={(e) => setInv({ 
                        ...inv, 
                        billingAddress: { ...inv.billingAddress, country: e.target.value }
                      })}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-1.5">
                  <Label htmlFor="tax_id">Tax ID / VAT Number (Optional)</Label>
                  <Input
                    id="tax_id"
                    placeholder="***********"
                    value={inv.billingAddress.tax_id}
                    onChange={(e) => setInv({ 
                      ...inv, 
                      billingAddress: { ...inv.billingAddress, tax_id: e.target.value }
                    })}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Reuse Previous Invoice */}
            <div className="space-y-2">
              <Label>Reuse Previous Invoice (Optional)</Label>
              <label className="flex h-20 items-center justify-center rounded-md border-2 border-dashed bg-muted text-sm cursor-pointer hover:bg-muted/80">
                <input
                  type="file"
                  accept="application/pdf"
                  className="hidden"
                  onChange={(e) => onDrop(e.target.files?.[0] || undefined)}
                />
                {previewFile ? (
                  <span className="flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    {previewFile.name}
                  </span>
                ) : (
                  "Drop a previous invoice PDF to extract payment details"
                )}
              </label>
            </div>

            {/* Actions */}
            <div className="flex gap-3 justify-end pt-4 border-t">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Close
              </Button>
              <Button 
                onClick={downloadPdf} 
                disabled={
                  selectedDeals.length === 0 || 
                  !inv.billingAddress.street1 || 
                  !inv.billingAddress.city || 
                  !inv.billingAddress.state || 
                  !inv.billingAddress.postal_code || 
                  !inv.billingAddress.country
                } 
                className="gap-2"
              >
                <Download className="h-4 w-4" />
                Generate Invoice PDF
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
