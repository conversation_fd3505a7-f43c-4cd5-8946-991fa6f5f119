"use client"

import type React from "react"
import Link from "next/link"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import { Search } from "lucide-react"
import { SignedIn, SignedOut, SignInButton, UserButton, useUser } from "@clerk/nextjs"
import { useEffect } from "react"
import { log } from "@/lib/log"


export function AppShell({ children, title, hideNavigation = false }: { children: React.ReactNode; title: string; hideNavigation?: boolean }) {
  if (hideNavigation) {
    return (
      <div className="flex min-h-svh w-full flex-col bg-background">
        {/* Simple header for onboarding without sidebar */}
        <header className="flex h-14 shrink-0 items-center gap-2 border-b px-4">
          <div className="flex items-center gap-2">
            <h1 className="font-semibold">{title}</h1>
          </div>
          <div className="ml-auto">
            <SignedIn>
              <UserButton
                afterSignOutUrl="/sign-in"
                appearance={{ elements: { userButtonAvatarBox: "size-8" } }}
              />
            </SignedIn>
            <SignedOut>
              <SignInButton mode="modal">
                <Button variant="outline" size="sm">Sign in</Button>
              </SignInButton>
            </SignedOut>
          </div>
        </header>
        <main className="flex-1 p-4 sm:p-6">{children}</main>
      </div>
    )
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <div className="flex min-h-svh w-full flex-1 flex-col bg-background">
        <header className="flex h-14 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="hidden md:flex">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href="/">Home</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>{title}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
          <div className="relative ml-auto flex-1 md:grow-0">
            <Search className="pointer-events-none absolute left-2 top-2.5 size-4 opacity-50" aria-hidden />
            <Input
              type="search"
              placeholder="Search..."
              className="w-full pl-8 md:w-[220px] lg:w-[360px]"
              aria-label="Global search"
            />
          </div>
          {/* Clerk UserButton replaces custom dropdown */}
          <SignedIn>
            <div className="ml-2 flex items-center">
              <UserButton
                afterSignOutUrl="/sign-in"
                appearance={{ elements: { userButtonAvatarBox: "size-9" } }}
              />
            </div>
          </SignedIn>
          <SignedOut>
            <SignInButton mode="modal">
              <Button variant="outline" size="sm" className="ml-2">Sign in</Button>
            </SignInButton>
          </SignedOut>
        </header>
        <main className="flex-1 p-4 sm:p-6">{children}</main>
      </div>
    </SidebarProvider>
  )
}
