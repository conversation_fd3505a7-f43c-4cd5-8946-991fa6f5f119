"use client"

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Mail, Send, CheckCircle, AlertCircle } from "lucide-react"

type NotificationType = 'partner-status' | 'deal-status'

export function NotificationCenter() {
  const { toast } = useToast()
  const [partners, setPartners] = React.useState<any[]>([])
  const [deals, setDeals] = React.useState<any[]>([])
  const [loading, setLoading] = React.useState(false)
  const [selectedType, setSelectedType] = React.useState<NotificationType>('partner-status')
  const [selectedPartner, setSelectedPartner] = React.useState('')
  const [selectedDeal, setSelectedDeal] = React.useState('')
  const [statusChange, setStatusChange] = React.useState('')
  const [customMessage, setCustomMessage] = React.useState('')
  const [recentNotifications, setRecentNotifications] = React.useState<any[]>([])

  React.useEffect(() => {
    fetchPartners()
    fetchDeals()
    fetchRecentNotifications()
  }, [])

  const fetchPartners = async () => {
    try {
      const response = await fetch('/api/admin/partners')
      if (response.ok) {
        const data = await response.json()
        setPartners(data.data || [])
      }
    } catch (error) {
      console.error('Failed to fetch partners:', error)
    }
  }

  const fetchDeals = async () => {
    try {
      const response = await fetch('/api/deals')
      if (response.ok) {
        const data = await response.json()
        setDeals(data.data || [])
      }
    } catch (error) {
      console.error('Failed to fetch deals:', error)
    }
  }

  const fetchRecentNotifications = async () => {
    // This would fetch from a notifications log table
    // For now, we'll simulate recent notifications
    setRecentNotifications([
      {
        id: '1',
        type: 'partner-approved',
        recipient: '<EMAIL>',
        subject: 'Welcome to IBC Partners Program!',
        sentAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        status: 'delivered'
      },
      {
        id: '2',
        type: 'deal-confirmed',
        recipient: '<EMAIL>',
        subject: 'Deal Update: TechCorp - Confirmed',
        sentAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        status: 'delivered'
      }
    ])
  }

  const sendPartnerNotification = async () => {
    if (!selectedPartner || !statusChange) {
      toast({ 
        title: "Missing fields", 
        description: "Please select a partner and status change.",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/notifications/partner-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          partnerId: selectedPartner,
          status: statusChange,
          reason: customMessage || undefined,
          tier: statusChange === 'active' ? 'trusted' : undefined
        })
      })

      if (response.ok) {
        toast({ 
          title: "Notification sent", 
          description: "Partner status notification has been sent successfully." 
        })
        setSelectedPartner('')
        setStatusChange('')
        setCustomMessage('')
        fetchRecentNotifications()
      } else {
        throw new Error('Failed to send notification')
      }
    } catch (error) {
      toast({ 
        title: "Failed to send", 
        description: "There was an error sending the notification.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const sendDealNotification = async () => {
    if (!selectedDeal || !statusChange) {
      toast({ 
        title: "Missing fields", 
        description: "Please select a deal and status change.",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/notifications/deal-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          dealId: selectedDeal,
          status: statusChange,
          statusMessage: customMessage || undefined
        })
      })

      if (response.ok) {
        toast({ 
          title: "Notification sent", 
          description: "Deal status notification has been sent successfully." 
        })
        setSelectedDeal('')
        setStatusChange('')
        setCustomMessage('')
        fetchRecentNotifications()
      } else {
        throw new Error('Failed to send notification')
      }
    } catch (error) {
      toast({ 
        title: "Failed to send", 
        description: "There was an error sending the notification.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSendNotification = () => {
    if (selectedType === 'partner-status') {
      sendPartnerNotification()
    } else {
      sendDealNotification()
    }
  }

  const getStatusOptions = () => {
    if (selectedType === 'partner-status') {
      return [
        { value: 'active', label: 'Approve Application' },
        { value: 'rejected', label: 'Reject Application' }
      ]
    } else {
      return [
        { value: 'pending', label: 'Pending' },
        { value: 'confirmed', label: 'Confirmed' },
        { value: 'rejected', label: 'Rejected' },
        { value: 'paid', label: 'Paid' }
      ]
    }
  }

  return (
    <div className="grid gap-6 md:grid-cols-2">
      {/* Send Notification */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Send Notification
          </CardTitle>
          <CardDescription>
            Manually send status update notifications to partners
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Notification Type</Label>
            <Select value={selectedType} onValueChange={(value) => setSelectedType(value as NotificationType)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="partner-status">Partner Status Change</SelectItem>
                <SelectItem value="deal-status">Deal Status Change</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {selectedType === 'partner-status' ? (
            <div className="space-y-2">
              <Label>Partner</Label>
              <Select value={selectedPartner} onValueChange={setSelectedPartner}>
                <SelectTrigger>
                  <SelectValue placeholder="Select partner" />
                </SelectTrigger>
                <SelectContent>
                  {partners.map(partner => (
                    <SelectItem key={partner.id} value={partner.id}>
                      {partner.full_name} ({partner.email})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          ) : (
            <div className="space-y-2">
              <Label>Deal</Label>
              <Select value={selectedDeal} onValueChange={setSelectedDeal}>
                <SelectTrigger>
                  <SelectValue placeholder="Select deal" />
                </SelectTrigger>
                <SelectContent>
                  {deals.map(deal => (
                    <SelectItem key={deal.id} value={deal.id}>
                      {deal.client_company} - ${deal.deal_value.toLocaleString()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="space-y-2">
            <Label>Status Change</Label>
            <Select value={statusChange} onValueChange={setStatusChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {getStatusOptions().map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Custom Message (Optional)</Label>
            <Textarea
              placeholder={selectedType === 'partner-status' 
                ? "Reason for rejection (if applicable)..." 
                : "Additional status update message..."}
              value={customMessage}
              onChange={(e) => setCustomMessage(e.target.value)}
              rows={3}
            />
          </div>

          <Button onClick={handleSendNotification} disabled={loading} className="w-full gap-2">
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
            Send Notification
          </Button>
        </CardContent>
      </Card>

      {/* Recent Notifications */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Notifications</CardTitle>
          <CardDescription>
            Latest email notifications sent to partners
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentNotifications.length > 0 ? (
              recentNotifications.map(notification => (
                <div key={notification.id} className="flex items-start justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      {notification.status === 'delivered' ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-yellow-600" />
                      )}
                      <span className="font-medium text-sm">{notification.subject}</span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      To: {notification.recipient}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(notification.sentAt).toLocaleString()}
                    </div>
                  </div>
                  <Badge variant={notification.status === 'delivered' ? 'default' : 'secondary'}>
                    {notification.status}
                  </Badge>
                </div>
              ))
            ) : (
              <div className="text-center py-6 text-muted-foreground">
                No recent notifications
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}