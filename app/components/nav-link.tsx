"use client"

import type React from "react"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

type NavLinkProps = {
  href: string
  children: React.ReactNode
  mobile?: boolean
}

export default function NavLink({ href, children, mobile = false }: NavLinkProps) {
  const pathname = usePathname()
  const isActive = pathname === href

  if (mobile) {
    return (
      <Link
        href={href}
        className={cn(
          "flex items-center gap-4 px-2.5",
          isActive ? "text-foreground" : "text-muted-foreground hover:text-foreground",
        )}
      >
        {children}
      </Link>
    )
  }

  return (
    <Link
      href={href}
      className={cn(
        "flex h-9 w-9 items-center justify-center rounded-lg transition-colors md:h-8 md:w-8",
        isActive ? "bg-accent text-accent-foreground" : "text-muted-foreground hover:text-foreground",
      )}
    >
      {children}
    </Link>
  )
}
