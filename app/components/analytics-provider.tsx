"use client"

import { createContext, useContext, useEffect, ReactNode } from 'react'
import { useAuth } from '@clerk/nextjs'
import { usePathname } from 'next/navigation'
import { analytics } from '@/lib/analytics'

const AnalyticsContext = createContext(analytics)

interface AnalyticsProviderProps {
  children: ReactNode
}

export function AnalyticsProvider({ children }: AnalyticsProviderProps) {
  const { userId } = useAuth()
  const pathname = usePathname()

  useEffect(() => {
    if (userId) {
      analytics.setUserId(userId)
    }
  }, [userId])

  // Track page views
  useEffect(() => {
    const page = pathname.split('/')[1] || 'dashboard'
    
    // Track specific page views
    if (page === 'earnings') {
      analytics.trackPageView('earnings')
    } else if (pathname === '/') {
      analytics.trackPageView('dashboard')
    }
  }, [pathname])

  return (
    <AnalyticsContext.Provider value={analytics}>
      {children}
    </AnalyticsContext.Provider>
  )
}

export function useAnalyticsContext() {
  return useContext(AnalyticsContext)
}