"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"

export function OnboardingBanner() {
  // In a real app, this would be driven by user state.
  const onboardingComplete = false
  const progress = 50 // Example progress

  if (onboardingComplete) {
    return null
  }

  return (
    <div className="w-full bg-background border-b">
      <div className="px-4 sm:px-6 py-3">
        <div className="flex flex-wrap items-center justify-between gap-x-4 gap-y-2">
          <div className="flex-grow">
            <p className="text-sm font-semibold">Finish setting up your account to unlock all features.</p>
            <div className="flex items-center gap-x-2">
              <Progress value={progress} className="w-40 h-2" />
              <span className="text-xs text-muted-foreground">{progress}% complete</span>
            </div>
          </div>
          <Button asChild size="sm">
            <Link href="/onboarding">Resume Onboarding</Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
