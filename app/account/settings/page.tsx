"use client"

import { useState, useEffect } from "react"
import { AppShell } from "@/app/components/app-shell"
import { <PERSON>Header } from "@/app/components/page-header"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Loader2, AlertCircle, CheckCircle, Clock } from "lucide-react"
import { toast } from "sonner"

interface ProfileData {
  full_name: string
  email: string
  telegram?: string
  whatsapp?: string
  x_profile?: string
  company_name: string
  company_type: string
  role: string
  preferred_communication: string[]
  preferred_payment_method: string
}

interface ChangeRequest {
  id: string
  field: string
  current_value: string
  requested_value: string
  reason: string
  status: 'pending' | 'approved' | 'rejected'
  created_at: string
  reviewed_at?: string
  reviewed_by?: string
  review_notes?: string
}

export default function SettingsPage() {
  const [profile, setProfile] = useState<ProfileData | null>(null)
  const [pendingChanges, setPendingChanges] = useState<ChangeRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [formData, setFormData] = useState<Partial<ProfileData>>({})
  const [changeReason, setChangeReason] = useState("")
  const [activeTab, setActiveTab] = useState("profile")

  useEffect(() => {
    fetchProfile()
    fetchPendingChanges()
  }, [])

  const fetchProfile = async () => {
    try {
      const response = await fetch('/api/profile')
      if (response.ok) {
        const data = await response.json()
        setProfile(data.profile)
        setFormData(data.profile)
      }
    } catch (error) {
      console.error('Error fetching profile:', error)
      toast.error('Failed to load profile data')
    } finally {
      setLoading(false)
    }
  }

  const fetchPendingChanges = async () => {
    try {
      const response = await fetch('/api/profile/change-requests')
      if (response.ok) {
        const data = await response.json()
        setPendingChanges(data.requests || [])
      }
    } catch (error) {
      console.error('Error fetching pending changes:', error)
    }
  }

  const submitChangeRequest = async (field: string, newValue: any) => {
    if (!changeReason.trim()) {
      toast.error('Please provide a reason for this change')
      return
    }

    setSubmitting(true)
    try {
      const response = await fetch('/api/profile/change-requests', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          field,
          current_value: profile?.[field as keyof ProfileData],
          requested_value: newValue,
          reason: changeReason
        })
      })

      if (response.ok) {
        toast.success('Change request submitted for review')
        setChangeReason("")
        fetchPendingChanges()
      } else {
        throw new Error('Failed to submit change request')
      }
    } catch (error) {
      console.error('Error submitting change:', error)
      toast.error('Failed to submit change request')
    } finally {
      setSubmitting(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4 text-yellow-500" />
      case 'approved': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'rejected': return <AlertCircle className="h-4 w-4 text-red-500" />
      default: return null
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending': return <Badge variant="secondary">Pending Review</Badge>
      case 'approved': return <Badge variant="default">Approved</Badge>
      case 'rejected': return <Badge variant="destructive">Rejected</Badge>
      default: return null
    }
  }

  if (loading) {
    return (
      <AppShell title="Settings">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </AppShell>
    )
  }

  return (
    <AppShell title="Settings">
      <div className="flex flex-col gap-6">
        <PageHeader 
          title="Account Settings" 
          subtitle="Manage your personal and company information. Changes require approval." 
        />

        {pendingChanges.length > 0 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              You have {pendingChanges.filter(c => c.status === 'pending').length} pending change request(s) awaiting review.
            </AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-1 sm:grid-cols-3">
            <TabsTrigger value="profile">Profile</TabsTrigger>
            <TabsTrigger value="company">Company</TabsTrigger>
            <TabsTrigger value="changes">
              Change Requests
              {pendingChanges.filter(c => c.status === 'pending').length > 0 && (
                <Badge variant="secondary" className="ml-2 text-xs">
                  {pendingChanges.filter(c => c.status === 'pending').length}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile">
            <Card>
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
                <CardDescription>
                  Update your personal details. Changes require ops team approval.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="full_name">Full Name</Label>
                  <Input 
                    id="full_name" 
                    value={formData.full_name || ''} 
                    onChange={(e) => setFormData({...formData, full_name: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input 
                    id="email" 
                    type="email" 
                    value={formData.email || ''} 
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                    disabled
                  />
                  <p className="text-xs text-muted-foreground">
                    Email changes require manual verification. Contact support.
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="telegram">Telegram Handle</Label>
                  <Input 
                    id="telegram" 
                    placeholder="@username"
                    value={formData.telegram || ''} 
                    onChange={(e) => setFormData({...formData, telegram: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="whatsapp">WhatsApp Number</Label>
                  <Input 
                    id="whatsapp" 
                    placeholder="+1234567890"
                    value={formData.whatsapp || ''} 
                    onChange={(e) => setFormData({...formData, whatsapp: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="x_profile">X (Twitter) Handle</Label>
                  <Input 
                    id="x_profile" 
                    placeholder="@username"
                    value={formData.x_profile || ''} 
                    onChange={(e) => setFormData({...formData, x_profile: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="reason">Reason for Changes</Label>
                  <Textarea 
                    id="reason"
                    placeholder="Please explain why you're making these changes..."
                    value={changeReason}
                    onChange={(e) => setChangeReason(e.target.value)}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  onClick={() => {
                    const hasChanges = Object.keys(formData).some(key => 
                      formData[key as keyof ProfileData] !== profile?.[key as keyof ProfileData]
                    )
                    if (hasChanges) {
                      submitChangeRequest('profile', formData)
                    } else {
                      toast.error('No changes to submit')
                    }
                  }}
                  disabled={submitting}
                >
                  {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Request Profile Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="company">
            <Card>
              <CardHeader>
                <CardTitle>Company Information</CardTitle>
                <CardDescription>
                  Update your company details. Changes require verification.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="company_name">Company Name</Label>
                  <Input 
                    id="company_name" 
                    value={formData.company_name || ''} 
                    onChange={(e) => setFormData({...formData, company_name: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="company_type">Company Type</Label>
                  <Select 
                    value={formData.company_type || ''} 
                    onValueChange={(value) => setFormData({...formData, company_type: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select company type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="startup">Startup</SelectItem>
                      <SelectItem value="enterprise">Enterprise</SelectItem>
                      <SelectItem value="agency">Agency</SelectItem>
                      <SelectItem value="consulting">Consulting</SelectItem>
                      <SelectItem value="investment">Investment Fund</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="role">Your Role</Label>
                  <Input 
                    id="role" 
                    value={formData.role || ''} 
                    onChange={(e) => setFormData({...formData, role: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="reason">Reason for Changes</Label>
                  <Textarea 
                    id="reason"
                    placeholder="Please explain why you're making these changes..."
                    value={changeReason}
                    onChange={(e) => setChangeReason(e.target.value)}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  onClick={() => {
                    const companyFields = ['company_name', 'company_type', 'role']
                    const hasChanges = companyFields.some(key => 
                      formData[key as keyof ProfileData] !== profile?.[key as keyof ProfileData]
                    )
                    if (hasChanges) {
                      submitChangeRequest('company', {
                        company_name: formData.company_name,
                        company_type: formData.company_type,
                        role: formData.role
                      })
                    } else {
                      toast.error('No changes to submit')
                    }
                  }}
                  disabled={submitting}
                >
                  {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Request Company Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>


          <TabsContent value="changes">
            <Card>
              <CardHeader>
                <CardTitle>Change Requests</CardTitle>
                <CardDescription>
                  Track the status of your submitted change requests.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {pendingChanges.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No change requests submitted yet.
                  </div>
                ) : (
                  <div className="space-y-4">
                    {pendingChanges.map((change) => (
                      <div key={change.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(change.status)}
                            <h4 className="font-medium capitalize">
                              {change.field.replace('_', ' ')} Change
                            </h4>
                          </div>
                          {getStatusBadge(change.status)}
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">Current:</span>
                            <p className="font-mono bg-muted p-2 rounded mt-1">
                              {typeof change.current_value === 'object' 
                                ? JSON.stringify(change.current_value, null, 2)
                                : change.current_value || 'Not set'
                              }
                            </p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Requested:</span>
                            <p className="font-mono bg-muted p-2 rounded mt-1">
                              {typeof change.requested_value === 'object' 
                                ? JSON.stringify(change.requested_value, null, 2)
                                : change.requested_value
                              }
                            </p>
                          </div>
                        </div>
                        <div className="mt-3">
                          <span className="text-muted-foreground text-sm">Reason:</span>
                          <p className="text-sm mt-1">{change.reason}</p>
                        </div>
                        <div className="flex justify-between items-center mt-3 text-xs text-muted-foreground">
                          <span>Submitted {new Date(change.created_at).toLocaleDateString()}</span>
                          {change.reviewed_at && (
                            <span>Reviewed {new Date(change.reviewed_at).toLocaleDateString()}</span>
                          )}
                        </div>
                        {change.review_notes && (
                          <div className="mt-2 p-2 bg-muted rounded text-sm">
                            <span className="font-medium">Review Notes:</span>
                            <p className="mt-1">{change.review_notes}</p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppShell>
  )
}
