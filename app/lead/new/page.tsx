"use client"

import * as React from "react"
import { useAuth } from '@clerk/nextjs'
import { AppShell } from "@/app/components/app-shell"
import { PageHeader } from "@/app/components/page-header"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { FileUp, Send, UserSquare, Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useAnalytics } from "@/lib/use-analytics"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

export default function LeadSubmissionPage() {
  const { userId } = useAuth()
  const { toast } = useToast()
  const analytics = useAnalytics()
  
  const [companyName, setCompanyName] = React.useState("")
  const [website, setWebsite] = React.useState("")
  const [pocName, setPocName] = React.useState("")
  const [pocTitle, setPocTitle] = React.useState("")
  const [pocEmail, setPocEmail] = React.useState("")
  const [pocTelegram, setPocTelegram] = React.useState("")
  const [notes, setNotes] = React.useState("")
  const [captcha, setCaptcha] = React.useState(false)
  const [showDuplicate, setShowDuplicate] = React.useState(false)
  const [duplicateData, setDuplicateData] = React.useState<any>(null)
  const [submitting, setSubmitting] = React.useState(false)
  const [profile, setProfile] = React.useState<any>(null)
  const [salesContact, setSalesContact] = React.useState<any>(null)
  const [formStarted, setFormStarted] = React.useState(false)
  const [referralAttribution, setReferralAttribution] = React.useState<{ partnerName: string; slug: string } | null>(null)

  // Load partner profile and sales contact
  React.useEffect(() => {
    const fetchProfile = async () => {
      if (!userId) return

      try {
        const response = await fetch('/api/profile')
        if (response.ok) {
          const data = await response.json()
          setProfile(data.profile)
          setSalesContact(data.salesContact)
        }
      } catch (error) {
        console.error('Failed to fetch profile:', error)
      }
    }

    fetchProfile()
  }, [userId])

  // Check for referral attribution and auto-fill POC name
  React.useEffect(() => {
    try {
      const cookie = document.cookie
        .split('; ')
        .find(row => row.startsWith('ibc_partner_ref='))
      
      if (cookie) {
        const attribution = JSON.parse(decodeURIComponent(cookie.split('=')[1]))
        if (attribution.partnerName && attribution.slug) {
          setReferralAttribution({
            partnerName: attribution.partnerName,
            slug: attribution.slug
          })
          
          // Auto-fill POC name only if it's currently empty
          if (!pocName) {
            setPocName(attribution.partnerName)
          }
        }
      }
    } catch (error) {
      console.warn('Failed to parse referral attribution cookie:', error)
    }
  }, [pocName])

  const requiredValid = companyName && website && pocName && pocEmail && captcha

  // Track form start when user begins typing
  const handleFormStart = React.useCallback(() => {
    if (!formStarted) {
      setFormStarted(true)
      analytics.trackLeadFormStart()
    }
  }, [formStarted, analytics])

  // Track form abandonment when user leaves without completing
  React.useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (formStarted && !submitting && !requiredValid) {
        analytics.trackLeadFormAbandon('incomplete_form')
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [formStarted, submitting, requiredValid, analytics])

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!requiredValid || submitting) return

    setSubmitting(true)

    try {
      // Get referral attribution from cookie
      let attribution = null
      try {
        const cookie = document.cookie
          .split('; ')
          .find(row => row.startsWith('ibc_partner_ref='))
        if (cookie) {
          attribution = JSON.parse(decodeURIComponent(cookie.split('=')[1]))
        }
      } catch (error) {
        console.warn('Failed to parse attribution cookie:', error)
      }

      const response = await fetch('/api/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          company_name: companyName,
          website,
          contact_name: pocName,
          contact_title: pocTitle,
          contact_email: pocEmail,
          contact_telegram: pocTelegram || null,
          notes: notes || null,
          attribution
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        
        // Handle duplicate detection
        if (response.status === 409 && error.duplicate) {
          setDuplicateData(error.duplicate)
          setShowDuplicate(true)
          return
        }
        
        throw new Error(error.error || 'Failed to submit lead')
      }

      const data = await response.json()
      
      // Track successful submission
      await analytics.trackLeadFormSubmit(data.lead_id, attribution)
      
      toast({
        title: "Lead submitted successfully!",
        description: "Our sales team will review your submission and be in touch soon.",
      })

      // Reset form
      setCompanyName("")
      setWebsite("")
      setPocName("")
      setPocTitle("")
      setPocEmail("")
      setPocTelegram("")
      setNotes("")
      setCaptcha(false)

    } catch (error) {
      console.error('Failed to submit lead:', error)
      toast({
        title: "Submission failed",
        description: error instanceof Error ? error.message : "Please try again later.",
        variant: "destructive",
      })
    } finally {
      setSubmitting(false)
    }
  }

  const useExisting = () => {
    setShowDuplicate(false)
    toast({
      title: "Using existing lead",
      description: "Redirecting to the existing lead details...",
    })
    // TODO: Navigate to existing lead
  }

  const submitAnyway = async () => {
    setShowDuplicate(false)
    setSubmitting(true)

    try {
      const response = await fetch('/api/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          company_name: companyName,
          website,
          contact_name: pocName,
          contact_title: pocTitle,
          contact_email: pocEmail,
          contact_telegram: pocTelegram || null,
          notes: notes || null,
          force: true // Force submission despite duplicate
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to submit lead')
      }

      toast({
        title: "Lead submitted successfully!",
        description: "Your duplicate lead has been submitted for review.",
      })

      // Reset form
      setCompanyName("")
      setWebsite("")
      setPocName("")
      setPocTitle("")
      setPocEmail("")
      setPocTelegram("")
      setNotes("")
      setCaptcha(false)

    } catch (error) {
      toast({
        title: "Submission failed",
        description: error instanceof Error ? error.message : "Please try again later.",
        variant: "destructive",
      })
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <AppShell title="Submit a Lead">
      <div className="flex flex-col gap-4">
        <PageHeader title="Connect Us" subtitle="Fill out the form to refer a new project." />
        
        {referralAttribution && (
          <Alert>
            <UserSquare className="h-4 w-4" />
            <AlertDescription>
              <strong>Referred by:</strong> {referralAttribution.partnerName}
              {pocName === referralAttribution.partnerName && (
                <span className="text-muted-foreground ml-2">(Auto-filled as Point of Contact)</span>
              )}
            </AlertDescription>
          </Alert>
        )}
        
        <form className="grid grid-cols-1 gap-8 lg:grid-cols-3" onSubmit={onSubmit}>
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Project & Contact Information</CardTitle>
                <CardDescription>All fields are required unless otherwise noted.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="companyName">Company / Project Name</Label>
                    <Input
                      id="companyName"
                      placeholder="Project Phoenix"
                      value={companyName}
                      onChange={(e) => setCompanyName(e.target.value)}
                      onFocus={handleFormStart}
                      aria-describedby="companyName-desc"
                    />
                    <p id="companyName-desc" className="text-xs text-muted-foreground">
                      Enter the official project name.
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      placeholder="https://projectphoenix.com"
                      value={website}
                      onChange={(e) => setWebsite(e.target.value)}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="pocName">Point of Contact Name</Label>
                    <Input
                      id="pocName"
                      placeholder="Jane Smith"
                      value={pocName}
                      onChange={(e) => setPocName(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="pocTitle">POC Title</Label>
                    <Input
                      id="pocTitle"
                      placeholder="CEO"
                      value={pocTitle}
                      onChange={(e) => setPocTitle(e.target.value)}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="pocEmail">POC Email</Label>
                    <Input
                      id="pocEmail"
                      type="email"
                      placeholder="<EMAIL>"
                      value={pocEmail}
                      onChange={(e) => setPocEmail(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="pocTelegram">POC Telegram (Optional)</Label>
                    <Input
                      id="pocTelegram"
                      placeholder="@janesmith"
                      value={pocTelegram}
                      onChange={(e) => setPocTelegram(e.target.value)}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notes">Notes (Optional)</Label>
                  <Textarea
                    id="notes"
                    placeholder="Tell us about the project, their needs, and why they're a good fit."
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label>File Drop (Optional)</Label>
                  <div className="flex items-center justify-center w-full">
                    <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-muted hover:bg-muted/80">
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <FileUp className="w-8 h-8 mb-4 text-muted-foreground" />
                        <p className="mb-2 text-sm text-muted-foreground">
                          <span className="font-semibold">Click to upload</span> or drag and drop
                        </p>
                        <p className="text-xs text-muted-foreground">PDF, DOCX, or PNG (MAX. 10MB)</p>
                      </div>
                      <Input type="file" className="hidden" />
                    </label>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="turnstile" checked={captcha} onCheckedChange={(v) => setCaptcha(Boolean(v))} />
                  <Label htmlFor="turnstile">I am not a robot</Label>
                </div>
              </CardContent>
            </Card>
          </div>
          <div className="space-y-6">
            <Card className="sticky top-20">
              <CardHeader>
                <CardTitle>Referring Partner</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4">
                  <div className="w-14 h-14 bg-muted rounded-full flex items-center justify-center">
                    <UserSquare className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <div>
                    <p className="font-semibold">{profile?.full_name || 'Loading...'}</p>
                    <p className="text-sm text-muted-foreground capitalize">
                      {profile?.tier || 'Partner'} Tier Partner
                    </p>
                  </div>
                </div>
                {salesContact && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Assigned Sales Contact</h4>
                    <div className="flex items-center gap-3">
                      <UserSquare className="h-8 w-8 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-semibold">{salesContact.full_name}</p>
                        <p className="text-xs text-muted-foreground">{salesContact.email}</p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
            <div className="flex gap-2">
              <Button type="button" variant="outline" className="w-full bg-transparent">
                Cancel
              </Button>
              <Button type="submit" className="w-full" disabled={!requiredValid || submitting}>
                {submitting ? (
                  <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Submitting...</>
                ) : (
                  <><Send className="mr-2 h-4 w-4" /> Submit</>
                )}
              </Button>
            </div>
          </div>
        </form>
      </div>

      <Dialog open={showDuplicate} onOpenChange={setShowDuplicate}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Possible duplicate lead</DialogTitle>
            <DialogDescription>
              We found a similar lead that may match your submission. Would you like to use the existing one?
            </DialogDescription>
          </DialogHeader>
          {duplicateData && (
            <div className="rounded-md border p-3 text-sm">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <span className="text-muted-foreground">Your submission</span>
                  <div className="font-medium">{companyName}</div>
                  <div className="text-xs text-muted-foreground">{website}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Existing lead</span>
                  <div className="font-medium">{duplicateData.company_name}</div>
                  <div className="text-xs text-muted-foreground">{duplicateData.website}</div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" className="bg-transparent" onClick={useExisting}>
              Use existing
            </Button>
            <Button onClick={submitAnyway}>Submit anyway</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AppShell>
  )
}
