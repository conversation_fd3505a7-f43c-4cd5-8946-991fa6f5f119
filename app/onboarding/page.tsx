"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react"
import { useAuth } from '@clerk/nextjs'
import { useRouter } from 'next/navigation'
import { AppShell } from "@/app/components/app-shell"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { User, Building, MessageSquare, FileCheck, CreditCard, ArrowRight, ArrowLeft, Loader2, CheckCircle, Shield } from "lucide-react"
import { companyTypes, communicationMethods, partnerRoles, type CommunicationMethod } from "@/lib/validations"

const steps = [
  {
    id: 1,
    title: "Personal Information",
    icon: <User className="h-5 w-5" />,
    fields: ["fullName"],
  },
  {
    id: 2,
    title: "Company Details", 
    icon: <Building className="h-5 w-5" />,
    fields: ["companyName", "companyType", "role", "xProfile", "internalPoc"],
  },
  {
    id: 3,
    title: "Communication",
    icon: <MessageSquare className="h-5 w-5" />,
    fields: ["preferredCommunication", "whatsapp", "telegram"],
  },
  {
    id: 4,
    title: "Payment Preference",
    icon: <CreditCard className="h-5 w-5" />,
    fields: ["preferredPaymentMethod"],
  },
  {
    id: 5,
    title: "Review & Terms",
    icon: <FileCheck className="h-5 w-5" />,
    fields: ["terms"],
  },
]

const companyTypeLabels: Record<string, string> = {
  'vc': 'Venture Capital (VC)',
  'cex': 'Central Exchange (CEX)', 
  'dex': 'Decentralized Exchange (DEX)',
  'launchpad': 'Launchpad',
  'marketing_agency': 'Marketing Agency',
  'incubator_accelerator': 'Incubator/Accelerator',
  'market_making': 'Market Making (MM)',
  'development': 'Development',
  'technology': 'Technology',
  'research': 'Research',
  'data_aggregation': 'Data and Aggregation Platform',
  'external_bd': 'External BD',
  'deal_flow_individual': 'Deal Flow Individual',
  'angel_investor': 'Angel Investor',
  'other': 'Other (Specify)'
}

const roleLabels: Record<string, string> = {
  'ceo': 'CEO',
  'cto': 'CTO',
  'cmo': 'CMO', 
  'bd': 'Business Development',
  'founder': 'Founder',
  'co_founder': 'Co-Founder',
  'partner': 'Partner',
  'director': 'Director',
  'manager': 'Manager',
  'other': 'Other (Specify)'
}

export default function OnboardingPage() {
  const { userId, isLoaded } = useAuth()
  const router = useRouter()
  const { toast } = useToast()
  
  const [currentStep, setCurrentStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [onboardingStatus, setOnboardingStatus] = useState<'not_started' | 'pending' | 'active' | null>(null)
  
  // Sales team state
  const [salesTeam, setSalesTeam] = useState<Array<{id: string, name: string, telegramHandle?: string, emailPrefix: string}>>([])  
  const [loadingSalesTeam, setLoadingSalesTeam] = useState(false)

  // Form data with all PRD fields
  const [formData, setFormData] = useState({
    // Personal Info
    fullName: '',
    
    // Company Info
    companyName: '',
    companyType: '',
    companyTypeOther: '',
    role: '',
    roleOther: '',
    xProfile: '',
    
    // Communication
    preferredCommunication: [] as CommunicationMethod[],
    whatsapp: '',
    telegram: '',
    
    // Payment Preference
    preferredPaymentMethod: '',
    
    // POC Selection
    internalPoc: '', // Will be selected from dropdown
    
    // Terms
    terms: false
  })
  
  // All hooks must be called before any conditional returns
  const updateFormData = useCallback((key: string, value: string | boolean | CommunicationMethod[]) => {
    setFormData(prev => ({ ...prev, [key]: value }))
  }, [])

  const handleCommunicationChange = useCallback((method: CommunicationMethod, checked: boolean) => {
    const current = formData.preferredCommunication
    if (checked) {
      updateFormData('preferredCommunication', [...current, method])
    } else {
      updateFormData('preferredCommunication', current.filter(m => m !== method))
    }
  }, [formData.preferredCommunication, updateFormData])

  // Fetch sales team when component mounts
  useEffect(() => {
    const fetchSalesTeam = async () => {
      setLoadingSalesTeam(true)
      try {
        const response = await fetch('/api/sales')
        if (response.ok) {
          const result = await response.json()
          setSalesTeam(result.data || [])
        } else {
          console.warn('Failed to fetch sales team:', response.status)
          toast({
            title: 'Warning',
            description: 'Could not load sales team. You can continue with the form.',
            variant: 'default'
          })
        }
      } catch (error) {
        console.error('Error fetching sales team:', error)
        toast({
          title: 'Warning', 
          description: 'Could not load sales team. You can continue with the form.',
          variant: 'default'
        })
      } finally {
        setLoadingSalesTeam(false)
      }
    }
    
    fetchSalesTeam()
  }, [])

  const StepContent = useMemo(() => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4" key="step-1">
            <div className="space-y-2">
              <Label htmlFor="fullName">Full Name *</Label>
              <Input 
                key="fullName-input"
                id="fullName" 
                placeholder="John Doe" 
                maxLength={50}
                value={formData.fullName || ''}
                onChange={(e) => updateFormData('fullName', e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                {formData.fullName?.length || 0}/50 characters
              </p>
            </div>
          </div>
        )
        
      case 2:
        return (
          <div className="space-y-4" key="step-2">
            <div className="space-y-2">
              <Label htmlFor="companyName">Company Name *</Label>
              <Input 
                id="companyName" 
                placeholder="Acme Blockchain Inc." 
                maxLength={50}
                value={formData.companyName}
                onChange={(e) => updateFormData('companyName', e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                {formData.companyName.length}/50 characters
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="companyType">Company Type *</Label>
              <Select value={formData.companyType} onValueChange={(value) => updateFormData('companyType', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select company type" />
                </SelectTrigger>
                <SelectContent>
                  {companyTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {companyTypeLabels[type]}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {formData.companyType === 'other' && (
              <div className="space-y-2">
                <Label htmlFor="companyTypeOther">Specify Company Type *</Label>
                <Input 
                  id="companyTypeOther" 
                  placeholder="Please specify your company type" 
                  maxLength={100}
                  value={formData.companyTypeOther}
                  onChange={(e) => updateFormData('companyTypeOther', e.target.value)}
                />
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="role">Your Role *</Label>
              <Select value={formData.role} onValueChange={(value) => updateFormData('role', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select your role" />
                </SelectTrigger>
                <SelectContent>
                  {partnerRoles.map((role) => (
                    <SelectItem key={role} value={role}>
                      {roleLabels[role]}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {formData.role === 'other' && (
              <div className="space-y-2">
                <Label htmlFor="roleOther">Specify Your Role *</Label>
                <Input 
                  id="roleOther" 
                  placeholder="Please specify your role" 
                  maxLength={50}
                  value={formData.roleOther}
                  onChange={(e) => updateFormData('roleOther', e.target.value)}
                />
              </div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="xProfile">X (Twitter) Profile</Label>
              <Input 
                id="xProfile" 
                type="url"
                placeholder="https://x.com/yourcompany" 
                maxLength={200}
                value={formData.xProfile}
                onChange={(e) => updateFormData('xProfile', e.target.value)}
              />
              <p className="text-xs text-muted-foreground">Optional: Your company&apos;s X profile</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="internalPoc">Preferred Point of Contact *</Label>
              <Select value={formData.internalPoc} onValueChange={(value) => updateFormData('internalPoc', value)}>
                <SelectTrigger>
                  <SelectValue placeholder={loadingSalesTeam ? "Loading sales team..." : "Select your preferred contact"} />
                </SelectTrigger>
                <SelectContent>
                  {loadingSalesTeam ? (
                    <SelectItem value="loading" disabled>Loading...</SelectItem>
                  ) : salesTeam.length > 0 ? (
                    salesTeam.map((member) => (
                      <SelectItem key={member.id} value={member.id}>
                        <div className="flex items-center gap-2">
                          <span>{member.name}</span>
                          {member.telegramHandle && (
                            <span className="text-xs text-muted-foreground">({member.telegramHandle})</span>
                          )}
                        </div>
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="ops-team">Operations Team (Default)</SelectItem>
                  )}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Choose your preferred sales representative who will be your main point of contact.
                {formData.internalPoc && salesTeam.find(m => m.id === formData.internalPoc)?.telegramHandle && (
                  <span className="block mt-1">
                    Telegram: {salesTeam.find(m => m.id === formData.internalPoc)?.telegramHandle}
                  </span>
                )}
              </p>
            </div>
          </div>
        )
        
      case 3:
        return (
          <div className="space-y-4" key="step-3">
            <div className="space-y-3">
              <Label>Preferred Communication Methods *</Label>
              <p className="text-sm text-muted-foreground">Select all methods you prefer for communication</p>
              <div className="space-y-3">
                {communicationMethods.map((method) => (
                  <div key={method} className="flex items-center space-x-2">
                    <Checkbox
                      id={method}
                      checked={formData.preferredCommunication.includes(method)}
                      onCheckedChange={(checked) => handleCommunicationChange(method, Boolean(checked))}
                    />
                    <Label htmlFor={method} className="capitalize">
                      {method === 'whatsapp' ? 'WhatsApp' : method === 'telegram' ? 'Telegram' : 'Email'}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {formData.preferredCommunication.includes('whatsapp') && (
              <div className="space-y-2">
                <Label htmlFor="whatsapp">WhatsApp Number *</Label>
                <Input 
                  id="whatsapp" 
                  placeholder="+1234567890" 
                  maxLength={20}
                  value={formData.whatsapp}
                  onChange={(e) => updateFormData('whatsapp', e.target.value)}
                />
                <p className="text-xs text-muted-foreground">Include country code (e.g., +1 for US)</p>
              </div>
            )}

            {formData.preferredCommunication.includes('telegram') && (
              <div className="space-y-2">
                <Label htmlFor="telegram">Telegram Handle *</Label>
                <Input 
                  id="telegram" 
                  placeholder="@johndoe" 
                  maxLength={32}
                  value={formData.telegram}
                  onChange={(e) => updateFormData('telegram', e.target.value)}
                />
                <p className="text-xs text-muted-foreground">Include @ symbol</p>
              </div>
            )}
            
            <div className="bg-blue-50 dark:bg-blue-950 rounded-lg p-3 text-sm">
              <p className="text-blue-700 dark:text-blue-300">
                <strong>Note:</strong> Your email address from account registration will also be used for important notifications.
              </p>
            </div>
          </div>
        )
        
      case 4:
        return (
          <div className="grid grid-cols-1 gap-4" key="step-4">
            <div className="space-y-2">
              <Label htmlFor="preferredPaymentMethod">Preferred Payment Method *</Label>
              <Select value={formData.preferredPaymentMethod} onValueChange={(value) => updateFormData('preferredPaymentMethod', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                  <SelectItem value="usdt">USDT (Crypto)</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Bank/wallet details will be collected during withdrawal process
              </p>
            </div>

          </div>
        )
        
      case 5:
        return (
          <div className="space-y-4" key="step-5">
            <div className="bg-muted/50 rounded-lg p-4 max-h-64 overflow-y-auto text-sm">
              <h4 className="font-semibold mb-2">Partnership Terms & Conditions</h4>
              <div className="space-y-2 text-muted-foreground">
                <p><strong>Commission Structure:</strong></p>
                <ul className="list-disc list-inside space-y-1 ml-2">
                  <li>Trusted Tier: 5% commission on successful deals</li>
                  <li>Elite Tier: 7.5% commission (requires $100k+ monthly volume)</li>
                  <li>Diamond Tier: 10% commission (requires $500k+ monthly volume)</li>
                </ul>
                
                <p className="pt-2"><strong>Payment Terms:</strong></p>
                <ul className="list-disc list-inside space-y-1 ml-2">
                  <li>Monthly commission payouts</li>
                  <li>Minimum payout threshold may apply</li>
                  <li>Bank transfer or USDT payment options</li>
                </ul>
                
                <p className="pt-2"><strong>Partner Obligations:</strong></p>
                <ul className="list-disc list-inside space-y-1 ml-2">
                  <li>Provide accurate referral attribution</li>
                  <li>Maintain professional representation of IBC Group</li>
                  <li>Comply with applicable laws and regulations</li>
                  <li>Use only approved marketing materials</li>
                </ul>
                
                <p className="pt-2 text-xs">
                  Full terms and conditions will be provided upon approval. 
                  This partnership agreement may be modified with notice.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-2 pt-4">
              <Checkbox 
                id="terms" 
                checked={formData.terms}
                onCheckedChange={(checked) => updateFormData('terms', Boolean(checked))}
              />
              <Label htmlFor="terms" className="text-sm leading-relaxed">
                I accept the partnership terms and conditions, and privacy policy. I confirm that all information provided is accurate and complete. *
              </Label>
            </div>
          </div>
        )
      default:
        return null
    }
  }, [currentStep, formData, updateFormData, handleCommunicationChange])
  
  // Check onboarding status
  useEffect(() => {
    // Only check status when auth is fully loaded AND we have a userId
    if (!isLoaded) {
      console.log('ONBOARDING: Auth not loaded yet')
      return
    }
    
    if (!userId) {
      console.log('ONBOARDING: No userId, will redirect to sign-in')
      return
    }
    
    let isCancelled = false
    
    const checkStatus = async () => {
      try {
        // First test if API routes are working
        console.log('ONBOARDING: Testing API access...')
        const testResponse = await fetch('/api/test', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'same-origin',
        })
        
        if (testResponse.ok) {
          const testData = await testResponse.json()
          console.log('ONBOARDING: Test API response:', testData)
        } else {
          console.error('ONBOARDING: Test API failed', testResponse.status)
        }
        
        console.log('ONBOARDING: Checking status for user', userId)
        const response = await fetch('/api/onboarding', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'same-origin',
        })
        
        if (!response.ok) {
          console.error('ONBOARDING: API error', response.status, response.statusText)
          const errorText = await response.text()
          console.error('ONBOARDING: Response body:', errorText.substring(0, 200))
          
          // If we get a 401, the user is not authenticated properly
          if (response.status === 401) {
            console.error('ONBOARDING: Unauthorized - redirecting to sign-in')
            router.push('/sign-in')
            return
          }
          
          setOnboardingStatus('not_started')
          return
        }
        
        const responseText = await response.text()
        console.log('ONBOARDING: Raw response:', responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''))
        
        // Check if response looks like HTML (indicating a redirect or error page)
        if (responseText.trim().startsWith('<!DOCTYPE') || responseText.trim().startsWith('<html')) {
          console.error('ONBOARDING: Received HTML instead of JSON - likely a redirect or error page')
          setOnboardingStatus('not_started') // Default to showing form
          return
        }
        
        let data
        try {
          data = JSON.parse(responseText)
        } catch (parseError) {
          console.error('ONBOARDING: JSON parse error:', parseError)
          console.error('ONBOARDING: Response was:', responseText.substring(0, 500))
          setOnboardingStatus('not_started') // Default to showing form
          return
        }
        
        console.log('ONBOARDING: Status response', data)
        
        if (!isCancelled) {
          if (data.data.completed) {
            setOnboardingStatus(data.data.status)
            console.log('ONBOARDING: Setting status to', data.data.status)
            
            if (data.data.status === 'active') {
              console.log('ONBOARDING: Redirecting to dashboard')
              router.push('/')
              return
            }
          } else {
            console.log('ONBOARDING: No profile found, showing form')
            setOnboardingStatus('not_started')
          }
        }
      } catch (error) {
        if (!isCancelled) {
          console.error('ONBOARDING: Failed to check status', error)
          setOnboardingStatus('not_started') // Default to showing form
        }
      }
    }
    
    checkStatus()
    
    return () => {
      isCancelled = true
    }
  }, [userId, isLoaded, router])
  
  // Early returns after all hooks have been called
  if (!isLoaded) {
    console.log('ONBOARDING: Showing loading state')
    return (
      <AppShell title="Loading..." hideNavigation={true}>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </AppShell>
    )
  }
  
  if (!userId) {
    console.log('ONBOARDING: No userId, redirecting to sign-in')
    router.push('/sign-in')
    return null
  }
  
  if (onboardingStatus === 'pending') {
    console.log('ONBOARDING: Showing success screen for pending status')
    return (
      <AppShell title="Application Submitted" hideNavigation={true}>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <CheckCircle className="h-16 w-16 text-green-500" />
          <h1 className="text-2xl font-bold">Partner Application Submitted!</h1>
          <p className="text-center text-muted-foreground max-w-md">
            Your partnership application has been submitted and is pending approval from our operations team. 
            You&apos;ll be notified via email once your account is approved (typically 2-3 business days).
          </p>
        </div>
      </AppShell>
    )
  }

  const handleNext = () => {
    if (!validateCurrentStep()) {
      return
    }
    
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrev = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }
  
  const validateCurrentStep = () => {
    switch (currentStep) {
      case 1:
        if (!formData.fullName) {
          toast({ title: 'Missing fields', description: 'Please fill in your full name.', variant: 'destructive' })
          return false
        }
        if (formData.fullName.length > 50) {
          toast({ title: 'Invalid input', description: 'Full name must be less than 50 characters.', variant: 'destructive' })
          return false
        }
        break
      case 2:
        if (!formData.companyName || !formData.companyType || !formData.role || !formData.internalPoc) {
          toast({ title: 'Missing fields', description: 'Please fill in all required fields including your preferred point of contact.', variant: 'destructive' })
          return false
        }
        if (formData.companyType === 'other' && !formData.companyTypeOther) {
          toast({ title: 'Missing specification', description: 'Please specify your company type.', variant: 'destructive' })
          return false
        }
        if (formData.role === 'other' && !formData.roleOther) {
          toast({ title: 'Missing specification', description: 'Please specify your role.', variant: 'destructive' })
          return false
        }
        if (formData.companyName.length > 50) {
          toast({ title: 'Invalid input', description: 'Company name must be less than 50 characters.', variant: 'destructive' })
          return false
        }
        break
      case 3:
        if (formData.preferredCommunication.length === 0) {
          toast({ title: 'Missing selection', description: 'Please select at least one communication method.', variant: 'destructive' })
          return false
        }
        if (formData.preferredCommunication.includes('whatsapp') && !formData.whatsapp) {
          toast({ title: 'Missing WhatsApp', description: 'WhatsApp number is required when selected as communication method.', variant: 'destructive' })
          return false
        }
        if (formData.preferredCommunication.includes('telegram') && !formData.telegram) {
          toast({ title: 'Missing Telegram', description: 'Telegram handle is required when selected as communication method.', variant: 'destructive' })
          return false
        }
        break
      case 4:
        if (!formData.preferredPaymentMethod) {
          toast({ title: 'Missing field', description: 'Please select your preferred payment method.', variant: 'destructive' })
          return false
        }
        // Country validation removed since we no longer collect billing address
        break
      case 5:
        if (!formData.terms) {
          toast({ title: 'Terms required', description: 'You must accept the terms and conditions.', variant: 'destructive' })
          return false
        }
        break
    }
    return true
  }
  
  const handleFinish = async () => {
    if (!validateCurrentStep()) {
      return
    }
    
    setLoading(true)
    
    try {
      const onboardingData = {
        // Personal Info
        full_name: formData.fullName,
        
        // Communication 
        preferred_communication: formData.preferredCommunication,
        telegram: formData.telegram || undefined,
        whatsapp: formData.whatsapp || undefined,
        
        // Company Info
        company_name: formData.companyName,
        company_type: formData.companyType as any,
        company_type_other: formData.companyTypeOther || undefined,
        x_profile: formData.xProfile || undefined,
        
        // Role
        role: formData.role as any,
        role_other: formData.roleOther || undefined,
        
        // Internal POC
        internal_poc: formData.internalPoc,
        
        // Payment Preference
        preferred_payment_method: formData.preferredPaymentMethod as 'bank_transfer' | 'usdt',
        
        // Terms
        terms_accepted: formData.terms,
        privacy_accepted: formData.terms,
        terms_version: '1.0'
      }
      
      const response = await fetch('/api/onboarding', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(onboardingData)
      })
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error('ONBOARDING: POST error response:', errorText)
        
        if (response.status === 409) {
          toast({ title: 'Already exists', description: 'Your profile already exists.', variant: 'destructive' })
          return
        }
        
        let errorMessage = 'Failed to submit onboarding'
        try {
          const errorData = JSON.parse(errorText)
          errorMessage = errorData.error || errorMessage
        } catch {
          // If not JSON, use the raw text
          errorMessage = errorText || errorMessage
        }
        
        throw new Error(errorMessage)
      }
      
      const resultText = await response.text()
      let result
      try {
        result = JSON.parse(resultText)
      } catch (parseError) {
        console.error('ONBOARDING: Failed to parse success response:', parseError)
        console.error('ONBOARDING: Response was:', resultText)
        throw new Error('Invalid response from server')
      }
      
      toast({ title: 'Success!', description: result.data.message })
      setOnboardingStatus('pending')
      
    } catch (error) {
      console.error('Onboarding error:', error)
      toast({ 
        title: 'Error', 
        description: error instanceof Error ? error.message : 'Failed to submit onboarding', 
        variant: 'destructive' 
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <AppShell title="Partner Application" hideNavigation={true}>
      <div className="mx-auto w-full max-w-4xl">
        <Card>
          <CardHeader>
            <div className="mb-4">
              <ol className="flex items-center justify-center w-full">
                {steps.map((step, index) => (
                  <li
                    key={step.id}
                    className={`flex w-full items-center ${
                      index < steps.length - 1
                        ? "after:content-[''] after:w-full after:h-1 after:border-b after:border-4 after:inline-block"
                        : ""
                    } ${step.id < currentStep ? "after:border-primary" : "after:border-muted"}`}
                  >
                    <span
                      className={`flex items-center justify-center w-10 h-10 rounded-full lg:h-12 lg:w-12 shrink-0 ${
                        step.id <= currentStep ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
                      }`}
                    >
                      {step.icon}
                    </span>
                  </li>
                ))}
              </ol>
            </div>
            <CardTitle>{steps[currentStep - 1].title}</CardTitle>
            <CardDescription>
              Step {currentStep} of {steps.length}. Please fill out the details below.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {StepContent}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={handlePrev} disabled={currentStep === 1}>
              <ArrowLeft className="mr-2 h-4 w-4" /> Previous
            </Button>
            {currentStep < steps.length ? (
              <Button onClick={handleNext}>
                Next <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            ) : (
              <Button onClick={handleFinish} disabled={loading}>
                {loading ? (
                  <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Submitting...</>
                ) : (
                  <>Submit Application</>
                )}
              </Button>
            )}
          </CardFooter>
        </Card>
      </div>
    </AppShell>
  )
}