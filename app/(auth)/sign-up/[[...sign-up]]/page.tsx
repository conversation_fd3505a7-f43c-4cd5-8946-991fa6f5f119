import { SignUp } from '@clerk/nextjs'
import Link from 'next/link'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'

export default function SignUpPage() {
  return (
    <div className="flex flex-col items-center space-y-6">
      {/* Back to apply page */}
      <div className="w-full flex justify-start">
        <Button variant="ghost" asChild size="sm">
          <Link href="/apply">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Partnership Info
          </Link>
        </Button>
      </div>

      <div className="text-center">
        <Badge variant="outline" className="mb-4">
          🚀 Step 1: Create Account
        </Badge>
        <h1 className="text-3xl font-bold mb-2">Join IBC Partner Portal</h1>
        <p className="text-muted-foreground mb-4">
          Create your account to begin the partner application process
        </p>
        
        {/* Process steps */}
        <div className="bg-muted/50 rounded-lg p-4 text-sm mb-6">
          <h3 className="font-semibold mb-3">What happens after signup:</h3>
          <div className="space-y-2 text-left">
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 shrink-0" />
              <span>Email verification (automatic)</span>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 shrink-0" />
              <span>Complete partner application form (5 minutes)</span>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 shrink-0" />
              <span>Review by our operations team (2-3 business days)</span>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 shrink-0" />
              <span>Dashboard access and start earning commissions</span>
            </div>
          </div>
        </div>
      </div>
      
      <SignUp 
        appearance={{
          elements: {
            card: 'w-full',
          }
        }}
        routing="path"
        path="/sign-up"
        signInUrl="/sign-in"
        redirectUrl="/onboarding"
      />
      
      <div className="text-center text-sm space-y-3">
        <div className="bg-blue-50 dark:bg-blue-950 rounded-lg p-3">
          <p className="text-blue-700 dark:text-blue-300 font-medium">
            ✅ No upfront costs or commitments required
          </p>
          <p className="text-blue-600 dark:text-blue-400 text-xs">
            Only successful partnerships generate commissions
          </p>
        </div>
        
        <p className="text-muted-foreground">
          Already have an account? <Link href="/sign-in" className="underline hover:no-underline">Sign in here</Link>
        </p>
        
        <p className="text-xs text-muted-foreground">
          Questions? Contact us at <strong><EMAIL></strong>
        </p>
      </div>
    </div>
  )
}