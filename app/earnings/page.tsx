import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { format } from 'date-fns'
import Link from 'next/link'

import { AppShell } from "@/app/components/app-shell"
import { PageHeader } from "@/app/components/page-header"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DollarSign, TrendingUp, Wallet, ArrowUpRight } from "lucide-react"
import { createClient } from '@/lib/supabase/server'
import { getCurrentUserRole } from '@/lib/rbac'
import { log } from '@/lib/log'

type EarningsData = {
  profile: {
    full_name: string
    tier: 'trusted' | 'elite' | 'diamond'
  }
  metrics: {
    totalWithdrawn: number
    pendingPayout: number
    estimatedFuture: number
  }
  deals: Array<{
    id: string
    client_company: string
    commission_amount: number
    status: 'pending' | 'confirmed' | 'paid' | 'disputed'
    created_at: string
    payment_date?: string
  }>
}

async function getEarningsData(): Promise<EarningsData> {
  const { userId } = await auth()
  
  if (!userId) {
    redirect('/sign-in')
  }

  try {
    // Check user role first
    const role = await getCurrentUserRole()
    if (!role) {
      redirect('/access-pending')
    }

    // Use service role for profile lookup, regular client for data
    const serviceClient = await createClient(true)
    const client = await createClient()

    // Get profile using service role
    const { data: profile, error: profileError } = await serviceClient
      .from('partners_profiles')
      .select('full_name, tier')
      .eq('id', userId)
      .single()

    if (profileError || !profile) {
      log.error('Profile not found for earnings', { 
        userId,
        error: profileError?.message,
        metadata: {
          code: profileError?.code,
          hint: profileError?.hint
        }
      })
      redirect('/access-pending')
    }

    // Get all deals and earnings using regular client (RLS will filter)
    const { data: deals, error: dealsError } = await client
      .from('partners_deals')
      .select('id, client_company, commission_amount, status, created_at, payment_date')
      .eq('partner_id', userId)
      .order('created_at', { ascending: false })

    if (dealsError) {
      log.error('Failed to fetch deals for earnings', {
        userId,
        error: dealsError.message,
        metadata: {
          code: dealsError.code,
          hint: dealsError.hint
        }
      })
      // Don't redirect, just use empty deals array
    }

    // Calculate metrics - ensure deals is an array
    const dealsArray = deals || []
    
    const totalWithdrawn = dealsArray
      .filter(d => d.status === 'paid')
      .reduce((sum, d) => sum + Number(d.commission_amount), 0)

    const pendingPayout = dealsArray
      .filter(d => d.status === 'confirmed')
      .reduce((sum, d) => sum + Number(d.commission_amount), 0)

    const estimatedFuture = dealsArray
      .filter(d => d.status === 'pending')
      .reduce((sum, d) => sum + Number(d.commission_amount), 0)


    return {
      profile,
      metrics: {
        totalWithdrawn,
        pendingPayout,
        estimatedFuture
      },
      deals: dealsArray
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    log.error('Failed to fetch earnings data', { 
      userId, 
      error: errorMessage,
      metadata: {
        stack: error instanceof Error ? error.stack : undefined
      }
    })
    throw new Error('Failed to load earnings data')
  }
}

export default async function EarningsPage() {
  const data = await getEarningsData()

  const formatCurrency = (amount: number) =>
    new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount)
  return (
    <AppShell title="Earnings">
      <div className="flex flex-col gap-4">
        <PageHeader 
          title="Earnings" 
          subtitle="Track your commissions and manage withdrawals."
          actions={
            <Button asChild disabled={data.metrics.pendingPayout === 0}>
              <Link href="/withdrawals/new">
                Withdraw Funds
              </Link>
            </Button>
          }
        />
        
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Withdrawn</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(data.metrics.totalWithdrawn)}</div>
              <p className="text-xs text-muted-foreground">All-time earnings paid out</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Payout</CardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(data.metrics.pendingPayout)}</div>
              <p className="text-xs text-muted-foreground">
                {data.metrics.pendingPayout > 0 ? 'Available for withdrawal' : 'No funds pending'}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Estimated Future</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(data.metrics.estimatedFuture)}</div>
              <p className="text-xs text-muted-foreground">From deals currently in progress</p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="flex flex-row items-center">
            <div className="grid gap-2">
              <CardTitle>Earnings History</CardTitle>
              <CardDescription>A detailed breakdown of your earnings by deal.</CardDescription>
            </div>
            <Button asChild size="sm" className="ml-auto gap-1">
              <Link href="/deals">
                View All Deals
                <ArrowUpRight className="h-4 w-4" />
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Client</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="hidden sm:table-cell">Date</TableHead>
                  <TableHead className="hidden sm:table-cell">Payment Date</TableHead>
                  <TableHead className="text-right">Commission</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.deals.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center text-muted-foreground h-24">
                      No earnings yet. Start referring clients to earn commissions!
                    </TableCell>
                  </TableRow>
                ) : (
                  data.deals.map((deal) => (
                    <TableRow key={deal.id}>
                      <TableCell className="font-medium">{deal.client_company}</TableCell>
                      <TableCell>
                        <Badge 
                          variant={
                            deal.status === 'paid' ? 'default' : 
                            deal.status === 'confirmed' ? 'secondary' : 
                            deal.status === 'disputed' ? 'destructive' : 'outline'
                          }
                          className="capitalize"
                        >
                          {deal.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="hidden sm:table-cell">
                        {format(new Date(deal.created_at), 'MMM dd, yyyy')}
                      </TableCell>
                      <TableCell className="hidden sm:table-cell">
                        {deal.payment_date ? format(new Date(deal.payment_date), 'MMM dd, yyyy') : '-'}
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        {formatCurrency(Number(deal.commission_amount))}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
