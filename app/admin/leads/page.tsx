"use client"

import { useState, useEffect } from "react"
import { Clock, User, Building2, Mail, Phone, MessageSquare, ExternalLink, AlertCircle, CheckCircle, XCircle, Eye } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabase/client"

type Application = {
  id: string
  email: string
  full_name: string
  company_name: string
  company_type: string
  company_type_other?: string
  role: string
  role_other?: string
  phone?: string
  telegram?: string
  whatsapp?: string
  x_profile?: string
  billing_address?: any
  preferred_communication?: string[]
  preferred_payment_method?: string
  status: 'pending' | 'under_review' | 'approved' | 'rejected' | 'withdrawn'
  reviewed_by?: string
  reviewed_at?: string
  review_notes?: string
  rejection_reason?: string
  referral_source?: string
  referral_partner_id?: string
  priority: 1 | 2 | 3
  terms_accepted: boolean
  privacy_accepted: boolean
  created_at: string
  updated_at: string
}

type Activity = {
  id: string
  action: string
  performed_by: string
  notes: string
  metadata: any
  created_at: string
}

const statusConfig = {
  pending: { 
    label: "Pending", 
    color: "bg-yellow-500", 
    icon: Clock,
    description: "Awaiting initial review" 
  },
  under_review: { 
    label: "Under Review", 
    color: "bg-blue-500", 
    icon: Eye,
    description: "Currently being evaluated" 
  },
  approved: { 
    label: "Approved", 
    color: "bg-green-500", 
    icon: CheckCircle,
    description: "Approved and ready for onboarding" 
  },
  rejected: { 
    label: "Rejected", 
    color: "bg-red-500", 
    icon: XCircle,
    description: "Application declined" 
  },
  withdrawn: { 
    label: "Withdrawn", 
    color: "bg-gray-500", 
    icon: AlertCircle,
    description: "Applicant withdrew their application" 
  }
}

const priorityConfig = {
  1: { label: "Low", color: "bg-gray-500" },
  2: { label: "Medium", color: "bg-yellow-500" },
  3: { label: "High", color: "bg-red-500" }
}

export default function AdminLeads() {
  const [applications, setApplications] = useState<Application[]>([])
  const [activities, setActivities] = useState<Activity[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedApp, setSelectedApp] = useState<Application | null>(null)
  const [reviewNotes, setReviewNotes] = useState("")
  const [rejectionReason, setRejectionReason] = useState("")
  const [actionLoading, setActionLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("all")
  
  const { toast } = useToast()
  const supabase = createClient()

  useEffect(() => {
    fetchApplications()
  }, [])

  useEffect(() => {
    if (selectedApp) {
      fetchActivities(selectedApp.id)
    }
  }, [selectedApp])

  const fetchApplications = async () => {
    try {
      console.log("Fetching applications via API...")
      const response = await fetch("/api/admin/applications")
      
      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`)
      }
      
      const result = await response.json()
      console.log("Applications API response:", result)
      
      setApplications(result.data || [])
    } catch (error) {
      console.error("Error fetching applications:", error)
      toast({
        title: "Error",
        description: "Failed to load applications",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchActivities = async (applicationId: string) => {
    try {
      const { data, error } = await supabase
        .from("partners_application_activities")
        .select("*")
        .eq("application_id", applicationId)
        .order("created_at", { ascending: false })

      if (error) throw error
      setActivities(data || [])
    } catch (error) {
      console.error("Error fetching activities:", error)
    }
  }

  const handleStatusUpdate = async (applicationId: string, newStatus: Application["status"], notes?: string, rejection?: string) => {
    setActionLoading(true)
    
    try {
      const response = await fetch("/api/admin/applications", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          application_id: applicationId,
          status: newStatus,
          review_notes: notes,
          rejection_reason: rejection
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Failed to update application")
      }

      toast({
        title: "Success",
        description: `Application ${newStatus === 'approved' ? 'approved' : newStatus === 'rejected' ? 'rejected' : 'updated'} successfully`,
      })

      // Refresh data
      await fetchApplications()
      if (selectedApp) {
        await fetchActivities(selectedApp.id)
      }
      
      // Close dialog
      setSelectedApp(null)
      setReviewNotes("")
      setRejectionReason("")
      
    } catch (error) {
      console.error("Error updating application:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update application",
        variant: "destructive"
      })
    } finally {
      setActionLoading(false)
    }
  }

  const getFilteredApplications = () => {
    switch (activeTab) {
      case "pending":
        return applications.filter(app => app.status === "pending")
      case "review":
        return applications.filter(app => app.status === "under_review")
      case "completed":
        return applications.filter(app => ["approved", "rejected"].includes(app.status))
      default:
        return applications
    }
  }

  const getStats = () => {
    return {
      total: applications.length,
      pending: applications.filter(app => app.status === "pending").length,
      under_review: applications.filter(app => app.status === "under_review").length,
      approved: applications.filter(app => app.status === "approved").length,
      rejected: applications.filter(app => app.status === "rejected").length,
    }
  }

  const stats = getStats()
  const filteredApplications = getFilteredApplications()

  if (loading) {
    return <div className="flex justify-center p-8">Loading applications...</div>
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Application Review</h1>
        <p className="text-muted-foreground">
          Review and approve partner applications
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pending}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Under Review</CardTitle>
            <Eye className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.under_review}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.approved}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.rejected}</div>
          </CardContent>
        </Card>
      </div>

      {/* Applications List */}
      <Card>
        <CardHeader>
          <CardTitle>Applications</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="all">All ({stats.total})</TabsTrigger>
              <TabsTrigger value="pending">Pending ({stats.pending})</TabsTrigger>
              <TabsTrigger value="review">Under Review ({stats.under_review})</TabsTrigger>
              <TabsTrigger value="completed">Completed ({stats.approved + stats.rejected})</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Applicant</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Source</TableHead>
                    <TableHead>Applied</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredApplications.map((app) => {
                    const statusConf = statusConfig[app.status]
                    const priorityConf = priorityConfig[app.priority]
                    const StatusIcon = statusConf.icon
                    
                    return (
                      <TableRow key={app.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{app.full_name}</div>
                            <div className="text-sm text-muted-foreground">{app.email}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{app.company_name}</div>
                            <div className="text-sm text-muted-foreground">
                              {app.company_type === 'other' ? app.company_type_other : app.company_type}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={`${statusConf.color} text-white`}>
                            <StatusIcon className="mr-1 h-3 w-3" />
                            {statusConf.label}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={`${priorityConf.color} text-white`}>
                            {priorityConf.label}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">{app.referral_source || 'Direct'}</span>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">{new Date(app.created_at).toLocaleDateString()}</span>
                        </TableCell>
                        <TableCell>
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => setSelectedApp(app)}
                              >
                                Review
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                              <DialogHeader>
                                <DialogTitle>Application Review: {app.full_name}</DialogTitle>
                                <DialogDescription>
                                  Submitted on {new Date(app.created_at).toLocaleDateString()}
                                </DialogDescription>
                              </DialogHeader>
                              
                              {selectedApp && (
                                <ApplicationReviewDialog 
                                  application={selectedApp}
                                  activities={activities}
                                  reviewNotes={reviewNotes}
                                  setReviewNotes={setReviewNotes}
                                  rejectionReason={rejectionReason}
                                  setRejectionReason={setRejectionReason}
                                  onStatusUpdate={handleStatusUpdate}
                                  actionLoading={actionLoading}
                                />
                              )}
                            </DialogContent>
                          </Dialog>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}

function ApplicationReviewDialog({
  application,
  activities,
  reviewNotes,
  setReviewNotes,
  rejectionReason,
  setRejectionReason,
  onStatusUpdate,
  actionLoading
}: {
  application: Application
  activities: Activity[]
  reviewNotes: string
  setReviewNotes: (notes: string) => void
  rejectionReason: string
  setRejectionReason: (reason: string) => void
  onStatusUpdate: (id: string, status: Application["status"], notes?: string, rejection?: string) => void
  actionLoading: boolean
}) {
  const statusConf = statusConfig[application.status]
  const StatusIcon = statusConf.icon

  return (
    <div className="space-y-6">
      {/* Application Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <StatusIcon className="h-5 w-5" />
          <div>
            <h3 className="font-semibold">{application.full_name}</h3>
            <p className="text-sm text-muted-foreground">{application.company_name}</p>
          </div>
        </div>
        <Badge className={`${statusConf.color} text-white`}>
          {statusConf.label}
        </Badge>
      </div>

      <Tabs defaultValue="details" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="details">Application Details</TabsTrigger>
          <TabsTrigger value="activity">Activity Log</TabsTrigger>
          <TabsTrigger value="review">Review & Actions</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span>{application.email}</span>
                </div>
                {application.phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{application.phone}</span>
                  </div>
                )}
                {application.telegram && (
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    <span>Telegram: {application.telegram}</span>
                  </div>
                )}
                {application.whatsapp && (
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    <span>WhatsApp: {application.whatsapp}</span>
                  </div>
                )}
                {application.x_profile && (
                  <div className="flex items-center gap-2">
                    <ExternalLink className="h-4 w-4 text-muted-foreground" />
                    <span>X: {application.x_profile}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Company Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  Company Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div>
                  <span className="text-sm font-medium">Company:</span>
                  <span className="ml-2">{application.company_name}</span>
                </div>
                <div>
                  <span className="text-sm font-medium">Type:</span>
                  <span className="ml-2">
                    {application.company_type === 'other' ? application.company_type_other : application.company_type}
                  </span>
                </div>
                <div>
                  <span className="text-sm font-medium">Role:</span>
                  <span className="ml-2">
                    {application.role === 'other' ? application.role_other : application.role}
                  </span>
                </div>
                {application.preferred_payment_method && (
                  <div>
                    <span className="text-sm font-medium">Payment Method:</span>
                    <span className="ml-2">{application.preferred_payment_method}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Application Metadata */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="text-base">Application Metadata</CardTitle>
              </CardHeader>
              <CardContent className="grid gap-4 md:grid-cols-3">
                <div>
                  <span className="text-sm font-medium">Source:</span>
                  <span className="ml-2">{application.referral_source || 'Direct'}</span>
                </div>
                <div>
                  <span className="text-sm font-medium">Terms Accepted:</span>
                  <span className="ml-2">{application.terms_accepted ? '✅ Yes' : '❌ No'}</span>
                </div>
                <div>
                  <span className="text-sm font-medium">Privacy Accepted:</span>
                  <span className="ml-2">{application.privacy_accepted ? '✅ Yes' : '❌ No'}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Activity Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {activities.map((activity) => (
                  <div key={activity.id} className="flex gap-3 pb-3 border-b last:border-b-0">
                    <div className="w-2 h-2 rounded-full bg-blue-500 mt-2 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium capitalize">{activity.action.replace('_', ' ')}</span>
                        <span className="text-sm text-muted-foreground">
                          {new Date(activity.created_at).toLocaleString()}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">{activity.notes}</p>
                      {activity.performed_by && (
                        <p className="text-xs text-muted-foreground mt-1">
                          by {activity.performed_by}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="review" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Review Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="review-notes">Review Notes</Label>
                <Textarea
                  id="review-notes"
                  placeholder="Add your review notes..."
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                />
              </div>

              {/* Quick Actions */}
              <div className="flex gap-2 flex-wrap">
                {application.status === 'pending' && (
                  <>
                    <Button
                      onClick={() => onStatusUpdate(application.id, 'under_review', reviewNotes)}
                      disabled={actionLoading}
                      variant="outline"
                    >
                      Start Review
                    </Button>
                    <Button
                      onClick={() => onStatusUpdate(application.id, 'approved', reviewNotes)}
                      disabled={actionLoading}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      Quick Approve
                    </Button>
                  </>
                )}

                {application.status === 'under_review' && (
                  <>
                    <Button
                      onClick={() => onStatusUpdate(application.id, 'approved', reviewNotes)}
                      disabled={actionLoading}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      Approve
                    </Button>
                  </>
                )}

                {['pending', 'under_review'].includes(application.status) && (
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button variant="destructive" disabled={actionLoading}>
                        Reject
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Reject Application</DialogTitle>
                        <DialogDescription>
                          Please provide a reason for rejecting this application.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="rejection-reason">Rejection Reason</Label>
                          <Select value={rejectionReason} onValueChange={setRejectionReason}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a reason" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="incomplete_application">Incomplete Application</SelectItem>
                              <SelectItem value="not_qualified">Not Qualified</SelectItem>
                              <SelectItem value="duplicate_application">Duplicate Application</SelectItem>
                              <SelectItem value="invalid_company">Invalid Company Information</SelectItem>
                              <SelectItem value="terms_not_accepted">Terms Not Accepted</SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="rejection-notes">Additional Notes</Label>
                          <Textarea
                            id="rejection-notes"
                            placeholder="Additional details about the rejection..."
                            value={reviewNotes}
                            onChange={(e) => setReviewNotes(e.target.value)}
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button
                          onClick={() => onStatusUpdate(application.id, 'rejected', reviewNotes, rejectionReason)}
                          disabled={!rejectionReason || actionLoading}
                          variant="destructive"
                        >
                          Reject Application
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}