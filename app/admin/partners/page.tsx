"use client"

import { useEffect, useState } from "react"
import { useUser } from "@clerk/nextjs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Download, Edit, Loader2, User, Phone, Mail } from "lucide-react"
import { toast } from "sonner"

interface Partner {
  id: string
  email: string
  full_name: string
  company_name: string
  company_type: string
  tier: 'trusted' | 'elite' | 'diamond'
  status: 'active' | 'pending' | 'suspended' | 'deleted'
  sales_contact_id: string | null
  created_at: string
  updated_at: string
  referral_slug: string
  billing_address: any
  phone: string
  partners_teams_assignments?: {
    sales_user_id: string
    ops_user_id: string | null
    accounting_user_id: string | null
    partners_users: {
      display_name: string
      email: string
    } | null
  }[]
}

interface PartnersResponse {
  data: Partner[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

async function exportPartners(format: 'csv' | 'json', includeBilling = false) {
  try {
    const params = new URLSearchParams({
      format,
      include_billing: includeBilling.toString()
    })
    
    const response = await fetch(`/api/admin/partners/export?${params}`)
    
    if (!response.ok) {
      throw new Error('Export failed')
    }
    
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `partners_export_${new Date().toISOString().split('T')[0]}.${format}`
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
  } catch (error) {
    console.error('Export failed:', error)
    alert('Export failed. Please try again.')
  }
}

export default function AdminPartners() {
  const { user } = useUser()
  const [partners, setPartners] = useState<Partner[]>([])
  const [loading, setLoading] = useState(true)
  const [userRole, setUserRole] = useState<'sales' | 'ops' | 'accounting' | 'super_admin' | null>(null)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    tier: 'all'
  })
  const [selectedPartner, setSelectedPartner] = useState<Partner | null>(null)
  const [updateLoading, setUpdateLoading] = useState(false)

  const fetchUserRole = async () => {
    try {
      const response = await fetch('/api/user/profile')
      if (response.ok) {
        const data = await response.json()
        setUserRole(data.role)
      }
    } catch (error) {
      console.error('Error fetching user role:', error)
    }
  }

  const fetchPartners = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.search && { search: filters.search }),
        ...(filters.status && filters.status !== 'all' && { status: filters.status }),
        ...(filters.tier && filters.tier !== 'all' && { tier: filters.tier })
      })

      const response = await fetch(`/api/admin/partners?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch partners')
      }

      const result: PartnersResponse = await response.json()
      setPartners(result.data)
      setPagination(result.pagination)
    } catch (error) {
      console.error('Error fetching partners:', error)
      toast.error('Failed to load partners')
    } finally {
      setLoading(false)
    }
  }

  const updatePartnerStatus = async (partnerId: string, status: string, reason?: string) => {
    setUpdateLoading(true)
    try {
      const response = await fetch('/api/admin/partners', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update_status',
          partner_id: partnerId,
          status,
          reason,
          notify_partner: true
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update status')
      }

      toast.success('Partner status updated successfully')
      setSelectedPartner(null)
      fetchPartners()
    } catch (error) {
      console.error('Error updating status:', error)
      toast.error('Failed to update partner status')
    } finally {
      setUpdateLoading(false)
    }
  }

  const updatePartnerTier = async (partnerId: string, tier: string, reason?: string) => {
    setUpdateLoading(true)
    try {
      const response = await fetch('/api/admin/partners', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update_tier',
          partner_id: partnerId,
          new_tier: tier,
          reason,
          notify_partner: true
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update tier')
      }

      toast.success('Partner tier updated successfully')
      setSelectedPartner(null)
      fetchPartners()
    } catch (error) {
      console.error('Error updating tier:', error)
      toast.error('Failed to update partner tier')
    } finally {
      setUpdateLoading(false)
    }
  }

  useEffect(() => {
    fetchPartners()
  }, [pagination.page, filters])

  useEffect(() => {
    fetchUserRole()
  }, [])

  const getBadgeVariant = (status: string) => {
    switch (status) {
      case 'active': return 'default'
      case 'pending': return 'secondary'
      case 'suspended': return 'destructive'
      case 'deleted': return 'outline'
      default: return 'secondary'
    }
  }

  const getTierBadgeVariant = (tier: string) => {
    switch (tier) {
      case 'diamond': return 'default'
      case 'elite': return 'secondary'
      case 'trusted': return 'outline'
      default: return 'outline'
    }
  }

  const getPageTitle = () => {
    if (userRole === 'sales') {
      return "My Partners"
    }
    return "Admin • Partners"
  }

  const canManagePartners = userRole === 'ops' || userRole === 'super_admin'

  return (
    <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>{userRole === 'sales' ? 'My Partners' : 'Partners'}</CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              {pagination.total} {userRole === 'sales' ? 'assigned partners' : 'total partners'}
            </p>
          </div>
          {canManagePartners && (
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => exportPartners('csv')}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Export CSV
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => exportPartners('csv', true)}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Full Export
              </Button>
            </div>
          )}
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4 flex-wrap">
            <Input 
              placeholder="Search partners..." 
              className="max-w-xs" 
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
            />
            <Select value={filters.tier} onValueChange={(value) => setFilters({ ...filters, tier: value })}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="All tiers" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All tiers</SelectItem>
                <SelectItem value="trusted">Trusted</SelectItem>
                <SelectItem value="elite">Elite</SelectItem>
                <SelectItem value="diamond">Diamond</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filters.status} onValueChange={(value) => setFilters({ ...filters, status: value })}>
              <SelectTrigger className="w-36">
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="suspended">Suspended</SelectItem>
                <SelectItem value="deleted">Deleted</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Partner</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Tier</TableHead>
                    <TableHead>Status</TableHead>
                    {userRole !== 'sales' && <TableHead>Sales Rep</TableHead>}
                    <TableHead>Created</TableHead>
                    {canManagePartners && <TableHead>Actions</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {partners.map((partner) => (
                    <TableRow key={partner.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{partner.full_name}</div>
                          <div className="text-sm text-muted-foreground">{partner.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{partner.company_name}</div>
                          <div className="text-sm text-muted-foreground">{partner.company_type}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getTierBadgeVariant(partner.tier)}>
                          {partner.tier.charAt(0).toUpperCase() + partner.tier.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getBadgeVariant(partner.status)}>
                          {partner.status.charAt(0).toUpperCase() + partner.status.slice(1)}
                        </Badge>
                      </TableCell>
                      {userRole !== 'sales' && (
                        <TableCell>
                          {partner.partners_teams_assignments?.[0]?.partners_users ? (
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <div className="font-medium text-sm">
                                  {partner.partners_teams_assignments[0].partners_users.display_name}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {partner.partners_teams_assignments[0].partners_users.email}
                                </div>
                              </div>
                            </div>
                          ) : (
                            <span className="text-sm text-muted-foreground">Unassigned</span>
                          )}
                        </TableCell>
                      )}
                      <TableCell className="text-sm text-muted-foreground">
                        {new Date(partner.created_at).toLocaleDateString()}
                      </TableCell>
                      {canManagePartners && (
                        <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => setSelectedPartner(partner)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Update Partner: {partner.full_name}</DialogTitle>
                            </DialogHeader>
                            {selectedPartner && (
                              <PartnerUpdateDialog
                                partner={selectedPartner}
                                onUpdateStatus={updatePartnerStatus}
                                onUpdateTier={updatePartnerTier}
                                loading={updateLoading}
                              />
                            )}
                          </DialogContent>
                        </Dialog>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {pagination.pages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={pagination.page === 1}
                      onClick={() => setPagination({ ...pagination, page: pagination.page - 1 })}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={pagination.page === pagination.pages}
                      onClick={() => setPagination({ ...pagination, page: pagination.page + 1 })}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
  )
}

function PartnerUpdateDialog({ 
  partner, 
  onUpdateStatus, 
  onUpdateTier, 
  loading 
}: {
  partner: Partner
  onUpdateStatus: (id: string, status: string, reason?: string) => void
  onUpdateTier: (id: string, tier: string, reason?: string) => void
  loading: boolean
}) {
  const [newStatus, setNewStatus] = useState<'pending' | 'active' | 'suspended' | 'deleted'>(partner.status)
  const [newTier, setNewTier] = useState<'trusted' | 'elite' | 'diamond'>(partner.tier)
  const [reason, setReason] = useState('')

  const handleStatusUpdate = () => {
    if (newStatus !== partner.status) {
      onUpdateStatus(partner.id, newStatus, reason)
    }
  }

  const handleTierUpdate = () => {
    if (newTier !== partner.tier) {
      onUpdateTier(partner.id, newTier, reason)
    }
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label>Status</Label>
          <Select value={newStatus} onValueChange={(value) => setNewStatus(value as 'pending' | 'active' | 'suspended' | 'deleted')}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="suspended">Suspended</SelectItem>
              <SelectItem value="deleted">Deleted</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label>Tier</Label>
          <Select value={newTier} onValueChange={(value) => setNewTier(value as 'trusted' | 'elite' | 'diamond')}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="trusted">Trusted (5%)</SelectItem>
              <SelectItem value="elite">Elite (7.5%)</SelectItem>
              <SelectItem value="diamond">Diamond (10%)</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div>
        <Label>Reason (optional)</Label>
        <Textarea 
          value={reason}
          onChange={(e) => setReason(e.target.value)}
          placeholder="Reason for this change..."
          className="mt-1"
        />
      </div>

      <div className="flex gap-2 justify-end">
        {newStatus !== partner.status && (
          <Button 
            onClick={handleStatusUpdate}
            disabled={loading}
          >
            {loading && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
            Update Status
          </Button>
        )}
        {newTier !== partner.tier && (
          <Button 
            onClick={handleTierUpdate}
            disabled={loading}
            variant="secondary"
          >
            {loading && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
            Update Tier
          </Button>
        )}
      </div>
    </div>
  )
}
