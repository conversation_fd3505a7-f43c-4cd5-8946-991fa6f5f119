"use client"

import { useEffect, useState } from "react"
import { PageHeader } from "../components/page-header"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Users, TrendingUp, DollarSign, FileText, Loader2, ArrowUpRight, CheckCircle, XCircle, Clock, Mail } from "lucide-react"
import Link from "next/link"
import { toast } from "sonner"

interface TopEarner {
  id: string
  name: string
  company: string
  tier: string
  totalEarnings: number
}

interface TopPerformer {
  id: string
  name: string
  company: string
  tier: string
  totalDeals: number
  totalValue: number
  totalCommissions: number
}

interface PendingPartner {
  id: string
  full_name: string
  company_name: string
  company_type: string
  created_at: string
  email: string
}

interface AnalyticsData {
  partners?: {
    total: number
    active: number
    pending: number
    newThisPeriod: number
    tierBreakdown: {
      trusted: number
      elite: number
      diamond: number
    }
  }
  deals?: {
    total: number
    confirmed: number
    pending: number
    totalValue: number
    averageValue: number
    statusBreakdown: {
      pending: number
      confirmed: number
      paid: number
      disputed: number
    }
  }
  earnings?: {
    totalCommissions: number
    paidOut: number
    pending: number
    averageCommission: number
    topEarners: TopEarner[]
  }
  leads?: {
    total: number
    approved: number
    pending: number
    conversionRate: number
    averageValue: number
  }
  topPerformers?: TopPerformer[]
  pendingPartners?: PendingPartner[]
}

export default function AdminDashboard() {
  const [analytics, setAnalytics] = useState<AnalyticsData>({})
  const [loading, setLoading] = useState(true)
  const [timeframe, setTimeframe] = useState('30d')
  const [selectedPartner, setSelectedPartner] = useState<PendingPartner | null>(null)
  const [approvalDialog, setApprovalDialog] = useState(false)
  const [approvalLoading, setApprovalLoading] = useState(false)
  const [approvalReason, setApprovalReason] = useState('')

  const fetchAnalytics = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/analytics?metric=overview&timeframe=${timeframe}`)
      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }
      const result = await response.json()
      setAnalytics(result.data)
    } catch (error) {
      console.error('Error fetching analytics:', error)
      toast.error('Failed to load analytics data')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [timeframe])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatPercent = (value: number) => {
    return `${(value * 100).toFixed(1)}%`
  }

  const handleQuickApproval = async (applicationId: string, action: 'approve' | 'reject', reason?: string) => {
    setApprovalLoading(true)
    try {
      const response = await fetch('/api/admin/applications', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          application_id: applicationId,
          status: action === 'approve' ? 'approved' : 'rejected',
          review_notes: reason || (action === 'approve' ? 'Approved via quick approval' : 'Rejected via quick approval'),
          rejection_reason: action === 'reject' ? reason : undefined
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update application status')
      }

      toast.success(`Application ${action === 'approve' ? 'approved' : 'rejected'} successfully`)
      setApprovalDialog(false)
      setSelectedPartner(null)
      setApprovalReason('')
      await fetchAnalytics() // Refresh data
    } catch (error) {
      console.error('Error updating application status:', error)
      toast.error(`Failed to ${action} application`)
    } finally {
      setApprovalLoading(false)
    }
  }

  const openApprovalDialog = (partner: PendingPartner) => {
    setSelectedPartner(partner)
    setApprovalDialog(true)
    setApprovalReason('')
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <PageHeader title="Admin Dashboard" subtitle="Platform overview and key metrics." />
        <Select value={timeframe} onValueChange={setTimeframe}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
            <SelectItem value="1y">Last year</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <>
          {/* Key Metrics Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Total Partners</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.partners?.total || 0}</div>
                <div className="flex items-center text-xs text-muted-foreground mt-1">
                  <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
                  +{analytics.partners?.newThisPeriod || 0} new
                </div>
                <div className="flex gap-1 mt-2">
                  <Badge variant="outline" className="text-xs">
                    {analytics.partners?.active || 0} active
                  </Badge>
                  {analytics.partners?.pending ? (
                    <Badge variant="secondary" className="text-xs">
                      {analytics.partners.pending} pending
                    </Badge>
                  ) : null}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Total Deals</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.deals?.total || 0}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  {analytics.deals?.confirmed || 0} confirmed, {analytics.deals?.pending || 0} pending
                </p>
                <div className="text-sm font-medium mt-2">
                  {formatCurrency(analytics.deals?.totalValue || 0)} total value
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Commissions</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(analytics.earnings?.totalCommissions || 0)}
                </div>
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>Paid: {formatCurrency(analytics.earnings?.paidOut || 0)}</span>
                  <span>Pending: {formatCurrency(analytics.earnings?.pending || 0)}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Leads</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.leads?.total || 0}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  {formatPercent(analytics.leads?.conversionRate || 0)} conversion rate
                </p>
                <div className="text-sm font-medium mt-2">
                  {analytics.leads?.approved || 0} approved, {analytics.leads?.pending || 0} pending
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <Link href="/admin/partners">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Manage Partners
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Review applications, assign tiers, and manage partner accounts.
                  </p>
                  {analytics.partners?.pending ? (
                    <Badge variant="secondary" className="mt-2">
                      {analytics.partners.pending} pending approval
                    </Badge>
                  ) : null}
                </CardContent>
              </Link>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <Link href="/admin/leads">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Lead Queue
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Review and approve new lead submissions from partners.
                  </p>
                  {analytics.leads?.pending ? (
                    <Badge variant="secondary" className="mt-2">
                      {analytics.leads.pending} awaiting review
                    </Badge>
                  ) : null}
                </CardContent>
              </Link>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <Link href="/admin/deals">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Deal Management
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Track deal progress and manage commission calculations.
                  </p>
                  {analytics.deals?.pending ? (
                    <Badge variant="secondary" className="mt-2">
                      {analytics.deals.pending} in progress
                    </Badge>
                  ) : null}
                </CardContent>
              </Link>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <Link href="/admin/withdrawals">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Withdrawals
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Process partner payout requests and track payments.
                  </p>
                  <div className="text-sm font-medium mt-2">
                    {formatCurrency(analytics.earnings?.pending || 0)} pending
                  </div>
                </CardContent>
              </Link>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <Link href="/admin/tools/notifications">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Mail className="h-5 w-5" />
                    Notifications
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Send email notifications and track delivery status.
                  </p>
                  <div className="text-sm font-medium mt-2 text-green-600">
                    Ready to send
                  </div>
                </CardContent>
              </Link>
            </Card>
          </div>

          {/* Pending Partners Approval */}
          {analytics.pendingPartners && analytics.pendingPartners.length > 0 && (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Partners Awaiting Approval</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    {analytics.pendingPartners.length} partner{analytics.pendingPartners.length !== 1 ? 's' : ''} pending review
                  </p>
                </div>
                <Link href="/admin/partners?status=pending">
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </Link>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analytics.pendingPartners.slice(0, 3).map((partner) => (
                    <div key={partner.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center">
                          <Clock className="h-5 w-5 text-orange-600" />
                        </div>
                        <div>
                          <div className="font-medium">{partner.full_name}</div>
                          <div className="text-sm text-muted-foreground">{partner.company_name}</div>
                          <div className="text-xs text-muted-foreground">
                            Applied {new Date(partner.created_at).toLocaleDateString()}
                          </div>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {partner.company_type}
                        </Badge>
                      </div>
                      <div className="flex gap-2">
                        <Button 
                          size="sm" 
                          variant="outline"
                          className="text-green-600 hover:text-green-700 hover:bg-green-50"
                          onClick={() => handleQuickApproval(partner.id, 'approve')}
                          disabled={approvalLoading}
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Approve
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          onClick={() => openApprovalDialog(partner)}
                          disabled={approvalLoading}
                        >
                          <XCircle className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Top Performers */}
          {analytics.topPerformers && analytics.topPerformers.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Partners</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.topPerformers.slice(0, 5).map((partner, index) => (
                    <div key={partner.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-sm font-bold">
                          #{index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{partner.name}</div>
                          <div className="text-sm text-muted-foreground">{partner.company}</div>
                        </div>
                        <Badge variant="outline">
                          {partner.tier.charAt(0).toUpperCase() + partner.tier.slice(1)}
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{formatCurrency(partner.totalCommissions)}</div>
                        <div className="text-sm text-muted-foreground">
                          {partner.totalDeals} deals
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
      
      {/* Rejection Dialog */}
      <Dialog open={approvalDialog} onOpenChange={setApprovalDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Partner Application</DialogTitle>
            <DialogDescription>
              You&apos;re about to reject {selectedPartner?.full_name}&apos;s application from {selectedPartner?.company_name}.
              Please provide a reason for rejection.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="reason">Reason for rejection</Label>
              <Textarea
                id="reason"
                value={approvalReason}
                onChange={(e) => setApprovalReason(e.target.value)}
                placeholder="Please explain why this application is being rejected..."
                className="mt-1"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button 
                variant="outline" 
                onClick={() => setApprovalDialog(false)}
                disabled={approvalLoading}
              >
                Cancel
              </Button>
              <Button 
                variant="destructive"
                onClick={() => selectedPartner?.id && handleQuickApproval(selectedPartner.id, 'reject', approvalReason)}
                disabled={approvalLoading || !approvalReason.trim()}
              >
                {approvalLoading && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                Reject Application
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
