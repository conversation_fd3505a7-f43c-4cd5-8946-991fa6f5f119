"use client"

import { useEffect, useState } from "react"
import { useUser } from "@clerk/nextjs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Download, 
  Edit, 
  Loader2, 
  User, 
  Phone, 
  Mail, 
  MessageCircle,
  Link as LinkIcon,
  Plus,
  Activity,
  TrendingUp,
  Eye,
  Copy,
  CheckCircle
} from "lucide-react"
import { toast } from "sonner"
import { SalesPerson, SalesPersonWithReferralStats, SalesStats } from "@/lib/types/sales"

interface SalesTeamResponse {
  success: boolean
  data?: SalesPersonWithReferralStats[]
  stats?: SalesStats
  error?: string
}

export default function SalesManagementPage() {
  const { user } = useUser()
  const [salesTeam, setSalesTeam] = useState<SalesPersonWithReferralStats[]>([])
  const [stats, setStats] = useState<SalesStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<"all" | "active" | "inactive">("all")
  const [editingMember, setEditingMember] = useState<SalesPersonWithReferralStats | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  // Form state for editing
  const [editForm, setEditForm] = useState({
    name: "",
    email: "",
    telegramHandle: "",
    telegramLink: "",
    isActive: true
  })

  useEffect(() => {
    fetchSalesTeam()
  }, [])

  const fetchSalesTeam = async () => {
    setLoading(true)
    try {
      // This would be implemented in the API
      const response = await fetch('/api/admin/sales')
      if (response.ok) {
        const result: SalesTeamResponse = await response.json()
        if (result.success && result.data) {
          setSalesTeam(result.data)
          if (result.stats) {
            setStats(result.stats)
          }
        } else {
          toast("Error", { description: result.error || "Failed to load sales team" })
        }
      } else {
        toast("Error", { description: "Failed to fetch sales team data" })
      }
    } catch (error) {
      console.error('Error fetching sales team:', error)
      toast("Error", { description: "Network error while fetching sales team" })
    } finally {
      setLoading(false)
    }
  }

  const handleEditMember = (member: SalesPersonWithReferralStats) => {
    setEditingMember(member)
    setEditForm({
      name: member.name,
      email: member.email,
      telegramHandle: member.telegramHandle || "",
      telegramLink: member.telegramLink || "",
      isActive: member.isActive
    })
    setIsEditDialogOpen(true)
  }

  const handleSaveChanges = async () => {
    if (!editingMember) return

    try {
      const response = await fetch(`/api/admin/sales/${editingMember.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm)
      })

      if (response.ok) {
        toast("Success", { description: "Sales team member updated successfully" })
        setIsEditDialogOpen(false)
        setEditingMember(null)
        fetchSalesTeam() // Refresh the data
      } else {
        toast("Error", { description: "Failed to update sales team member" })
      }
    } catch (error) {
      console.error('Error updating sales member:', error)
      toast("Error", { description: "Network error while updating member" })
    }
  }

  const copyReferralLink = async (slug: string) => {
    const link = `${window.location.origin}/referral/sales/${slug}`
    try {
      await navigator.clipboard.writeText(link)
      toast("Copied", { description: "Referral link copied to clipboard" })
    } catch (error) {
      console.error('Failed to copy link:', error)
      toast("Error", { description: "Failed to copy referral link" })
    }
  }

  const toggleMemberStatus = async (memberId: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/admin/sales/${memberId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive: !currentStatus })
      })

      if (response.ok) {
        toast("Success", { description: `Sales member ${!currentStatus ? 'activated' : 'deactivated'}` })
        fetchSalesTeam()
      } else {
        toast("Error", { description: "Failed to update member status" })
      }
    } catch (error) {
      console.error('Error toggling member status:', error)
      toast("Error", { description: "Network error while updating status" })
    }
  }

  const exportSalesData = () => {
    // Implementation for exporting sales data as CSV
    const csvData = salesTeam.map(member => ({
      Name: member.name,
      Email: member.email,
      'Telegram Handle': member.telegramHandle || 'N/A',
      'Referral Slug': member.referralSlug,
      'Total Clicks': member.totalClicks,
      'Total Conversions': member.totalConversions,
      'Conversion Rate': `${((member.totalConversions / Math.max(member.totalClicks, 1)) * 100).toFixed(1)}%`,
      Status: member.isActive ? 'Active' : 'Inactive',
      'Created At': new Date(member.createdAt).toLocaleDateString()
    }))

    const csv = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).join(','))
    ].join('\n')

    const blob = new Blob([csv], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `sales-team-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  const filteredSalesTeam = salesTeam.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (member.telegramHandle?.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesStatus = statusFilter === "all" || 
                         (statusFilter === "active" && member.isActive) ||
                         (statusFilter === "inactive" && !member.isActive)

    return matchesSearch && matchesStatus
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading sales team...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">Sales Team Management</h1>
          <p className="text-muted-foreground">Manage sales team members and their referral performance</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={exportSalesData} variant="outline" className="gap-2">
            <Download className="h-4 w-4" />
            Export
          </Button>
          <Button className="gap-2">
            <Plus className="h-4 w-4" />
            Add Sales Member
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">Total Clicks</span>
              </div>
              <p className="text-2xl font-bold mt-1">{stats.totalClicks}</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">Conversions</span>
              </div>
              <p className="text-2xl font-bold mt-1">{stats.totalConversions}</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium">Conversion Rate</span>
              </div>
              <p className="text-2xl font-bold mt-1">{(stats.conversionRate * 100).toFixed(1)}%</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-medium">Active Members</span>
              </div>
              <p className="text-2xl font-bold mt-1">{salesTeam.filter(m => m.isActive).length}</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search by name, email, or Telegram handle..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="w-full sm:w-48">
              <Select value={statusFilter} onValueChange={(value: "all" | "active" | "inactive") => setStatusFilter(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Members</SelectItem>
                  <SelectItem value="active">Active Only</SelectItem>
                  <SelectItem value="inactive">Inactive Only</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sales Team Table */}
      <Card>
        <CardHeader>
          <CardTitle>Sales Team Members ({filteredSalesTeam.length})</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Referral Link</TableHead>
                <TableHead>Performance</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSalesTeam.map((member) => (
                <TableRow key={member.id}>
                  <TableCell>
                    <div className="font-medium">{member.name}</div>
                    <div className="text-sm text-muted-foreground">{member.email}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center gap-1 text-sm">
                        <Mail className="h-3 w-3" />
                        {member.email.split('@')[0]}
                      </div>
                      {member.telegramHandle && (
                        <div className="flex items-center gap-1 text-sm text-blue-600">
                          <MessageCircle className="h-3 w-3" />
                          {member.telegramHandle}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <code className="text-xs bg-muted px-2 py-1 rounded">
                        /referral/sales/{member.referralSlug}
                      </code>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => copyReferralLink(member.referralSlug)}
                        className="h-6 w-6 p-0"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">
                        {member.totalClicks} clicks
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {member.totalConversions} conversions
                      </div>
                      {member.totalClicks > 0 && (
                        <div className="text-xs text-green-600">
                          {((member.totalConversions / member.totalClicks) * 100).toFixed(1)}% rate
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={member.isActive ? "success" : "secondary"}>
                      {member.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleEditMember(member)}
                        className="h-6 w-6 p-0"
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => toggleMemberStatus(member.id, member.isActive)}
                        className="h-6 w-6 p-0"
                      >
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Sales Team Member</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Name</Label>
              <Input
                id="edit-name"
                value={editForm.name}
                onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-email">Email</Label>
              <Input
                id="edit-email"
                type="email"
                value={editForm.email}
                onChange={(e) => setEditForm(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-telegram">Telegram Handle</Label>
              <Input
                id="edit-telegram"
                placeholder="@username"
                value={editForm.telegramHandle}
                onChange={(e) => setEditForm(prev => ({ ...prev, telegramHandle: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-telegram-link">Telegram Link</Label>
              <Input
                id="edit-telegram-link"
                placeholder="https://t.me/username"
                value={editForm.telegramLink}
                onChange={(e) => setEditForm(prev => ({ ...prev, telegramLink: e.target.value }))}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="edit-active"
                checked={editForm.isActive}
                onCheckedChange={(checked) => setEditForm(prev => ({ ...prev, isActive: Boolean(checked) }))}
              />
              <Label htmlFor="edit-active">Active</Label>
            </div>
            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveChanges}>
                Save Changes
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}