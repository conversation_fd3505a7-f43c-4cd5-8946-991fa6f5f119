import type React from "react"
import { redirect } from "next/navigation"
import { getCurrentUserRole } from "@/lib/rbac"
import { AppShell } from "../components/app-shell"
import { Badge } from "@/components/ui/badge"
import { Shield } from "lucide-react"

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Check if user has admin access
  const role = await getCurrentUserRole()
  
  if (!role || !['ops', 'accounting', 'super_admin'].includes(role)) {
    redirect('/access-pending')
  }

  return (
    <AppShell title="Admin">
      <div className="mb-4">
        <div className="flex items-center gap-2">
          <Shield className="h-4 w-4" />
          <Badge variant="outline">
            {role === 'super_admin' ? 'Super Admin' : role.toUpperCase()}
          </Badge>
        </div>
      </div>
      {children}
    </AppShell>
  )
}
