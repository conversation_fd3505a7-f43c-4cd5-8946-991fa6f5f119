import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

const rows = [
  { id: "w_1002", partner: "<PERSON>", amount: 800, status: "Pending" },
  { id: "w_1003", partner: "Maya", amount: 2500, status: "Processing" },
]

export default function AdminWithdrawals() {
  return (
    <Card>
        <CardHeader>
          <CardTitle>Queue</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Partner</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Amount</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {rows.map((r) => (
                <TableRow key={r.id}>
                  <TableCell>{r.id}</TableCell>
                  <TableCell>{r.partner}</TableCell>
                  <TableCell>
                    <Badge variant={r.status === "Pending" ? "outline" : "secondary"}>{r.status}</Badge>
                  </TableCell>
                  <TableCell className="text-right">${r.amount.toFixed(2)}</TableCell>
                  <TableCell>
                    <Button size="sm">Mark Paid</Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
  )
}
