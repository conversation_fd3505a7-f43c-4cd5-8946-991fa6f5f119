import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"

const deals = [
  { id: "D100", project: "Phoenix", value: 50000, commission: 5, status: "Closed" },
  { id: "D101", project: "Nova", value: 25000, commission: 6, status: "In Progress" },
]

export default function AdminDeals() {
  return (
    <Card>
        <CardHeader>
          <CardTitle>Deals Editor</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Project</TableHead>
                <TableHead>Value</TableHead>
                <TableHead>Commission %</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {deals.map((d) => (
                <TableRow key={d.id}>
                  <TableCell>
                    <Input defaultValue={d.project} />
                  </TableCell>
                  <TableCell>
                    <Input defaultValue={d.value} />
                  </TableCell>
                  <TableCell>
                    <Input defaultValue={d.commission} />
                  </TableCell>
                  <TableCell>
                    <Input defaultValue={d.status} />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
  )
}
