"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"

export default function BulkReassign() {
  const [from, setFrom] = React.useState("")
  const [to, setTo] = React.useState("")
  const [count, setCount] = React.useState(24)
  const [running, setRunning] = React.useState(false)
  const [progress, setProgress] = React.useState(0)

  const start = async () => {
    setRunning(true)
    for (let i = 1; i <= count; i++) {
      setProgress(Math.round((i / count) * 100))
      await new Promise((r) => setTimeout(r, 50))
    }
    setRunning(false)
  }

  return (
    <Card className="max-w-xl">
        <CardHeader>
          <CardTitle>Reassign Partners</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-1.5">
            <Label htmlFor="from">From (Sales A)</Label>
            <Input id="from" placeholder="<EMAIL>" value={from} onChange={(e) => setFrom(e.target.value)} />
          </div>
          <div className="space-y-1.5">
            <Label htmlFor="to">To (Sales B)</Label>
            <Input id="to" placeholder="<EMAIL>" value={to} onChange={(e) => setTo(e.target.value)} />
          </div>
          <div className="text-sm text-muted-foreground">Partners to move: {count}</div>
          <Progress value={progress} />
        </CardContent>
        <CardFooter className="gap-2">
          <Button onClick={start} disabled={running || !from || !to}>
            Execute
          </Button>
          <Button variant="outline" className="bg-transparent" disabled={running}>
            Cancel
          </Button>
        </CardFooter>
      </Card>
  )
}
