"use client"

import * as React from "react"
import { <PERSON><PERSON>eader } from "@/app/components/page-header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Search, UserCheck, Users2, ArrowRight, CheckCircle2 } from "lucide-react"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"

type Partner = {
  id: string
  full_name: string
  email: string
  company_name: string
  status: string
  tier: string
  created_at: string
  sales_user?: {
    id: string
    display_name: string
    email: string
  }
}

type SalesUser = {
  id: string
  display_name: string
  email: string
  role: string
}

export default function SalesAssignmentPage() {
  const { toast } = useToast()
  const [partners, setPartners] = React.useState<Partner[]>([])
  const [salesUsers, setSalesUsers] = React.useState<SalesUser[]>([])
  const [loading, setLoading] = React.useState(true)
  const [saving, setSaving] = React.useState<string | null>(null)
  const [searchTerm, setSearchTerm] = React.useState("")
  const [statusFilter, setStatusFilter] = React.useState<string>("all")
  const [assignmentFilter, setAssignmentFilter] = React.useState<string>("all")

  React.useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    setLoading(true)
    try {
      // Fetch partners and sales users in parallel
      const [partnersRes, salesRes] = await Promise.all([
        fetch('/api/admin/partners'),
        fetch('/api/admin/users?role=sales')
      ])

      if (partnersRes.ok) {
        const partnersData = await partnersRes.json()
        setPartners(partnersData.data || [])
      }

      if (salesRes.ok) {
        const salesData = await salesRes.json()
        setSalesUsers(salesData.data || [])
      }
    } catch (error) {
      console.error('Failed to fetch data:', error)
      toast({
        title: "Error",
        description: "Failed to load partners and sales users",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const assignSalesUser = async (partnerId: string, salesUserId: string | null) => {
    setSaving(partnerId)
    try {
      const response = await fetch('/api/admin/partners/assign-sales', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ partnerId, salesUserId })
      })

      if (response.ok) {
        // Update local state
        setPartners(prev => prev.map(partner => 
          partner.id === partnerId 
            ? { 
                ...partner, 
                sales_user: salesUserId 
                  ? salesUsers.find(s => s.id === salesUserId) 
                  : undefined 
              }
            : partner
        ))
        
        const salesUserName = salesUserId 
          ? salesUsers.find(s => s.id === salesUserId)?.display_name
          : 'None'
        
        toast({
          title: "Assignment updated",
          description: `Sales rep ${salesUserName} assigned to partner`
        })
      } else {
        throw new Error('Failed to update assignment')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update sales assignment",
        variant: "destructive"
      })
    } finally {
      setSaving(null)
    }
  }

  const bulkReassign = async (fromSalesId: string, toSalesId: string) => {
    setSaving('bulk')
    try {
      const response = await fetch('/api/admin/partners/bulk-reassign', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ fromSalesUserId: fromSalesId, toSalesUserId: toSalesId })
      })

      if (response.ok) {
        const result = await response.json()
        await fetchData() // Refresh data
        
        toast({
          title: "Bulk reassignment completed",
          description: `${result.updated} partners reassigned successfully`
        })
      } else {
        throw new Error('Failed to bulk reassign')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to complete bulk reassignment",
        variant: "destructive"
      })
    } finally {
      setSaving(null)
    }
  }

  // Filter partners based on search and filters
  const filteredPartners = partners.filter(partner => {
    const matchesSearch = partner.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         partner.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         partner.company_name.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === "all" || partner.status === statusFilter
    
    const matchesAssignment = assignmentFilter === "all" ||
                             (assignmentFilter === "assigned" && partner.sales_user) ||
                             (assignmentFilter === "unassigned" && !partner.sales_user)
    
    return matchesSearch && matchesStatus && matchesAssignment
  })

  const unassignedCount = partners.filter(p => !p.sales_user).length
  const assignedCount = partners.filter(p => p.sales_user).length

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
        <PageHeader 
          title="Sales Team Assignment" 
          subtitle="Assign and manage sales representatives for partners"
        />

        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Partners</CardTitle>
              <Users2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{partners.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Assigned</CardTitle>
              <CheckCircle2 className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{assignedCount}</div>
              <p className="text-xs text-muted-foreground">
                {partners.length > 0 ? Math.round((assignedCount / partners.length) * 100) : 0}% of partners
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Unassigned</CardTitle>
              <UserCheck className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{unassignedCount}</div>
              <p className="text-xs text-muted-foreground">
                Need sales assignment
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Bulk Assignment Tool */}
        <Card>
          <CardHeader>
            <CardTitle>Bulk Reassignment</CardTitle>
            <CardDescription>
              Transfer all partners from one sales representative to another
            </CardDescription>
          </CardHeader>
          <CardContent>
            <BulkReassignTool 
              salesUsers={salesUsers}
              onReassign={bulkReassign}
              loading={saving === 'bulk'}
            />
          </CardContent>
        </Card>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle>Partner Assignments</CardTitle>
            <CardDescription>
              Individual partner sales representative assignments
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Label htmlFor="search">Search Partners</Label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Search by name, email, or company..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label>Status Filter</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>Assignment</Label>
                <Select value={assignmentFilter} onValueChange={setAssignmentFilter}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Partners</SelectItem>
                    <SelectItem value="assigned">Assigned</SelectItem>
                    <SelectItem value="unassigned">Unassigned</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Partners Table */}
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Partner</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Current Sales Rep</TableHead>
                    <TableHead>Assign Sales Rep</TableHead>
                    <TableHead className="w-[100px]">Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPartners.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                        No partners found matching your filters
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredPartners.map((partner) => (
                      <TableRow key={partner.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{partner.full_name}</div>
                            <div className="text-sm text-muted-foreground">{partner.email}</div>
                          </div>
                        </TableCell>
                        <TableCell>{partner.company_name}</TableCell>
                        <TableCell>
                          <Badge variant={
                            partner.status === 'active' ? 'default' :
                            partner.status === 'pending' ? 'secondary' : 'destructive'
                          }>
                            {partner.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {partner.sales_user ? (
                            <div>
                              <div className="font-medium">{partner.sales_user.display_name}</div>
                              <div className="text-sm text-muted-foreground">{partner.sales_user.email}</div>
                            </div>
                          ) : (
                            <Badge variant="outline">Unassigned</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <PartnerAssignment
                            partner={partner}
                            salesUsers={salesUsers}
                            onAssign={assignSalesUser}
                            loading={saving === partner.id}
                          />
                        </TableCell>
                        <TableCell>
                          {saving === partner.id ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <CheckCircle2 className="h-4 w-4 text-green-600" />
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
  )
}

function PartnerAssignment({ 
  partner, 
  salesUsers, 
  onAssign, 
  loading 
}: { 
  partner: Partner
  salesUsers: SalesUser[]
  onAssign: (partnerId: string, salesUserId: string | null) => void
  loading: boolean
}) {
  const [selectedSalesId, setSelectedSalesId] = React.useState<string>(
    partner.sales_user?.id || "unassigned"
  )

  const handleAssign = () => {
    const salesUserId = selectedSalesId === "unassigned" ? null : selectedSalesId
    onAssign(partner.id, salesUserId)
  }

  const hasChanged = (partner.sales_user?.id || "unassigned") !== selectedSalesId

  return (
    <div className="flex items-center gap-2">
      <Select
        value={selectedSalesId}
        onValueChange={setSelectedSalesId}
        disabled={loading}
      >
        <SelectTrigger className="w-[200px]">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="unassigned">Unassigned</SelectItem>
          {salesUsers.map((user) => (
            <SelectItem key={user.id} value={user.id}>
              {user.display_name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {hasChanged && (
        <Button
          size="sm"
          onClick={handleAssign}
          disabled={loading}
        >
          {loading ? <Loader2 className="h-3 w-3 animate-spin" /> : "Save"}
        </Button>
      )}
    </div>
  )
}

function BulkReassignTool({ 
  salesUsers, 
  onReassign, 
  loading 
}: { 
  salesUsers: SalesUser[]
  onReassign: (fromSalesId: string, toSalesId: string) => void
  loading: boolean
}) {
  const [fromSalesId, setFromSalesId] = React.useState<string>("")
  const [toSalesId, setToSalesId] = React.useState<string>("")

  const canReassign = fromSalesId && toSalesId && fromSalesId !== toSalesId

  const handleReassign = () => {
    if (canReassign) {
      onReassign(fromSalesId, toSalesId)
    }
  }

  return (
    <div className="flex items-center gap-4">
      <div className="space-y-2">
        <Label>From Sales Rep</Label>
        <Select value={fromSalesId} onValueChange={setFromSalesId}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select sales rep" />
          </SelectTrigger>
          <SelectContent>
            {salesUsers.map((user) => (
              <SelectItem key={user.id} value={user.id}>
                {user.display_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <ArrowRight className="h-4 w-4 text-muted-foreground mt-6" />
      
      <div className="space-y-2">
        <Label>To Sales Rep</Label>
        <Select value={toSalesId} onValueChange={setToSalesId}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select sales rep" />
          </SelectTrigger>
          <SelectContent>
            {salesUsers.map((user) => (
              <SelectItem key={user.id} value={user.id}>
                {user.display_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <Button
        onClick={handleReassign}
        disabled={!canReassign || loading}
        className="mt-6"
      >
        {loading ? (
          <>
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            Reassigning...
          </>
        ) : (
          "Bulk Reassign"
        )}
      </Button>
    </div>
  )
}