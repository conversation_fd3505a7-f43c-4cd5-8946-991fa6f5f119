"use client"

import * as React from "react"
import { <PERSON>Header } from "@/app/components/page-header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from "recharts"
import { TrendingUp, TrendingDown, Users, DollarSign, FileText, Target, Loader2, Download, Calendar } from "lucide-react"

type AnalyticsData = {
  partners?: {
    total: number
    active: number
    pending: number
    newThisPeriod: number
    tierBreakdown: {
      trusted: number
      elite: number
      diamond: number
    }
  }
  deals?: {
    total: number
    confirmed: number
    pending: number
    totalValue: number
    averageValue: number
    statusBreakdown: {
      pending: number
      confirmed: number
      paid: number
      disputed: number
    }
  }
  earnings?: {
    totalCommissions: number
    paidOut: number
    pending: number
    averageCommission: number
    topEarners: any[]
  }
  leads?: {
    total: number
    approved: number
    pending: number
    conversionRate: number
    averageValue: number
  }
  topPerformers?: any[]
  timeSeries?: any[]
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

export default function AdminAnalytics() {
  const { toast } = useToast()
  const [loading, setLoading] = React.useState(true)
  const [timeframe, setTimeframe] = React.useState('30d')
  const [selectedMetric, setSelectedMetric] = React.useState('overview')
  const [analytics, setAnalytics] = React.useState<AnalyticsData>({})

  const fetchAnalytics = React.useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/analytics?metric=${selectedMetric}&timeframe=${timeframe}`)
      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }
      const result = await response.json()
      setAnalytics(result.data)
    } catch (error) {
      console.error('Error fetching analytics:', error)
      toast({
        title: "Error",
        description: "Failed to load analytics data",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }, [selectedMetric, timeframe, toast])

  React.useEffect(() => {
    fetchAnalytics()
  }, [fetchAnalytics])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatPercent = (value: number) => {
    return `${(value * 100).toFixed(1)}%`
  }

  const getChangeIndicator = (current: number, previous: number) => {
    if (previous === 0) return null
    const change = ((current - previous) / previous) * 100
    const isPositive = change > 0
    return (
      <span className={`flex items-center text-xs ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
        {Math.abs(change).toFixed(1)}%
      </span>
    )
  }

  const tierData = analytics.partners ? [
    { name: 'Trusted', value: analytics.partners.tierBreakdown.trusted, color: '#8884D8' },
    { name: 'Elite', value: analytics.partners.tierBreakdown.elite, color: '#00C49F' },
    { name: 'Diamond', value: analytics.partners.tierBreakdown.diamond, color: '#FFBB28' }
  ] : []

  const dealStatusData = analytics.deals ? [
    { name: 'Pending', value: analytics.deals.statusBreakdown.pending, color: '#FFBB28' },
    { name: 'Confirmed', value: analytics.deals.statusBreakdown.confirmed, color: '#00C49F' },
    { name: 'Paid', value: analytics.deals.statusBreakdown.paid, color: '#0088FE' },
    { name: 'Disputed', value: analytics.deals.statusBreakdown.disputed, color: '#FF8042' }
  ] : []

  if (loading) {
    return (
      <div className="flex flex-col gap-4">
        <PageHeader title="Analytics Dashboard" subtitle="Business intelligence and performance metrics" />
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading analytics...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <PageHeader title="Analytics Dashboard" subtitle="Business intelligence and performance metrics" />
        <div className="flex gap-2">
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" className="gap-2">
            <Download className="h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Tabs value={selectedMetric} onValueChange={setSelectedMetric}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="partners">Partners</TabsTrigger>
          <TabsTrigger value="deals">Deals</TabsTrigger>
          <TabsTrigger value="earnings">Earnings</TabsTrigger>
          <TabsTrigger value="leads">Leads</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Total Partners</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.partners?.total || 0}</div>
                <div className="flex items-center justify-between mt-1">
                  <span className="text-xs text-muted-foreground">
                    {analytics.partners?.active || 0} active
                  </span>
                  {analytics.partners?.newThisPeriod ? (
                    <Badge variant="secondary" className="text-xs">
                      +{analytics.partners.newThisPeriod} new
                    </Badge>
                  ) : null}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(analytics.deals?.totalValue || 0)}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {analytics.deals?.total || 0} deals closed
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Commissions</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(analytics.earnings?.totalCommissions || 0)}
                </div>
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>Paid: {formatCurrency(analytics.earnings?.paidOut || 0)}</span>
                  <span>Pending: {formatCurrency(analytics.earnings?.pending || 0)}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatPercent(analytics.leads?.conversionRate || 0)}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {analytics.leads?.approved || 0} of {analytics.leads?.total || 0} leads
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Charts */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Partner Distribution by Tier</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={tierData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {tierData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Deal Status Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={dealStatusData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="value" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Top Performers */}
          <Card>
            <CardHeader>
              <CardTitle>Top Performing Partners</CardTitle>
              <CardDescription>Ranked by total commission earnings</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Rank</TableHead>
                    <TableHead>Partner</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Tier</TableHead>
                    <TableHead>Deals</TableHead>
                    <TableHead>Revenue</TableHead>
                    <TableHead>Commissions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {analytics.topPerformers?.slice(0, 10).map((partner, index) => (
                    <TableRow key={partner.id}>
                      <TableCell className="font-medium">#{index + 1}</TableCell>
                      <TableCell>{partner.name}</TableCell>
                      <TableCell>{partner.company}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {partner.tier?.charAt(0).toUpperCase() + partner.tier?.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>{partner.totalDeals}</TableCell>
                      <TableCell>{formatCurrency(partner.totalValue)}</TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(partner.totalCommissions)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="partners" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Partner Growth</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{analytics.partners?.total || 0}</div>
                <p className="text-sm text-muted-foreground">Total Partners</p>
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Active</span>
                    <span className="font-medium">{analytics.partners?.active || 0}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Pending</span>
                    <span className="font-medium text-yellow-600">{analytics.partners?.pending || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Tier Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-sm">Trusted</span>
                    </div>
                    <span className="font-medium">{analytics.partners?.tierBreakdown.trusted || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-sm">Elite</span>
                    </div>
                    <span className="font-medium">{analytics.partners?.tierBreakdown.elite || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <span className="text-sm">Diamond</span>
                    </div>
                    <span className="font-medium">{analytics.partners?.tierBreakdown.diamond || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">New This Period</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-600">
                  +{analytics.partners?.newThisPeriod || 0}
                </div>
                <p className="text-sm text-muted-foreground">New partners joined</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="deals" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Total Deals</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.deals?.total || 0}</div>
                <p className="text-xs text-muted-foreground">All time</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Total Value</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(analytics.deals?.totalValue || 0)}</div>
                <p className="text-xs text-muted-foreground">Combined revenue</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Average Deal</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(analytics.deals?.averageValue || 0)}</div>
                <p className="text-xs text-muted-foreground">Per deal</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Confirmed Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {analytics.deals?.total ? 
                    formatPercent((analytics.deals.confirmed || 0) / analytics.deals.total) : 
                    '0%'
                  }
                </div>
                <p className="text-xs text-muted-foreground">Success rate</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="earnings" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Total Commissions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{formatCurrency(analytics.earnings?.totalCommissions || 0)}</div>
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Paid Out</span>
                    <span className="font-medium text-green-600">
                      {formatCurrency(analytics.earnings?.paidOut || 0)}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Pending</span>
                    <span className="font-medium text-yellow-600">
                      {formatCurrency(analytics.earnings?.pending || 0)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Average Commission</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {formatCurrency(analytics.earnings?.averageCommission || 0)}
                </div>
                <p className="text-sm text-muted-foreground">Per deal</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Top Earner</CardTitle>
              </CardHeader>
              <CardContent>
                {analytics.earnings?.topEarners?.[0] ? (
                  <div>
                    <div className="font-medium">{analytics.earnings.topEarners[0].name}</div>
                    <div className="text-2xl font-bold text-green-600">
                      {formatCurrency(analytics.earnings.topEarners[0].totalCommissions)}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {analytics.earnings.topEarners[0].totalDeals} deals
                    </p>
                  </div>
                ) : (
                  <p className="text-muted-foreground">No data available</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="leads" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Total Leads</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.leads?.total || 0}</div>
                <p className="text-xs text-muted-foreground">All submissions</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Approved</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{analytics.leads?.approved || 0}</div>
                <p className="text-xs text-muted-foreground">Converted to deals</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Pending Review</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{analytics.leads?.pending || 0}</div>
                <p className="text-xs text-muted-foreground">Awaiting approval</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Conversion Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatPercent(analytics.leads?.conversionRate || 0)}</div>
                <p className="text-xs text-muted-foreground">Lead to deal</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
