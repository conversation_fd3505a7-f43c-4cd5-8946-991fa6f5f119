import Link from "next/link"
import { AppShell } from "@/app/components/app-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"

export default function NotFound() {
  return (
    <AppShell title="Not found">
      <div className="mx-auto max-w-xl text-center space-y-3">
        <h1 className="text-3xl font-bold">We couldn’t find that page</h1>
        <p className="text-muted-foreground">Check the link or go back home. Need help? Contact support.</p>
        <div className="flex justify-center gap-2">
          <Button asChild>
            <Link href="/">Go Home</Link>
          </Button>
          <Button asChild variant="outline" className="bg-transparent">
            <Link href="/support">Contact</Link>
          </Button>
        </div>
      </div>
    </AppShell>
  )
}
