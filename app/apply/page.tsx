import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowRight, DollarSign, Users, TrendingUp, Shield, Clock, Award, CheckCircle } from 'lucide-react'

export default function ApplyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <Badge variant="outline" className="mb-4 text-lg py-2 px-4">
            🚀 IBC Partnership Program
          </Badge>
          <h1 className="text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Join the Future of Blockchain
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Partner with IBC Group to unlock exclusive opportunities in the blockchain ecosystem. 
            Earn competitive commissions, access premium resources, and grow your business with industry leaders.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="text-lg py-6 px-8">
              <Link href="/sign-up">
                Apply Now <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="text-lg py-6 px-8">
              <Link href="/sign-in">
                Partner Login
              </Link>
            </Button>
          </div>
        </div>

        {/* Commission Tiers */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-8">Tiered Commission Structure</h2>
          <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <Card className="text-center">
              <CardHeader>
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-8 w-8 text-green-600" />
                </div>
                <CardTitle className="text-2xl">🔰 Trusted</CardTitle>
                <CardDescription>Starting tier for new partners</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-4xl font-bold text-green-600 mb-4">5%</div>
                <p className="text-sm text-muted-foreground">Commission on all successful deals</p>
                <div className="mt-4 space-y-2 text-sm">
                  <div className="flex items-center justify-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Basic support</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Marketing materials</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="text-center border-2 border-blue-500 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-blue-500 hover:bg-blue-600">Most Popular</Badge>
              </div>
              <CardHeader>
                <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Award className="h-8 w-8 text-blue-600" />
                </div>
                <CardTitle className="text-2xl">⭐ Elite</CardTitle>
                <CardDescription>$100k+ monthly volume</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-4xl font-bold text-blue-600 mb-4">7.5%</div>
                <p className="text-sm text-muted-foreground">Commission on all successful deals</p>
                <div className="mt-4 space-y-2 text-sm">
                  <div className="flex items-center justify-center gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-500" />
                    <span>Priority support</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-500" />
                    <span>Advanced analytics</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-500" />
                    <span>Technical integration</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="h-8 w-8 text-purple-600" />
                </div>
                <CardTitle className="text-2xl">💎 Diamond</CardTitle>
                <CardDescription>$500k+ monthly volume</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-4xl font-bold text-purple-600 mb-4">10%</div>
                <p className="text-sm text-muted-foreground">Commission on all successful deals</p>
                <div className="mt-4 space-y-2 text-sm">
                  <div className="flex items-center justify-center gap-2">
                    <CheckCircle className="h-4 w-4 text-purple-500" />
                    <span>24/7 dedicated support</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <CheckCircle className="h-4 w-4 text-purple-500" />
                    <span>Custom features</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <CheckCircle className="h-4 w-4 text-purple-500" />
                    <span>Private Slack channel</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Benefits */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">Why Partner With IBC?</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center p-6">
              <DollarSign className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="font-semibold mb-2">Competitive Commissions</h3>
              <p className="text-sm text-muted-foreground">Up to 10% commission on successful deals with transparent tracking</p>
            </Card>
            <Card className="text-center p-6">
              <Users className="h-12 w-12 text-blue-500 mx-auto mb-4" />
              <h3 className="font-semibold mb-2">Dedicated Support</h3>
              <p className="text-sm text-muted-foreground">Personal relationship managers and priority technical support</p>
            </Card>
            <Card className="text-center p-6">
              <Clock className="h-12 w-12 text-purple-500 mx-auto mb-4" />
              <h3 className="font-semibold mb-2">Fast Payouts</h3>
              <p className="text-sm text-muted-foreground">Monthly commission payouts via bank transfer or USDT</p>
            </Card>
            <Card className="text-center p-6">
              <TrendingUp className="h-12 w-12 text-orange-500 mx-auto mb-4" />
              <h3 className="font-semibold mb-2">Tier Progression</h3>
              <p className="text-sm text-muted-foreground">Automatic tier upgrades based on performance and volume</p>
            </Card>
          </div>
        </div>

        {/* Partner Types */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-8">Perfect for:</h2>
          <div className="grid md:grid-cols-3 lg:grid-cols-4 gap-4 max-w-6xl mx-auto">
            {[
              'Venture Capital (VC)',
              'Central Exchange (CEX)', 
              'Decentralized Exchange (DEX)',
              'Launchpads',
              'Marketing Agencies',
              'Incubator/Accelerators',
              'Market Makers',
              'Development Teams',
              'Technology Partners',
              'Research Organizations',
              'Data Platforms',
              'Business Development',
              'Deal Flow Individuals',
              'Angel Investors'
            ].map((type) => (
              <Badge key={type} variant="outline" className="p-3 text-center justify-center">
                {type}
              </Badge>
            ))}
          </div>
        </div>

        {/* Application Process */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-8">Simple Application Process</h2>
          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-blue-600">1</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Sign Up</h3>
                <p className="text-muted-foreground">Create your account with email verification</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-blue-600">2</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Complete Profile</h3>
                <p className="text-muted-foreground">Fill out your company details and partnership information</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-blue-600">3</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Start Earning</h3>
                <p className="text-muted-foreground">Get approved and start referring clients with your unique link</p>
              </div>
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center">
          <Card className="max-w-2xl mx-auto p-8 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950">
            <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
            <p className="text-muted-foreground mb-6">
              Join hundreds of partners already earning with IBC Group. 
              Application takes less than 5 minutes.
            </p>
            <Button asChild size="lg" className="text-lg py-6 px-8">
              <Link href="/sign-up">
                Apply for Partnership <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <p className="text-xs text-muted-foreground mt-4">
              Already have an account? <Link href="/sign-in" className="underline">Sign in here</Link>
            </p>
          </Card>
        </div>
      </div>
    </div>
  )
}