"use client"

import { useEffect, useState } from "react"
import { useUser } from "@clerk/nextjs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { 
  TrendingUp, 
  MousePointer, 
  Users,
  Copy,
  ExternalLink,
  Calendar,
  Target,
  Award,
  MessageCircle,
  Loader2
} from "lucide-react"
import { toast } from "sonner"
import { SalesDashboardData } from "@/lib/types/sales"

interface DashboardResponse {
  success: boolean
  data?: SalesDashboardData
  error?: string
}

export default function SalesDashboardPage() {
  const { user } = useUser()
  const [dashboardData, setDashboardData] = useState<SalesDashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState<"7d" | "30d" | "90d">("30d")

  useEffect(() => {
    fetchDashboardData()
  }, [selectedPeriod])

  const fetchDashboardData = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/sales/dashboard?period=${selectedPeriod}`)
      if (response.ok) {
        const result: DashboardResponse = await response.json()
        if (result.success && result.data) {
          setDashboardData(result.data)
        } else {
          toast("Error", { description: result.error || "Failed to load dashboard data" })
        }
      } else {
        if (response.status === 403) {
          toast("Access Denied", { description: "You don't have permission to access the sales dashboard" })
        } else {
          toast("Error", { description: "Failed to fetch dashboard data" })
        }
      }
    } catch (error) {
      console.error('Error fetching dashboard:', error)
      toast("Error", { description: "Network error while fetching data" })
    } finally {
      setLoading(false)
    }
  }

  const copyReferralLink = async (slug: string) => {
    const link = `${window.location.origin}/referral/sales/${slug}`
    try {
      await navigator.clipboard.writeText(link)
      toast("Copied", { description: "Referral link copied to clipboard" })
    } catch (error) {
      console.error('Failed to copy link:', error)
      toast("Error", { description: "Failed to copy referral link" })
    }
  }

  const shareOnTelegram = (slug: string) => {
    const link = `${window.location.origin}/referral/sales/${slug}`
    const message = encodeURIComponent(`Check out IBC Group's enterprise blockchain solutions: ${link}`)
    const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(link)}&text=${message}`
    window.open(telegramUrl, '_blank')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading dashboard...</span>
      </div>
    )
  }

  if (!dashboardData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-lg font-medium">No dashboard data available</p>
          <p className="text-muted-foreground">Please contact your administrator if you should have access.</p>
        </div>
      </div>
    )
  }

  const { personalStats, referralLinks, recentActivity, assignedPartners } = dashboardData

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">Sales Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back, {user?.firstName || user?.emailAddresses[0]?.emailAddress}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant={selectedPeriod === "7d" ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedPeriod("7d")}
          >
            7 Days
          </Button>
          <Button
            variant={selectedPeriod === "30d" ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedPeriod("30d")}
          >
            30 Days
          </Button>
          <Button
            variant={selectedPeriod === "90d" ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedPeriod("90d")}
          >
            90 Days
          </Button>
        </div>
      </div>

      {/* Performance Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <MousePointer className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">Total Clicks</span>
            </div>
            <p className="text-2xl font-bold mt-1">{personalStats.totalClicks}</p>
            <p className="text-xs text-muted-foreground">
              {personalStats.monthlyClicks} this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Conversions</span>
            </div>
            <p className="text-2xl font-bold mt-1">{personalStats.totalConversions}</p>
            <p className="text-xs text-muted-foreground">
              {personalStats.monthlyConversions} this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium">Conversion Rate</span>
            </div>
            <p className="text-2xl font-bold mt-1">
              {(personalStats.conversionRate * 100).toFixed(1)}%
            </p>
            <p className="text-xs text-muted-foreground">
              {personalStats.totalClicks > 0 ? 'Above average' : 'No data yet'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-orange-600" />
              <span className="text-sm font-medium">Assigned Partners</span>
            </div>
            <p className="text-2xl font-bold mt-1">{assignedPartners.length}</p>
            <p className="text-xs text-muted-foreground">
              Active partnerships
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="referral-links" className="space-y-4">
        <TabsList>
          <TabsTrigger value="referral-links">Referral Links</TabsTrigger>
          <TabsTrigger value="partners">Assigned Partners</TabsTrigger>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="referral-links" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Your Referral Links</CardTitle>
              <p className="text-sm text-muted-foreground">
                Share these links to track referrals and earn attribution for new leads
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {referralLinks.map((link) => (
                  <div key={link.slug} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <code className="text-sm bg-muted px-2 py-1 rounded">
                          /referral/sales/{link.slug}
                        </code>
                        <Badge variant={link.isActive ? "success" : "secondary"}>
                          {link.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>{link.clicks} clicks</span>
                        <span>{link.conversions} conversions</span>
                        <span>Created {new Date(link.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => copyReferralLink(link.slug)}
                        className="gap-1"
                      >
                        <Copy className="h-3 w-3" />
                        Copy
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => shareOnTelegram(link.slug)}
                        className="gap-1"
                      >
                        <MessageCircle className="h-3 w-3" />
                        Share
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => window.open(`/referral/sales/${link.slug}`, '_blank')}
                        className="gap-1"
                      >
                        <ExternalLink className="h-3 w-3" />
                        Preview
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="partners" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Assigned Partners ({assignedPartners.length})</CardTitle>
              <p className="text-sm text-muted-foreground">
                Partners you are managing and their performance
              </p>
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Partner</TableHead>
                    <TableHead>Tier</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Deals</TableHead>
                    <TableHead>Commission</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {assignedPartners.map((partner) => (
                    <TableRow key={partner.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{partner.name}</div>
                          {partner.company && (
                            <div className="text-sm text-muted-foreground">{partner.company}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="capitalize">
                          {partner.tier === 'diamond' && '💎'}
                          {partner.tier === 'elite' && '⭐'}
                          {partner.tier === 'trusted' && '🔰'}
                          {partner.tier}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant={partner.status === 'active' ? 'success' : 'secondary'}
                          className="capitalize"
                        >
                          {partner.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">{partner.totalDeals}</span>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">${partner.totalCommission.toLocaleString()}</span>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <p className="text-sm text-muted-foreground">
                Latest clicks and conversions on your referral links
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentActivity.length > 0 ? recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      activity.type === 'conversion' ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'
                    }`}>
                      {activity.type === 'conversion' ? <Award className="h-4 w-4" /> : <MousePointer className="h-4 w-4" />}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium capitalize">
                        {activity.type === 'conversion' ? 'New Conversion' : 'Link Click'}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        From /referral/sales/{activity.slug}
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {new Date(activity.timestamp).toLocaleString()}
                    </div>
                  </div>
                )) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No recent activity</p>
                    <p className="text-sm">Share your referral links to start seeing activity here</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}