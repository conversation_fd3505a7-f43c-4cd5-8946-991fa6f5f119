import { notFound } from 'next/navigation'
import { Metada<PERSON> } from 'next'
import Link from 'next/link'

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, CheckCircle, Users, Zap, Shield, MessageSquare } from "lucide-react"
import { createClient } from '@/lib/supabase/server'
import { log } from '@/lib/log'
import { trackServerEvent } from '@/lib/analytics-server'

type Props = {
  params: Promise<{ slug: string }>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

async function getSalesReferralData(slug: string) {
  try {
    const client = await createClient(true) // Use service role for public access

    // Get sales person and referral link data
    const { data: salesData } = await client
      .from('partners_sales')
      .select(`
        id,
        name,
        email,
        referral_slug,
        telegram_handle,
        telegram_link,
        is_active
      `)
      .eq('referral_slug', slug)
      .eq('is_active', true)
      .single()

    if (!salesData) {
      return null
    }

    // Get the referral link data for click tracking
    const { data: referralLink } = await client
      .from('partners_sales_referral_links')
      .select('id, slug, clicks')
      .eq('slug', slug)
      .eq('active', true)
      .single()

    return {
      salesPerson: salesData,
      referralLink: referralLink || { id: null, slug, clicks: 0 }
    }

  } catch (error) {
    log.error('Failed to fetch sales referral data', { 
      error,
      metadata: { slug }
    })
    return null
  }
}

export async function generateMetadata({ params, searchParams }: Props): Promise<Metadata> {
  const resolvedParams = await params
  const data = await getSalesReferralData(resolvedParams.slug)
  
  if (!data) {
    return {
      title: 'Sales Referral Link Not Found',
      description: 'The sales referral link you followed is not valid.'
    }
  }

  const salesPersonName = data.salesPerson.name

  return {
    title: `Connect with IBC Group via ${salesPersonName}`,
    description: `${salesPersonName} from IBC Group's sales team has shared this link with you. Learn about our enterprise solutions and get expert guidance.`,
    openGraph: {
      title: `Connect with IBC Group via ${salesPersonName}`,
      description: `${salesPersonName} from IBC Group's sales team has shared this link with you. Learn about our enterprise solutions.`,
      type: 'website',
    },
  }
}

export default async function SalesReferralPage({ params, searchParams }: Props) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const data = await getSalesReferralData(resolvedParams.slug)

  if (!data) {
    notFound()
  }

  const { salesPerson, referralLink } = data
  const utmParams = new URLSearchParams()

  // Preserve UTM parameters
  const utmParamsObj: Record<string, string> = {}
  Object.entries(resolvedSearchParams).forEach(([key, value]) => {
    if (key.startsWith('utm_') && typeof value === 'string') {
      utmParams.set(key, value)
      utmParamsObj[key] = value
    }
  })

  const connectUsUrl = `/lead/new?${utmParams.toString()}`

  // Track sales referral link visit
  try {
    await trackServerEvent('sales_referral_link_visited', {
      sales_referral_slug: resolvedParams.slug,
      sales_person_name: salesPerson.name,
      sales_person_id: salesPerson.id,
      ...utmParamsObj
    })

    // Update click count if referral link exists
    if (referralLink.id) {
      const client = await createClient(true)
      await client
        .from('partners_sales_referral_links')
        .update({ clicks: (referralLink.clicks || 0) + 1 })
        .eq('id', referralLink.id)
    }
  } catch (error) {
    log.error('Failed to track sales referral visit', { 
      error,
      metadata: { slug: resolvedParams.slug }
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/50">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="text-2xl font-bold">IBC Group</div>
              <Badge variant="outline" className="hidden sm:flex">
                Enterprise Solutions
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Sales Team</span>
              <Badge variant="default" className="gap-1">
                <span>👤</span>
                {salesPerson.name}
              </Badge>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-12">
        <div className="mx-auto max-w-4xl space-y-12">
          
          {/* Hero Section */}
          <div className="text-center space-y-6">
            <div className="space-y-2">
              <h1 className="text-4xl font-bold tracking-tight lg:text-5xl">
                Welcome to IBC Group
              </h1>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                You've been connected by <strong>{salesPerson.name}</strong> from our sales team.
                Get expert guidance on our enterprise blockchain solutions.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild className="gap-2">
                <Link href={connectUsUrl}>
                  Connect with Us
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="#learn-more">
                  Learn More
                </Link>
              </Button>
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6" id="learn-more">
            <Card className="text-center">
              <CardHeader>
                <Shield className="h-8 w-8 mx-auto text-primary" />
                <CardTitle className="text-lg">Enterprise Security</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Bank-grade security and compliance for your mission-critical blockchain applications.
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardHeader>
                <Zap className="h-8 w-8 mx-auto text-primary" />
                <CardTitle className="text-lg">Rapid Deployment</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Get up and running in days, not months, with our streamlined blockchain onboarding.
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardHeader>
                <Users className="h-8 w-8 mx-auto text-primary" />
                <CardTitle className="text-lg">Expert Support</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  24/7 support from our team of blockchain and enterprise solution specialists.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Sales Team Contact Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-blue-600" />
                Direct Sales Contact
              </CardTitle>
              <CardDescription>
                Connect directly with our sales team for personalized service
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                  <span className="text-lg">👤</span>
                </div>
                <div className="flex-1">
                  <div className="font-semibold">{salesPerson.name}</div>
                  <div className="text-sm text-muted-foreground">IBC Group Sales Team</div>
                  <Badge variant="outline" className="text-xs mt-1">
                    Sales Representative
                  </Badge>
                </div>
                {salesPerson.telegram_handle && (
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" asChild>
                      <Link 
                        href={salesPerson.telegram_link || `https://t.me/${salesPerson.telegram_handle.replace('@', '')}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="gap-2"
                      >
                        <MessageSquare className="h-3 w-3" />
                        Telegram
                      </Link>
                    </Button>
                  </div>
                )}
              </div>
              <p className="text-sm text-muted-foreground">
                {salesPerson.name} is a member of our experienced sales team, ready to provide expert guidance 
                on IBC Group's blockchain infrastructure and enterprise solutions. They understand your industry's 
                unique challenges and can help you find the perfect solution.
              </p>
            </CardContent>
          </Card>

          {/* CTA Section */}
          <div className="bg-primary/5 rounded-lg p-8 text-center space-y-4">
            <h2 className="text-2xl font-bold">Ready to Get Started?</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Connect with us to learn how IBC Group's enterprise blockchain solutions can transform your business. 
              {salesPerson.name} will be your dedicated point of contact throughout the process.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild className="gap-2">
                <Link href={connectUsUrl}>
                  Start Your Journey
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
              {salesPerson.telegram_handle && (
                <Button size="lg" variant="outline" asChild className="gap-2">
                  <Link 
                    href={salesPerson.telegram_link || `https://t.me/${salesPerson.telegram_handle.replace('@', '')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <MessageSquare className="h-4 w-4" />
                    Contact {salesPerson.name} on Telegram
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t bg-background/95 mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-sm text-muted-foreground">
              © 2024 IBC Group. All rights reserved.
            </div>
            <div className="flex gap-4 text-sm">
              <Link href="/privacy" className="text-muted-foreground hover:text-foreground">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-muted-foreground hover:text-foreground">
                Terms of Service
              </Link>
              <Link href="/contact" className="text-muted-foreground hover:text-foreground">
                Contact
              </Link>
            </div>
          </div>
        </div>
      </footer>
      
      {/* Attribution tracking script */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
            // Sales attribution system for referral tracking
            (function() {
              'use strict';
              
              const slug = '${resolvedParams.slug}';
              const salesPersonName = '${salesPerson.name}';
              const salesPersonId = '${salesPerson.id}';
              
              // Set sales attribution cookie
              const attribution = {
                type: 'sales',
                slug: slug,
                salesPersonName: salesPersonName,
                salesPersonId: salesPersonId,
                timestamp: Date.now(),
                visitId: Math.random().toString(36).substr(2, 9)
              };
              
              // Set cookie for 30 days
              const expires = new Date();
              expires.setTime(expires.getTime() + (30 * 24 * 60 * 60 * 1000));
              
              const cookieValue = encodeURIComponent(JSON.stringify(attribution));
              document.cookie = 'ibc_sales_ref=' + cookieValue + '; expires=' + expires.toUTCString() + '; path=/; SameSite=Lax';
              
              console.log('Sales attribution cookie set:', attribution);
            })();
          `
        }}
      />
    </div>
  )
}