// Attribution system for referral landing pages
// This script sets the referral attribution cookie when users visit /referral/[slug]

(function() {
  'use strict';
  
  // Extract slug from URL path
  const pathParts = window.location.pathname.split('/');
  if (pathParts[1] !== 'referral' || !pathParts[2]) {
    return; // Not a referral page
  }
  
  const slug = pathParts[2];
  
  // Get partner information from the page or fetch it
  const getPartnerInfo = async () => {
    try {
      const response = await fetch(`/api/referral/info?slug=${encodeURIComponent(slug)}`);
      if (!response.ok) {
        throw new Error('Failed to fetch partner info');
      }
      return await response.json();
    } catch (error) {
      console.warn('Failed to fetch partner info:', error);
      return null;
    }
  };
  
  // Set attribution cookie
  const setAttributionCookie = (partnerInfo) => {
    const attribution = {
      slug: slug,
      partnerName: partnerInfo?.name || 'Unknown Partner',
      timestamp: Date.now(),
      visitId: Math.random().toString(36).substr(2, 9)
    };
    
    // Set cookie for 30 days
    const expires = new Date();
    expires.setTime(expires.getTime() + (30 * 24 * 60 * 60 * 1000));
    
    const cookieValue = encodeURIComponent(JSON.stringify(attribution));
    document.cookie = `ibc_partner_ref=${cookieValue}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`;
    
    console.log('Attribution cookie set:', attribution);
  };
  
  // Initialize attribution tracking
  const initAttribution = async () => {
    const partnerInfo = await getPartnerInfo();
    if (partnerInfo) {
      setAttributionCookie(partnerInfo);
      
      // Track page visit (optional analytics)
      if (window.gtag) {
        window.gtag('event', 'referral_page_visit', {
          'referral_slug': slug,
          'partner_name': partnerInfo.name
        });
      }
    }
  };
  
  // Run when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initAttribution);
  } else {
    initAttribution();
  }
})();