import { notFound } from 'next/navigation'
import { Metada<PERSON> } from 'next'
import Link from 'next/link'

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, CheckCircle, Users, Zap, Shield } from "lucide-react"
import { createClient } from '@/lib/supabase/server'
import { log } from '@/lib/log'
import { trackServerEvent } from '@/lib/analytics-server'

type Props = {
  params: Promise<{ slug: string }>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

async function getReferralData(slug: string) {
  try {
    const client = await createClient(true) // Use service role for public access

    // Get partner and referral link data
    const { data: referralLink } = await client
      .from('partners_referral_links')
      .select(`
        id,
        slug,
        active,
        partners_profiles (
          full_name,
          company_name,
          tier,
          status
        )
      `)
      .eq('slug', slug)
      .eq('active', true)
      .single()

    if (!referralLink) {
      return null
    }

    const partner = Array.isArray(referralLink.partners_profiles) 
      ? referralLink.partners_profiles[0] 
      : referralLink.partners_profiles

    if (!partner || partner?.status !== 'active') {
      return null
    }

    return {
      partner,
      referralLink: {
        id: referralLink.id,
        slug: referralLink.slug
      }
    }

  } catch (error) {
    log.error('Failed to fetch referral data', { 
      error,
      metadata: { slug }
    })
    return null
  }
}

export async function generateMetadata({ params, searchParams }: Props): Promise<Metadata> {
  const resolvedParams = await params
  const data = await getReferralData(resolvedParams.slug)
  
  if (!data) {
    return {
      title: 'Referral Link Not Found',
      description: 'The referral link you followed is not valid.'
    }
  }

  const partnerName = data.partner.full_name
  const companyName = data.partner.company_name

  return {
    title: `Connect with IBC Group via ${partnerName}`,
    description: `${partnerName}${companyName ? ` from ${companyName}` : ''} has referred you to IBC Group. Learn about our enterprise solutions and get started with a trusted partner.`,
    openGraph: {
      title: `Connect with IBC Group via ${partnerName}`,
      description: `${partnerName}${companyName ? ` from ${companyName}` : ''} has referred you to IBC Group. Learn about our enterprise solutions.`,
      type: 'website',
    },
  }
}

export default async function ReferralPage({ params, searchParams }: Props) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const data = await getReferralData(resolvedParams.slug)

  if (!data) {
    notFound()
  }

  const { partner, referralLink } = data
  const utmParams = new URLSearchParams()

  // Preserve UTM parameters
  const utmParamsObj: Record<string, string> = {}
  Object.entries(resolvedSearchParams).forEach(([key, value]) => {
    if (key.startsWith('utm_') && typeof value === 'string') {
      utmParams.set(key, value)
      utmParamsObj[key] = value
    }
  })

  const connectUsUrl = `/lead/new?${utmParams.toString()}`

  // Track referral link visit
  try {
    await trackServerEvent('referral_link_visited', {
      referral_slug: resolvedParams.slug,
      partner_name: partner.full_name,
      partner_tier: partner.tier,
      ...utmParamsObj
    })
  } catch (error) {
    log.error('Failed to track referral visit', { 
      error,
      metadata: { slug: resolvedParams.slug }
    })
  }

  const getTierEmoji = (tier: string) => {
    switch (tier) {
      case 'diamond': return '💎'
      case 'elite': return '⭐'
      case 'trusted': return '🔰'
      default: return '🔰'
    }
  }

  const getTierLabel = (tier: string) => tier.charAt(0).toUpperCase() + tier.slice(1)

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/50">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="text-2xl font-bold">IBC Group</div>
              <Badge variant="outline" className="hidden sm:flex">
                Enterprise Solutions
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Referred by</span>
              <Badge variant="secondary" className="gap-1">
                <span>{getTierEmoji(partner.tier)}</span>
                {partner.full_name}
              </Badge>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-12">
        <div className="mx-auto max-w-4xl space-y-12">
          
          {/* Hero Section */}
          <div className="text-center space-y-6">
            <div className="space-y-2">
              <h1 className="text-4xl font-bold tracking-tight lg:text-5xl">
                Welcome to IBC Group
              </h1>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                You've been referred by <strong>{partner.full_name}</strong>
                {partner.company_name && (
                  <span> from <strong>{partner.company_name}</strong></span>
                )}, a {getTierLabel(partner.tier)} tier partner.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild className="gap-2">
                <Link href={connectUsUrl}>
                  Connect with Us
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="#learn-more">
                  Learn More
                </Link>
              </Button>
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6" id="learn-more">
            <Card className="text-center">
              <CardHeader>
                <Shield className="h-8 w-8 mx-auto text-primary" />
                <CardTitle className="text-lg">Enterprise Security</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Bank-grade security and compliance for your mission-critical applications.
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardHeader>
                <Zap className="h-8 w-8 mx-auto text-primary" />
                <CardTitle className="text-lg">Rapid Deployment</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Get up and running in days, not months, with our streamlined onboarding.
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardHeader>
                <Users className="h-8 w-8 mx-auto text-primary" />
                <CardTitle className="text-lg">Expert Support</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  24/7 support from our team of enterprise solution specialists.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Partner Trust Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Trusted Partner Referral
              </CardTitle>
              <CardDescription>
                This referral comes from a verified IBC Group partner
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                  <span className="text-lg">{getTierEmoji(partner.tier)}</span>
                </div>
                <div>
                  <div className="font-semibold">{partner.full_name}</div>
                  {partner.company_name && (
                    <div className="text-sm text-muted-foreground">{partner.company_name}</div>
                  )}
                  <Badge variant="outline" className="text-xs mt-1">
                    {getTierLabel(partner.tier)} Partner
                  </Badge>
                </div>
              </div>
              <p className="text-sm text-muted-foreground">
                Our {getTierLabel(partner.tier)} tier partners are carefully vetted professionals who understand 
                your industry's unique challenges and can provide expert guidance throughout your journey with IBC Group.
              </p>
            </CardContent>
          </Card>

          {/* CTA Section */}
          <div className="bg-primary/5 rounded-lg p-8 text-center space-y-4">
            <h2 className="text-2xl font-bold">Ready to Get Started?</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Connect with us to learn how IBC Group's enterprise solutions can transform your business. 
              Your trusted partner {partner.full_name} will be credited for this referral.
            </p>
            <Button size="lg" asChild className="gap-2">
              <Link href={connectUsUrl}>
                Start Your Journey
                <ArrowRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t bg-background/95 mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-sm text-muted-foreground">
              © 2024 IBC Group. All rights reserved.
            </div>
            <div className="flex gap-4 text-sm">
              <Link href="/privacy" className="text-muted-foreground hover:text-foreground">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-muted-foreground hover:text-foreground">
                Terms of Service
              </Link>
              <Link href="/contact" className="text-muted-foreground hover:text-foreground">
                Contact
              </Link>
            </div>
          </div>
        </div>
      </footer>
      
      {/* Attribution tracking script */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
            // Attribution system for referral tracking
            (function() {
              'use strict';
              
              const slug = '${resolvedParams.slug}';
              const partnerName = '${partner.full_name}';
              
              // Set attribution cookie
              const attribution = {
                slug: slug,
                partnerName: partnerName,
                timestamp: Date.now(),
                visitId: Math.random().toString(36).substr(2, 9)
              };
              
              // Set cookie for 30 days
              const expires = new Date();
              expires.setTime(expires.getTime() + (30 * 24 * 60 * 60 * 1000));
              
              const cookieValue = encodeURIComponent(JSON.stringify(attribution));
              document.cookie = 'ibc_partner_ref=' + cookieValue + '; expires=' + expires.toUTCString() + '; path=/; SameSite=Lax';
              
              console.log('Attribution cookie set:', attribution);
            })();
          `
        }}
      />
    </div>
  )
}
