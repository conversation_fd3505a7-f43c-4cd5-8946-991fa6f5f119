"use client"

import Link from "next/link"
import { AppShell } from "@/app/components/app-shell"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Share2, Copy } from "lucide-react"
import { notFound } from "next/navigation"

type Props = { params: { slug: string } }

export default function ReferralLandingClient({ params }: Props) {
  const slug = params.slug

  // Simulated states:
  if (slug === "invalid") {
    notFound()
  }
  const isSuspended = slug === "suspended"

  // Simulate loading skeleton briefly (server-first design would do real loading)
  // For scaffold, show full UI.
  const href = `/lead/new?ref=${encodeURIComponent(slug)}`
  const shareUrl = `${typeof window !== "undefined" ? window.location.origin : ""}/referral/${slug}`

  async function onShare() {
    const url = typeof window !== "undefined" ? window.location.href : ""
    if (navigator.share) {
      await navigator.share({ title: "Referral", url })
    } else {
      await navigator.clipboard.writeText(url)
      alert("Link copied")
    }
  }

  return (
    <AppShell title="Referral">
      <div className="mx-auto w-full max-w-3xl">
        <Card className="rounded-2xl">
          <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            <img src="/diverse-avatars.png" width={64} height={64} alt="Partner avatar" className="rounded-full" />
            <div className="w-full sm:w-auto">
              <CardTitle className="flex items-center gap-2">
                Referred by <span className="font-mono truncate max-w-[180px]">{slug}</span>
                <Badge variant="outline">Trusted</Badge>
              </CardTitle>
              <CardDescription>
                Hi — I think you’d be a great fit. Submit your project and our team will review.
              </CardDescription>
            </div>
            <div className="w-full sm:w-auto flex sm:ml-auto gap-2">
              <Button variant="outline" className="bg-transparent" onClick={onShare} aria-label="Share referral link">
                <Share2 className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="bg-transparent"
                onClick={() => navigator.clipboard.writeText(shareUrl)}
                aria-label="Copy referral link"
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-3">
              <Button asChild disabled={isSuspended}>
                <Link href={href}>Apply / Refer a Project</Link>
              </Button>
              <Button variant="ghost" asChild>
                <Link href="/resources">Read FAQ</Link>
              </Button>
              {isSuspended ? <span className="text-sm text-warning">This partner is currently suspended.</span> : null}
            </div>
            <div className="mt-2">
              <Skeleton className="h-24 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
