"use client"

import * as React from "react"
import { AppShell } from "@/app/components/app-shell"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

const CURRENT_VERSION = "v1.2.0"

export default function TermsPage() {
  const [accepted, setAccepted] = React.useState<string | null>(null)
  React.useEffect(() => {
    setAccepted(localStorage.getItem("termsAcceptedVersion"))
  }, [])
  const accept = () => {
    localStorage.setItem("termsAcceptedVersion", CURRENT_VERSION)
    setAccepted(CURRENT_VERSION)
  }

  return (
    <AppShell title="Terms">
      <Card className="max-w-3xl">
        <CardHeader>
          <CardTitle>Terms & Conditions</CardTitle>
          <CardDescription>Current version: {CURRENT_VERSION}</CardDescription>
        </CardHeader>
        <CardContent className="prose prose-sm max-w-none dark:prose-invert">
          <p className="text-sm text-muted-foreground">
            Versioned read-only viewer. Your last accepted version: {accepted || "None"}.
          </p>
          <hr className="my-4" />
          <p>{"[Terms content here.]"}</p>
        </CardContent>
        <CardFooter className="justify-between">
          <div className="text-xs text-muted-foreground">
            {accepted === CURRENT_VERSION
              ? "You have accepted the latest version."
              : "You need to accept the updated terms."}
          </div>
          {accepted === CURRENT_VERSION ? (
            <Button variant="outline" className="bg-transparent">
              Re-accept
            </Button>
          ) : (
            <Button onClick={accept}>Accept</Button>
          )}
        </CardFooter>
      </Card>
    </AppShell>
  )
}
