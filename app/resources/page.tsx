"use client"

import { AppShell } from "@/app/components/app-shell"
import { <PERSON><PERSON><PERSON><PERSON> } from "@/app/components/page-header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Download, Filter, ExternalLink, FileText, Presentation, BookOpen, Palette, Calendar, MessageSquare } from "lucide-react"
import { useState } from "react"

const resources = [
  {
    title: "IBC Partner Playbook 2025",
    type: "guide",
    category: "Strategy",
    description: "Complete guide to maximizing your partnership success with IBC Group.",
    size: "2.4 MB",
    format: "PDF",
    updated: "2025-01-15",
    downloadUrl: "#",
    featured: true
  },
  {
    title: "Web3 Infrastructure Services Deck",
    type: "deck",
    category: "Sales Materials",
    description: "Comprehensive presentation covering our blockchain infrastructure solutions.",
    size: "8.1 MB",
    format: "PPTX",
    updated: "2025-01-10",
    downloadUrl: "#",
    featured: true
  },
  {
    title: "DeFi Protocol Case Study",
    type: "case-study",
    category: "Success Stories",
    description: "How we helped a DeFi protocol scale to $500M TVL in 6 months.",
    size: "1.8 MB",
    format: "PDF",
    updated: "2024-12-20",
    downloadUrl: "#",
    featured: false
  },
  {
    title: "IBC Brand Assets Package",
    type: "assets",
    category: "Brand Materials",
    description: "Official logos, brand guidelines, and co-marketing templates.",
    size: "15.2 MB",
    format: "ZIP",
    updated: "2024-11-30",
    downloadUrl: "#",
    featured: false
  },
  {
    title: "Technical Integration Guide",
    type: "guide",
    category: "Technical",
    description: "Step-by-step guide for technical integrations and API documentation.",
    size: "3.7 MB",
    format: "PDF",
    updated: "2025-01-08",
    downloadUrl: "#",
    featured: false
  },
  {
    title: "Q4 2024 Market Analysis",
    type: "deck",
    category: "Market Intelligence",
    description: "Comprehensive analysis of Web3 infrastructure market trends.",
    size: "5.9 MB",
    format: "PPTX",
    updated: "2025-01-05",
    downloadUrl: "#",
    featured: false
  },
  {
    title: "Partnership ROI Calculator",
    type: "tool",
    category: "Sales Tools",
    description: "Interactive calculator to demonstrate partnership value to prospects.",
    size: "892 KB",
    format: "XLSX",
    updated: "2024-12-15",
    downloadUrl: "#",
    featured: false
  },
  {
    title: "Competitive Analysis: Infrastructure Providers",
    type: "guide",
    category: "Competitive Intelligence",
    description: "Detailed comparison of IBC vs major competitors in the space.",
    size: "2.1 MB",
    format: "PDF",
    updated: "2024-12-10",
    downloadUrl: "#",
    featured: false
  }
]

const quickLinks = [
  {
    title: "Partner Telegram Group",
    description: "Join our private partner community",
    icon: MessageSquare,
    url: "https://t.me/ibcpartners",
    external: true
  },
  {
    title: "Book a Strategy Call",
    description: "Schedule time with your sales rep",
    icon: Calendar,
    url: "https://calendly.com/ibc-partnerships",
    external: true
  },
  {
    title: "Partner Portal Training",
    description: "Learn how to maximize this portal",
    icon: BookOpen,
    url: "#",
    external: false
  }
]

export default function ResourcesPage() {
  const [filterType, setFilterType] = useState("all")
  
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'guide': return FileText
      case 'deck': return Presentation
      case 'case-study': return BookOpen
      case 'assets': return Palette
      case 'tool': return Download
      default: return FileText
    }
  }

  const getTypeBadgeVariant = (type: string) => {
    switch (type) {
      case 'guide': return 'default'
      case 'deck': return 'secondary'
      case 'case-study': return 'outline'
      case 'assets': return 'destructive'
      case 'tool': return 'secondary'
      default: return 'outline'
    }
  }

  const filteredResources = filterType === "all" 
    ? resources 
    : resources.filter(resource => resource.type === filterType)

  const featuredResources = resources.filter(resource => resource.featured)

  return (
    <AppShell title="Resources">
      <div className="flex flex-col gap-6">
        <PageHeader 
          title="Partner Resources" 
          subtitle="Access the latest materials, case studies, and tools to accelerate your success."
          actions={
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" /> Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                <DropdownMenuLabel>Filter by Type</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuRadioGroup value={filterType} onValueChange={setFilterType}>
                  <DropdownMenuRadioItem value="all">All Resources</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="guide">Guides</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="deck">Presentations</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="case-study">Case Studies</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="assets">Brand Assets</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="tool">Tools</DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          }
        />

        {/* Quick Links Section */}
        <div className="grid gap-4 sm:grid-cols-3">
          {quickLinks.map((link) => {
            const IconComponent = link.icon
            return (
              <Card key={link.title} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center">
                      <IconComponent className="h-6 w-6 text-primary" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-sm">{link.title}</h3>
                      <p className="text-xs text-muted-foreground">{link.description}</p>
                    </div>
                    <Button asChild size="sm" variant="ghost">
                      <a 
                        href={link.url} 
                        target={link.external ? "_blank" : undefined}
                        rel={link.external ? "noopener noreferrer" : undefined}
                        className="shrink-0"
                      >
                        {link.external ? <ExternalLink className="h-4 w-4" /> : <BookOpen className="h-4 w-4" />}
                      </a>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Featured Resources */}
        {filterType === "all" && featuredResources.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold mb-4">Featured Resources</h2>
            <div className="grid gap-4 sm:grid-cols-2">
              {featuredResources.map((resource) => {
                const IconComponent = getTypeIcon(resource.type)
                return (
                  <Card key={resource.title} className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center">
                            <IconComponent className="h-5 w-5 text-primary" />
                          </div>
                          <div>
                            <CardTitle className="text-base">{resource.title}</CardTitle>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant={getTypeBadgeVariant(resource.type)} className="text-xs">
                                {resource.type.replace('-', ' ')}
                              </Badge>
                              <span className="text-xs text-muted-foreground">{resource.size}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-sm">{resource.description}</CardDescription>
                      <div className="flex items-center gap-4 mt-3 text-xs text-muted-foreground">
                        <span>Updated {new Date(resource.updated).toLocaleDateString()}</span>
                        <span>{resource.format}</span>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button className="w-full">
                        <Download className="mr-2 h-4 w-4" /> Download
                      </Button>
                    </CardFooter>
                  </Card>
                )
              })}
            </div>
          </div>
        )}

        {/* All Resources */}
        <div>
          <h2 className="text-lg font-semibold mb-4">
            {filterType === "all" ? "All Resources" : `${filterType.charAt(0).toUpperCase() + filterType.slice(1).replace('-', ' ')}s`}
          </h2>
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {filteredResources.map((resource) => {
              const IconComponent = getTypeIcon(resource.type)
              return (
                <Card key={resource.title} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-start gap-3">
                      <div className="h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center">
                        <IconComponent className="h-4 w-4 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <CardTitle className="text-sm leading-tight">{resource.title}</CardTitle>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant={getTypeBadgeVariant(resource.type)} className="text-xs">
                            {resource.category}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-xs">{resource.description}</CardDescription>
                    <div className="flex items-center gap-3 mt-3 text-xs text-muted-foreground">
                      <span>{resource.size}</span>
                      <span>{resource.format}</span>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button variant="secondary" className="w-full" size="sm">
                      <Download className="mr-2 h-3 w-3" /> Download
                    </Button>
                  </CardFooter>
                </Card>
              )
            })}
          </div>
        </div>
      </div>
    </AppShell>
  )
}
