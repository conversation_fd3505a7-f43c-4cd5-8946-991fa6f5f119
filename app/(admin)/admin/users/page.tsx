"use client"

import { useEffect, useState } from "react"
import { AppShell } from "@/app/components/app-shell"
import { PageHeader } from "@/app/components/page-header"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialog<PERSON><PERSON>er, <PERSON>ertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { Users, UserPlus, Edit3, Trash2, Shield, Clock, Search, Loader2 } from "lucide-react"
import { toast } from "sonner"
import type { AdminUser, AdminRole } from "@/lib/validations/users"

const roleLabels: Record<AdminRole, string> = {
  sales: 'Sales',
  ops: 'Operations',
  accounting: 'Accounting', 
  super_admin: 'Super Admin'
}

const roleColors: Record<AdminRole, string> = {
  sales: 'bg-blue-500',
  ops: 'bg-green-500',
  accounting: 'bg-yellow-500',
  super_admin: 'bg-red-500'
}

export default function UserManagement() {
  const [users, setUsers] = useState<AdminUser[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('all')
  
  // Modal states
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [selectedUser, setSelectedUser] = useState<AdminUser | null>(null)
  const [actionLoading, setActionLoading] = useState(false)
  
  // Form states
  const [addForm, setAddForm] = useState({
    email: '',
    display_name: '',
    role: '' as AdminRole | ''
  })
  const [editForm, setEditForm] = useState({
    role: '' as AdminRole | '',
    reason: ''
  })
  const [deleteReason, setDeleteReason] = useState('')

  useEffect(() => {
    fetchUsers()
  }, [roleFilter, searchTerm])

  const fetchUsers = async () => {
    try {
      const params = new URLSearchParams()
      if (roleFilter && roleFilter !== 'all') params.set('role', roleFilter)
      if (searchTerm) params.set('search', searchTerm)
      
      const response = await fetch(`/api/admin/users?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch users')
      }
      const result = await response.json()
      setUsers(result.data || [])
    } catch (error) {
      console.error('Error fetching users:', error)
      toast.error('Failed to load users')
    } finally {
      setLoading(false)
    }
  }

  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault()
    setActionLoading(true)
    
    try {
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(addForm)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to add user')
      }

      toast.success('User added successfully')
      setShowAddModal(false)
      setAddForm({ email: '', display_name: '', role: '' })
      await fetchUsers()
    } catch (error) {
      console.error('Error adding user:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to add user')
    } finally {
      setActionLoading(false)
    }
  }

  const handleEditRole = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedUser) return
    
    setActionLoading(true)
    
    try {
      const response = await fetch('/api/admin/users', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          user_id: selectedUser.id,
          role: editForm.role,
          reason: editForm.reason
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update user role')
      }

      toast.success('User role updated successfully')
      setShowEditModal(false)
      setEditForm({ role: '', reason: '' })
      setSelectedUser(null)
      await fetchUsers()
    } catch (error) {
      console.error('Error updating user role:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to update user role')
    } finally {
      setActionLoading(false)
    }
  }

  const handleRemoveAccess = async () => {
    if (!selectedUser) return
    
    setActionLoading(true)
    
    try {
      const response = await fetch('/api/admin/users', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          user_id: selectedUser.id,
          reason: deleteReason
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to remove admin access')
      }

      toast.success('Admin access removed successfully')
      setShowDeleteModal(false)
      setDeleteReason('')
      setSelectedUser(null)
      await fetchUsers()
    } catch (error) {
      console.error('Error removing admin access:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to remove admin access')
    } finally {
      setActionLoading(false)
    }
  }

  const openEditModal = (user: AdminUser) => {
    setSelectedUser(user)
    setEditForm({
      role: user.role || '',
      reason: ''
    })
    setShowEditModal(true)
  }

  const openDeleteModal = (user: AdminUser) => {
    setSelectedUser(user)
    setDeleteReason('')
    setShowDeleteModal(true)
  }

  const formatLastLogin = (lastSignIn: string | null) => {
    if (!lastSignIn) return 'Never'
    return new Date(lastSignIn).toLocaleDateString()
  }

  const getRoleBadge = (role: AdminRole | null) => {
    if (!role) return <Badge variant="outline">No Access</Badge>
    
    return (
      <Badge className={`text-white ${roleColors[role]}`}>
        {roleLabels[role]}
      </Badge>
    )
  }

  return (
    <AppShell title="User Management">
      <div className="flex flex-col gap-6">
      <PageHeader
        title="User Management"
        subtitle="Manage admin users and their roles. Only visible to Super Admins."
        actions={
          <Button onClick={() => setShowAddModal(true)}>
            <UserPlus className="mr-2 h-4 w-4" />
            Add Admin User
          </Button>
        }
      />

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Users & Roles
          </CardTitle>
          <CardDescription>
            View all users who have logged in and manage their admin access
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Users</SelectItem>
                <SelectItem value="super_admin">Super Admins</SelectItem>
                <SelectItem value="ops">Operations</SelectItem>
                <SelectItem value="accounting">Accounting</SelectItem>
                <SelectItem value="sales">Sales</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Last Login</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                      No users found
                    </TableCell>
                  </TableRow>
                ) : (
                  users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          {user.profile_image_url ? (
                            <img 
                              src={user.profile_image_url} 
                              alt="" 
                              className="w-8 h-8 rounded-full"
                            />
                          ) : (
                            <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                              {(user.first_name?.[0] || user.email[0]).toUpperCase()}
                            </div>
                          )}
                          <div>
                            <div className="font-medium">
                              {user.display_name || `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Unknown'}
                            </div>
                            <div className="text-sm text-muted-foreground">{user.email}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getRoleBadge(user.role)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          {formatLastLogin(user.last_sign_in_at || null)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={user.clerk_user_id ? "default" : "secondary"}>
                          {user.clerk_user_id ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex gap-2 justify-end">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => openEditModal(user)}
                            disabled={!user.role}
                          >
                            <Edit3 className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => openDeleteModal(user)}
                            disabled={!user.role}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Add User Modal */}
      <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Admin User</DialogTitle>
            <DialogDescription>
              Grant admin access to a user. They must have logged in at least once.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleAddUser} className="space-y-4">
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={addForm.email}
                onChange={(e) => setAddForm({ ...addForm, email: e.target.value })}
                placeholder="<EMAIL>"
                required
              />
            </div>
            <div>
              <Label htmlFor="display_name">Display Name</Label>
              <Input
                id="display_name"
                value={addForm.display_name}
                onChange={(e) => setAddForm({ ...addForm, display_name: e.target.value })}
                placeholder="John Doe"
                required
              />
            </div>
            <div>
              <Label htmlFor="role">Role</Label>
              <Select 
                value={addForm.role} 
                onValueChange={(value) => setAddForm({ ...addForm, role: value as AdminRole })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sales">Sales</SelectItem>
                  <SelectItem value="ops">Operations</SelectItem>
                  <SelectItem value="accounting">Accounting</SelectItem>
                  <SelectItem value="super_admin">Super Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => setShowAddModal(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={actionLoading}>
                {actionLoading && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                Add User
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Role Modal */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit User Role</DialogTitle>
            <DialogDescription>
              Change the role for {selectedUser?.display_name || selectedUser?.email}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleEditRole} className="space-y-4">
            <div>
              <Label htmlFor="edit_role">New Role</Label>
              <Select 
                value={editForm.role} 
                onValueChange={(value) => setEditForm({ ...editForm, role: value as AdminRole })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sales">Sales</SelectItem>
                  <SelectItem value="ops">Operations</SelectItem>
                  <SelectItem value="accounting">Accounting</SelectItem>
                  <SelectItem value="super_admin">Super Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="reason">Reason (optional)</Label>
              <Textarea
                id="reason"
                value={editForm.reason}
                onChange={(e) => setEditForm({ ...editForm, reason: e.target.value })}
                placeholder="Reason for role change..."
                rows={3}
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => setShowEditModal(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={actionLoading || !editForm.role}>
                {actionLoading && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                Update Role
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Remove Access Modal */}
      <AlertDialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Admin Access</AlertDialogTitle>
            <AlertDialogDescription>
              This will remove admin access for {selectedUser?.display_name || selectedUser?.email}.
              They will no longer be able to access the admin panel.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-4">
            <Label htmlFor="delete_reason">Reason for removal</Label>
            <Textarea
              id="delete_reason"
              value={deleteReason}
              onChange={(e) => setDeleteReason(e.target.value)}
              placeholder="Why are you removing admin access?"
              rows={3}
              className="mt-2"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={actionLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleRemoveAccess}
              disabled={actionLoading || !deleteReason.trim()}
              className="bg-red-600 hover:bg-red-700"
            >
              {actionLoading && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
              Remove Access
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      </div>
    </AppShell>
  )
}