export type AnalyticsEvent = 
  | 'referral_link_visited'
  | 'sales_referral_link_visited'
  | 'lead_form_started'
  | 'lead_form_submitted'
  | 'lead_form_abandoned'
  | 'partner_login'
  | 'partner_dashboard_viewed'
  | 'earnings_page_viewed'
  | 'withdrawal_requested'
  | 'referral_link_copied'
  | 'deal_status_changed'

export interface AnalyticsData {
  event: AnalyticsEvent
  properties?: Record<string, any>
  userId?: string
  sessionId?: string
  timestamp?: string
}

class Analytics {
  private enabled: boolean = true
  private sessionId: string
  private userId?: string

  constructor() {
    this.sessionId = this.generateSessionId()
    this.enabled = process.env.NODE_ENV === 'production'
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  setUserId(userId: string) {
    this.userId = userId
  }

  async track(event: AnalyticsEvent, properties?: Record<string, any>) {
    if (!this.enabled) {
      console.log('Analytics (dev):', event, properties)
      return
    }

    const data: AnalyticsData = {
      event,
      properties: {
        ...properties,
        url: typeof window !== 'undefined' ? window.location.href : undefined,
        referrer: typeof window !== 'undefined' ? document.referrer : undefined,
        userAgent: typeof window !== 'undefined' ? navigator.userAgent : undefined,
      },
      userId: this.userId,
      sessionId: this.sessionId,
      timestamp: new Date().toISOString()
    }

    try {
      // Send to internal analytics endpoint
      await fetch('/api/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })
    } catch (error) {
      console.error('Analytics tracking failed:', error)
    }
  }

  // Referral attribution tracking
  async trackReferralVisit(slug: string, partnerName: string, searchParams?: Record<string, string>) {
    await this.track('referral_link_visited', {
      referral_slug: slug,
      partner_name: partnerName,
      utm_source: searchParams?.utm_source,
      utm_medium: searchParams?.utm_medium,
      utm_campaign: searchParams?.utm_campaign,
      utm_content: searchParams?.utm_content,
      utm_term: searchParams?.utm_term,
    })
  }

  // Lead form tracking
  async trackLeadFormStart() {
    await this.track('lead_form_started')
  }

  async trackLeadFormSubmit(leadId: string, attribution?: any) {
    await this.track('lead_form_submitted', {
      lead_id: leadId,
      has_attribution: !!attribution,
      attribution_partner: attribution?.partner_id,
      attribution_slug: attribution?.slug,
    })
  }

  async trackLeadFormAbandon(step: string) {
    await this.track('lead_form_abandoned', {
      abandonment_step: step,
    })
  }

  // Partner activity tracking
  async trackPartnerLogin(tier: string) {
    await this.track('partner_login', {
      partner_tier: tier,
    })
  }

  async trackPageView(page: string) {
    const eventMap: Record<string, AnalyticsEvent> = {
      'dashboard': 'partner_dashboard_viewed',
      'earnings': 'earnings_page_viewed',
    }

    const event = eventMap[page]
    if (event) {
      await this.track(event, { page })
    }
  }

  async trackReferralLinkCopy(slug: string) {
    await this.track('referral_link_copied', {
      referral_slug: slug,
    })
  }

  async trackWithdrawalRequest(amount: number, method: string) {
    await this.track('withdrawal_requested', {
      amount,
      method,
    })
  }

  async trackDealStatusChange(dealId: string, oldStatus: string, newStatus: string) {
    await this.track('deal_status_changed', {
      deal_id: dealId,
      old_status: oldStatus,
      new_status: newStatus,
    })
  }
}

// Export singleton instance
export const analytics = new Analytics()


