import { createClient } from '@/lib/supabase/server'
import type { AnalyticsEvent, AnalyticsData } from './analytics'

// Server-side analytics helper
export async function trackServerEvent(event: AnalyticsEvent, properties?: Record<string, any>, userId?: string) {
  if (process.env.NODE_ENV !== 'production') {
    console.log('Server Analytics (dev):', event, properties)
    return
  }

  const data: AnalyticsData = {
    event,
    properties,
    userId,
    timestamp: new Date().toISOString()
  }

  try {
    const client = await createClient()
    
    await client.from('analytics_events').insert({
      event,
      properties,
      user_id: userId,
      created_at: new Date().toISOString()
    })
  } catch (error) {
    console.error('Server analytics tracking failed:', error)
  }
}