import { auth, currentUser } from '@clerk/nextjs/server'
import { createClient, getUserRole, isInternalUser } from '@/lib/supabase/server'
import { log } from '@/lib/log'
import type { UserRole, PartnerTier, Permission } from '@/lib/rbac-constants'
import { rolePermissions } from '@/lib/rbac-constants'
export { roleLabels, tierLabels, rolePermissions, type UserRole, type PartnerTier, type Permission } from '@/lib/rbac-constants'


// Get current user's role and permissions
export async function getCurrentUserRole(): Promise<UserRole | null> {
  try {
    const authResult = await auth()
    const { userId } = authResult
    if (!userId) return null

    // Use service role ONLY for role detection to avoid chicken-and-egg problem
    const client = await createClient(true)

    // Get user email from Clerk session to check admin users
    let userEmail = authResult.sessionClaims?.email as string

    // If not in session claims, try currentUser
    if (!userEmail) {
      try {
        const user = await currentUser()
        userEmail = user?.emailAddresses?.[0]?.emailAddress || ''
      } catch (userError) {
        console.error('Error getting current user in RBAC:', userError)
      }
    }
    
    if (userEmail) {
      // Check if user is internal admin user (in internal_users table)
      const { data: adminUser } = await client
        .from('internal_users')
        .select('role')
        .eq('email', userEmail)
        .maybeSingle()

      if (adminUser) {
        console.log(`RBAC: Found admin user ${userEmail} with role ${adminUser.role}`)
        return adminUser.role as UserRole
      }
    }

    // Check if user is a partner
    const { data: profile } = await client
      .from('partners_profiles')
      .select('status')
      .eq('id', userId)
      .single()

    if (profile?.status === 'active') {
      return 'partner'
    }

    return null
  } catch (error) {
    log.error('Failed to get user role', { error })
    return null
  }
}

// Check if user has specific permission
export async function hasPermission(permission: Permission): Promise<boolean> {
  try {
    const role = await getCurrentUserRole()
    if (!role) return false

    return rolePermissions[role].includes(permission)
  } catch (error) {
    log.error('Failed to check permission', { 
      error,
      metadata: { permission }
    })
    return false
  }
}

// Check if user can access specific partner data
export async function canAccessPartner(partnerId: string): Promise<boolean> {
  try {
    const { userId } = await auth()
    if (!userId) return false

    const role = await getCurrentUserRole()
    if (!role) return false

    // Super admin can access everything
    if (role === 'super_admin') return true

    // Partners can only access their own data
    if (role === 'partner') {
      return userId === partnerId
    }

    // Sales users can access assigned partners
    if (role === 'sales') {
      const client = await createClient(true) // Use service role for role-based checks
      const { data } = await client
        .from('partner_assignments')
        .select('id')
        .eq('partner_id', partnerId)
        .eq('sales_user_id', userId)
        .eq('active', true)
        .single()

      return !!data
    }

    // Ops and accounting can access all active partners
    if (role === 'ops' || role === 'accounting') {
      const client = await createClient(true) // Use service role for role-based checks
      const { data } = await client
        .from('partners_profiles')
        .select('status')
        .eq('id', partnerId)
        .single()

      return data?.status === 'active'
    }

    return false
  } catch (error) {
    log.error('Failed to check partner access', { 
      error,
      metadata: { partnerId }
    })
    return false
  }
}

// Get user's accessible partners
export async function getAccessiblePartners(): Promise<string[]> {
  try {
    const { userId } = await auth()
    if (!userId) return []

    const role = await getCurrentUserRole()
    if (!role) return []

    const client = await createClient(true) // Use service role for role-based checks

    // Super admin can access all partners
    if (role === 'super_admin') {
      const { data } = await client
        .from('partners_profiles')
        .select('id')

      return data?.map(p => p.id) || []
    }

    // Partners can only access themselves
    if (role === 'partner') {
      return [userId]
    }

    // Sales users can access assigned partners
    if (role === 'sales') {
      const { data } = await client
        .from('partner_assignments')
        .select('partner_id')
        .eq('sales_user_id', userId)
        .eq('active', true)

      return data?.map(a => a.partner_id) || []
    }

    // Ops and accounting can access all active partners
    if (role === 'ops' || role === 'accounting') {
      const { data } = await client
        .from('partners_profiles')
        .select('id')
        .eq('status', 'active')

      return data?.map(p => p.id) || []
    }

    return []
  } catch (error) {
    log.error('Failed to get accessible partners', { error })
    return []
  }
}

// Permission guards for route handlers
export async function requirePermission(permission: Permission) {
  const hasAccess = await hasPermission(permission)
  if (!hasAccess) {
    throw new Error(`Access denied: ${permission} permission required`)
  }
}

export async function requireRole(roles: UserRole | UserRole[]) {
  const currentRole = await getCurrentUserRole()
  const allowedRoles = Array.isArray(roles) ? roles : [roles]
  
  if (!currentRole || !allowedRoles.includes(currentRole)) {
    throw new Error(`Access denied: ${allowedRoles.join(' or ')} role required`)
  }
}

export async function requirePartnerAccess(partnerId: string) {
  const hasAccess = await canAccessPartner(partnerId)
  if (!hasAccess) {
    throw new Error(`Access denied: cannot access partner ${partnerId}`)
  }
}

// Get user context for logging and audit
export async function getUserContext() {
  try {
    const { userId } = await auth()
    if (!userId) return null

    const role = await getCurrentUserRole()
    const isInternal = await isInternalUser(userId)

    return {
      userId,
      role,
      isInternal,
      userType: isInternal ? 'internal' : 'partner'
    }
  } catch (error) {
    log.error('Failed to get user context', { error })
    return null
  }
}

// Commission rate helpers
export function getCommissionRateForTier(tier: PartnerTier): number {
  const rates = {
    trusted: 0.05,   // 5%
    elite: 0.075,    // 7.5%
    diamond: 0.10    // 10%
  }
  return rates[tier]
}

export function calculateCommission(dealValue: number, tier: PartnerTier, customRate?: number): number {
  const rate = customRate || getCommissionRateForTier(tier)
  return Math.round(dealValue * rate * 100) / 100 // round to 2 decimal places
}

// Tier requirement helpers
export function getTierRequirements(tier: PartnerTier) {
  const requirements = {
    trusted: {
      min_volume: 0,
      requirements: ['Complete onboarding', 'Agree to terms'],
      benefits: ['0.5% commission', 'Basic support', 'Marketing materials']
    },
    elite: {
      min_volume: 100000,
      requirements: ['$100k+ monthly volume', 'Technical integration', 'KYB verification'],
      benefits: ['0.75% commission', 'Priority support', 'Advanced analytics']
    },
    diamond: {
      min_volume: 500000,
      requirements: ['$500k+ monthly volume', 'Dedicated relationship manager', 'Custom integration'],
      benefits: ['1% commission', '24/7 support', 'Custom features', 'Private Slack channel']
    }
  }
  return requirements[tier]
}

export function canUpgradeToTier(currentTier: PartnerTier, targetTier: PartnerTier, monthlyVolume: number): boolean {
  if (currentTier === targetTier) return false
  
  const requirements = getTierRequirements(targetTier)
  return monthlyVolume >= requirements.min_volume
}

// Validation middleware for API routes
export function withPermission(permission: Permission) {
  return async (handler: Function) => {
    await requirePermission(permission)
    return handler
  }
}

export function withRole(roles: UserRole | UserRole[]) {
  return async (handler: Function) => {
    await requireRole(roles)
    return handler
  }
}

export function withPartnerAccess(getPartnerId: (req: any) => string) {
  return async (handler: Function, req: any) => {
    const partnerId = getPartnerId(req)
    await requirePartnerAccess(partnerId)
    return handler
  }
}