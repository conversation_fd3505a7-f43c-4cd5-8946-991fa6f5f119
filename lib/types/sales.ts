export interface SalesPerson {
  id: string
  name: string
  email: string
  referralSlug: string
  telegramHandle?: string
  telegramLink?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface SalesReferralLink {
  id: string
  salesId: string
  slug: string
  active: boolean
  clicks: number
  createdAt: string
  updatedAt: string
}

export interface SalesPersonWithReferralStats extends SalesPerson {
  referralLinks: Array<{
    id: string
    slug: string
    clicks: number
    active: boolean
  }>
  totalClicks: number
  totalConversions: number
  activeLinks: number
}

export interface SalesReferralAttribution {
  id: string
  sessionId: string
  salesId?: string
  salesReferralSlug?: string
  partnerId?: string
  referralSlug?: string
  attributionType: 'partner' | 'sales' | 'direct'
  firstVisitAt?: string
  lastVisitAt?: string
  converted: boolean
  convertedAt?: string
  leadId?: string
  utmSource?: string
  utmMedium?: string
  utmCampaign?: string
  utmTerm?: string
  utmContent?: string
  userAgent?: string
  ipAddress?: string
}

export interface SalesStats {
  totalClicks: number
  totalConversions: number
  conversionRate: number
  topPerformers: Array<{
    salesPersonId: string
    salesPersonName: string
    clicks: number
    conversions: number
    conversionRate: number
  }>
  attributionBreakdown: {
    partner: number
    sales: number
    direct: number
  }
  periodComparison?: {
    clicksChange: number
    conversionsChange: number
    conversionRateChange: number
  }
}

export interface CreateSalesPersonData {
  name: string
  email: string
  telegramHandle?: string
  telegramLink?: string
}

export interface UpdateSalesPersonData extends Partial<CreateSalesPersonData> {
  id: string
  isActive?: boolean
}

export interface SalesPersonFormData {
  name: string
  email: string
  telegramHandle: string
  telegramLink: string
  generateReferralSlug: boolean
  customReferralSlug?: string
}

// Public API types for frontend consumption
export interface PublicSalesPersonInfo {
  id: string
  name: string
  telegramHandle?: string
  emailPrefix: string // Only the part before @
}

export interface SalesReferralLinkInfo {
  id: string
  slug: string
  clicks: number
  salesPersonName: string
  telegramHandle?: string
  telegramLink?: string
  type: 'sales'
}

// Form validation types
export interface SalesPersonValidation {
  name: {
    required: boolean
    minLength: number
    maxLength: number
  }
  email: {
    required: boolean
    pattern: RegExp
    unique: boolean
  }
  telegramHandle: {
    required: boolean
    pattern: RegExp
    maxLength: number
  }
  referralSlug: {
    required: boolean
    pattern: RegExp
    unique: boolean
    maxLength: number
  }
}

export const SALES_VALIDATION_RULES: SalesPersonValidation = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 50
  },
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    unique: true
  },
  telegramHandle: {
    required: false,
    pattern: /^@?[a-zA-Z0-9_]{5,32}$/,
    maxLength: 33 // Including @ symbol
  },
  referralSlug: {
    required: true,
    pattern: /^[a-z0-9-]+$/,
    unique: true,
    maxLength: 50
  }
}

// API Response types
export interface SalesPersonResponse {
  success: boolean
  data?: SalesPerson
  error?: string
}

export interface SalesPersonListResponse {
  success: boolean
  data?: PublicSalesPersonInfo[]
  error?: string
}

export interface SalesStatsResponse {
  success: boolean
  data?: SalesStats
  error?: string
}

// Attribution cookie types (for client-side)
export interface SalesAttributionCookie {
  type: 'sales'
  slug: string
  salesPersonName: string
  salesPersonId: string
  timestamp: number
  visitId: string
}

export interface PartnerAttributionCookie {
  type: 'partner'
  slug: string
  partnerName: string
  partnerId: string
  timestamp: number
  visitId: string
  tier?: string
  companyName?: string
}

export type AttributionCookie = SalesAttributionCookie | PartnerAttributionCookie

// Dashboard types
export interface SalesDashboardData {
  personalStats: {
    totalClicks: number
    totalConversions: number
    conversionRate: number
    monthlyClicks: number
    monthlyConversions: number
  }
  referralLinks: Array<{
    slug: string
    clicks: number
    conversions: number
    isActive: boolean
    createdAt: string
  }>
  recentActivity: Array<{
    type: 'click' | 'conversion'
    timestamp: string
    slug: string
    metadata?: Record<string, any>
  }>
  assignedPartners: Array<{
    id: string
    name: string
    company?: string
    tier: string
    status: string
    totalDeals: number
    totalCommission: number
  }>
}

// Admin types
export interface AdminSalesManagement {
  salesPeople: SalesPersonWithReferralStats[]
  overallStats: SalesStats
  recentAssignments: Array<{
    partnerName: string
    salesPersonName: string
    assignedAt: string
    assignedBy: string
  }>
}