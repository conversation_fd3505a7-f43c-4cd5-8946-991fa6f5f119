'use client'

import { jsPDF } from 'jspdf'
import { format } from 'date-fns'

export interface InvoiceData {
  invoiceNumber: string
  partner: {
    name: string
    company: string
    email: string
    billing_address: {
      street1: string
      street2?: string
      city: string
      state: string
      postal_code: string
      country: string
      tax_id?: string
    }
  }
  deals: Array<{
    id: string
    client_company: string
    deal_value: number
    commission_rate: number
    commission_amount: number
    date: string
    description?: string
  }>
  paymentMethod: {
    type: 'bank' | 'crypto'
    bankDetails?: {
      accountName: string
      accountNumber: string
      routingNumber: string
      bankName: string
      swiftCode?: string
    }
    cryptoDetails?: {
      walletAddress: string
      currency: string
      network?: string
    }
  }
  totals: {
    subtotal: number
    fees?: number
    total: number
  }
  notes?: string
}

// Utility to encrypt invoice data for reuse
export function encryptInvoiceData(data: InvoiceData, passphrase: string): string {
  // Simple XOR encryption for demo purposes
  // In production, use proper encryption
  const jsonString = JSON.stringify(data)
  let encrypted = ''
  
  for (let i = 0; i < jsonString.length; i++) {
    const keyChar = passphrase[i % passphrase.length]
    encrypted += String.fromCharCode(jsonString.charCodeAt(i) ^ keyChar.charCodeAt(0))
  }
  
  return btoa(encrypted)
}

export function decryptInvoiceData(encryptedData: string, passphrase: string): InvoiceData {
  try {
    const encrypted = atob(encryptedData)
    let decrypted = ''
    
    for (let i = 0; i < encrypted.length; i++) {
      const keyChar = passphrase[i % passphrase.length]
      decrypted += String.fromCharCode(encrypted.charCodeAt(i) ^ keyChar.charCodeAt(0))
    }
    
    return JSON.parse(decrypted)
  } catch (error) {
    throw new Error('Invalid passphrase or corrupted data')
  }
}

export function generateInvoicePDF(data: InvoiceData): jsPDF {
  const doc = new jsPDF()
  
  // Header
  doc.setFontSize(24)
  doc.setFont('helvetica', 'bold')
  doc.text('COMMISSION INVOICE', 20, 30)
  
  // Invoice details
  doc.setFontSize(12)
  doc.setFont('helvetica', 'normal')
  doc.text(`Invoice #: ${data.invoiceNumber}`, 20, 50)
  doc.text(`Date: ${format(new Date(), 'MMMM dd, yyyy')}`, 20, 60)
  
  // From (IBC)
  doc.setFont('helvetica', 'bold')
  doc.text('From:', 20, 80)
  doc.setFont('helvetica', 'normal')
  doc.text('IBC Ventures LTD', 20, 90)
  doc.text('International Blockchain Consulting', 20, 100)
  doc.text('Email: <EMAIL>', 20, 110)
  
  // To (Partner)
  doc.setFont('helvetica', 'bold')
  doc.text('To:', 120, 80)
  doc.setFont('helvetica', 'normal')
  doc.text(data.partner.name, 120, 90)
  doc.text(data.partner.company, 120, 100)
  doc.text(data.partner.email, 120, 110)
  
  // Billing address
  const billing = data.partner.billing_address
  let yPos = 120
  doc.text(`${billing.street1}`, 120, yPos)
  if (billing.street2) {
    yPos += 10
    doc.text(billing.street2, 120, yPos)
  }
  yPos += 10
  doc.text(`${billing.city}, ${billing.state} ${billing.postal_code}`, 120, yPos)
  yPos += 10
  doc.text(billing.country, 120, yPos)
  
  if (billing.tax_id) {
    yPos += 10
    doc.text(`Tax ID: ${billing.tax_id}`, 120, yPos)
  }
  
  // Commission Details Table
  const startY = Math.max(yPos + 20, 160)
  
  doc.setFont('helvetica', 'bold')
  doc.text('Commission Details', 20, startY)
  
  // Table headers
  const tableY = startY + 15
  doc.setFont('helvetica', 'bold')
  doc.text('Deal', 20, tableY)
  doc.text('Client', 60, tableY)
  doc.text('Deal Value', 100, tableY)
  doc.text('Rate', 130, tableY)
  doc.text('Commission', 155, tableY)
  
  // Draw header line
  doc.line(20, tableY + 2, 190, tableY + 2)
  
  // Table rows
  let currentY = tableY + 10
  doc.setFont('helvetica', 'normal')
  
  data.deals.forEach((deal, index) => {
    if (currentY > 250) {
      doc.addPage()
      currentY = 30
    }
    
    doc.text(deal.id.substring(0, 8) + '...', 20, currentY)
    doc.text(deal.client_company.substring(0, 15), 60, currentY)
    doc.text(`$${deal.deal_value.toLocaleString()}`, 100, currentY)
    doc.text(`${(deal.commission_rate * 100).toFixed(1)}%`, 130, currentY)
    doc.text(`$${deal.commission_amount.toLocaleString()}`, 155, currentY)
    
    currentY += 10
  })
  
  // Totals
  currentY += 10
  doc.line(130, currentY - 5, 190, currentY - 5)
  
  doc.setFont('helvetica', 'bold')
  doc.text('Subtotal:', 130, currentY)
  doc.text(`$${data.totals.subtotal.toLocaleString()}`, 155, currentY)
  
  if (data.totals.fees) {
    currentY += 10
    doc.text('Processing Fees:', 130, currentY)
    doc.text(`$${data.totals.fees.toLocaleString()}`, 155, currentY)
  }
  
  currentY += 10
  doc.setFontSize(14)
  doc.text('Total:', 130, currentY)
  doc.text(`$${data.totals.total.toLocaleString()}`, 155, currentY)
  
  // Payment Details
  currentY += 20
  doc.setFontSize(12)
  doc.setFont('helvetica', 'bold')
  doc.text('Payment Details:', 20, currentY)
  
  currentY += 10
  doc.setFont('helvetica', 'normal')
  
  if (data.paymentMethod.type === 'bank' && data.paymentMethod.bankDetails) {
    const bank = data.paymentMethod.bankDetails
    doc.text(`Account Name: ${bank.accountName}`, 20, currentY)
    currentY += 10
    doc.text(`Account Number: ${bank.accountNumber}`, 20, currentY)
    currentY += 10
    doc.text(`Routing Number: ${bank.routingNumber}`, 20, currentY)
    currentY += 10
    doc.text(`Bank Name: ${bank.bankName}`, 20, currentY)
    if (bank.swiftCode) {
      currentY += 10
      doc.text(`SWIFT Code: ${bank.swiftCode}`, 20, currentY)
    }
  } else if (data.paymentMethod.type === 'crypto' && data.paymentMethod.cryptoDetails) {
    const crypto = data.paymentMethod.cryptoDetails
    doc.text(`Currency: ${crypto.currency}`, 20, currentY)
    currentY += 10
    if (crypto.network) {
      doc.text(`Network: ${crypto.network}`, 20, currentY)
      currentY += 10
    }
    doc.text(`Wallet Address:`, 20, currentY)
    currentY += 10
    doc.setFont('courier', 'normal')
    doc.setFontSize(10)
    doc.text(crypto.walletAddress, 20, currentY)
    doc.setFont('helvetica', 'normal')
    doc.setFontSize(12)
  }
  
  // Notes
  if (data.notes) {
    currentY += 20
    if (currentY > 250) {
      doc.addPage()
      currentY = 30
    }
    
    doc.setFont('helvetica', 'bold')
    doc.text('Notes:', 20, currentY)
    currentY += 10
    doc.setFont('helvetica', 'normal')
    const splitNotes = doc.splitTextToSize(data.notes, 170)
    doc.text(splitNotes, 20, currentY)
  }
  
  // Footer
  const pageHeight = doc.internal.pageSize.height
  doc.setFontSize(10)
  doc.setFont('helvetica', 'italic')
  doc.text('Generated via IBC Partner Portal - No data stored', 20, pageHeight - 20)
  doc.text(`Generated on ${format(new Date(), 'yyyy-MM-dd HH:mm:ss')}`, 20, pageHeight - 10)
  
  return doc
}

export function downloadInvoicePDF(data: InvoiceData, filename?: string) {
  const doc = generateInvoicePDF(data)
  const defaultFilename = `invoice_${data.invoiceNumber}_${format(new Date(), 'yyyy-MM-dd')}.pdf`
  doc.save(filename || defaultFilename)
}

export function getInvoicePreviewURL(data: InvoiceData): string {
  const doc = generateInvoicePDF(data)
  return doc.output('datauristring')
}

// Validation helpers
export function validateInvoiceData(data: Partial<InvoiceData>): string[] {
  const errors: string[] = []
  
  if (!data.partner?.name) errors.push('Partner name is required')
  if (!data.partner?.company) errors.push('Partner company is required')
  if (!data.partner?.email) errors.push('Partner email is required')
  if (!data.partner?.billing_address?.street1) errors.push('Billing address is required')
  if (!data.partner?.billing_address?.city) errors.push('City is required')
  if (!data.partner?.billing_address?.state) errors.push('State is required')
  if (!data.partner?.billing_address?.country) errors.push('Country is required')
  
  if (!data.deals || data.deals.length === 0) errors.push('At least one deal is required')
  if (!data.paymentMethod?.type) errors.push('Payment method is required')
  
  if (data.paymentMethod?.type === 'bank' && !data.paymentMethod.bankDetails?.accountName) {
    errors.push('Bank account details are required')
  }
  
  if (data.paymentMethod?.type === 'crypto' && !data.paymentMethod.cryptoDetails?.walletAddress) {
    errors.push('Crypto wallet address is required')
  }
  
  return errors
}