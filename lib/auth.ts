import { auth, currentUser } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { log } from '@/lib/log'

export interface ClerkUser {
  id: string
  email: string
  firstName: string | null
  lastName: string | null
  fullName: string
}

export async function requireAuth() {
  const { userId } = await auth()
  
  if (!userId) {
    log.authEvent('auth_required_redirect', 'anonymous')
    redirect('/sign-in')
  }
  
  return { userId }
}

export async function getCurrentUser(): Promise<ClerkUser> {
  const user = await currentUser()
  
  if (!user) {
    log.authEvent('current_user_not_found', 'anonymous')
    redirect('/sign-in')
  }

  const userData = extractUserData(user)
  log.debug('Current user retrieved', { 
    userId: userData.id, 
    metadata: { 
      email: userData.email 
    } 
  })
  
  return userData
}

export function extractUserData(user: any): ClerkUser {
  return {
    id: user.id,
    email: user.primaryEmailAddress?.emailAddress || '',
    firstName: user.firstName,
    lastName: user.lastName,
    fullName: `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Unknown User',
  }
}

// Get user ID from server context (for API routes)
export async function getAuthUserId(): Promise<string | null> {
  try {
    const { userId } = await auth()
    return userId
  } catch (error) {
    log.error('Failed to get auth user ID', { error })
    return null
  }
}

// Check if user has completed onboarding (to be implemented when we have Supabase)
export async function hasCompletedOnboarding(userId: string): Promise<boolean> {
  // TODO: Implement when Supabase is set up
  // Check if user has profile with required fields completed
  return true // Temporary - will check profile completeness
}

// Get user's partner profile (to be implemented when we have Supabase)
export async function getPartnerProfile(userId: string) {
  // TODO: Implement when Supabase is set up
  // Query profiles table for user's partner information
  return null
}

// Check if user is internal staff (to be implemented when we have Supabase)
export async function isInternalUser(userId: string): Promise<boolean> {
  // TODO: Implement when Supabase is set up
  // Check if user exists in users table (internal staff)
  return false
}

// Get user's role if they're internal staff
export async function getUserRole(userId: string): Promise<string | null> {
  // TODO: Implement when Supabase is set up
  // Query users table for role
  return null
}