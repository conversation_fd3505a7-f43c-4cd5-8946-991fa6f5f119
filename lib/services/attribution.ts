import { createClient } from '@/lib/supabase/server'
import { log } from '@/lib/log'

export type AttributionType = 'partner' | 'sales' | 'direct'

export interface PartnerAttribution {
  type: 'partner'
  partnerId: string
  slug: string
  partnerName: string
  companyName?: string
  tier: string
  timestamp: number
  visitId: string
}

export interface SalesAttribution {
  type: 'sales'
  salesId: string
  slug: string
  salesPersonName: string
  telegramHandle?: string
  timestamp: number
  visitId: string
}

export type Attribution = PartnerAttribution | SalesAttribution

export interface AttributionCookies {
  partner?: PartnerAttribution
  sales?: SalesAttribution
}

/**
 * Parse attribution cookies from request headers
 */
export function parseAttributionCookies(cookieHeader?: string): AttributionCookies {
  const result: AttributionCookies = {}
  
  if (!cookieHeader) {
    return result
  }

  const cookies = Object.fromEntries(
    cookieHeader.split(';').map(cookie => {
      const [name, value] = cookie.trim().split('=')
      return [name, decodeURIComponent(value || '')]
    })
  )

  // Parse partner attribution
  if (cookies.ibc_partner_ref) {
    try {
      const partnerData = JSON.parse(cookies.ibc_partner_ref)
      if (partnerData && typeof partnerData === 'object') {
        result.partner = {
          type: 'partner',
          ...partnerData
        } as PartnerAttribution
      }
    } catch (error) {
      log.warn('Failed to parse partner attribution cookie', { error })
    }
  }

  // Parse sales attribution
  if (cookies.ibc_sales_ref) {
    try {
      const salesData = JSON.parse(cookies.ibc_sales_ref)
      if (salesData && typeof salesData === 'object') {
        result.sales = {
          type: 'sales',
          ...salesData
        } as SalesAttribution
      }
    } catch (error) {
      log.warn('Failed to parse sales attribution cookie', { error })
    }
  }

  return result
}

/**
 * Determine the primary attribution based on priority rules:
 * 1. Partner referral (direct partner links have priority)
 * 2. Sales referral
 * 3. Direct (no attribution)
 */
export function getPrimaryAttribution(cookies: AttributionCookies): Attribution | null {
  // Partner attribution takes priority
  if (cookies.partner) {
    return cookies.partner
  }

  // Sales attribution is secondary
  if (cookies.sales) {
    return cookies.sales
  }

  return null
}

/**
 * Track referral attribution in the database
 */
export async function trackAttribution(
  sessionId: string,
  attribution: Attribution | null,
  metadata: {
    userAgent?: string
    ipAddress?: string
    utmSource?: string
    utmMedium?: string
    utmCampaign?: string
    utmTerm?: string
    utmContent?: string
  } = {}
): Promise<void> {
  try {
    const client = await createClient(true) // Use service role

    if (!attribution) {
      // Track direct visit
      await client
        .from('partners_referral_attribution')
        .insert({
          session_id: sessionId,
          attribution_type: 'direct',
          first_visit_at: new Date().toISOString(),
          last_visit_at: new Date().toISOString(),
          user_agent: metadata.userAgent,
          ip_address: metadata.ipAddress,
          utm_source: metadata.utmSource,
          utm_medium: metadata.utmMedium,
          utm_campaign: metadata.utmCampaign,
          utm_term: metadata.utmTerm,
          utm_content: metadata.utmContent,
        })
      return
    }

    // Check if session already exists
    const { data: existingSession } = await client
      .from('partners_referral_attribution')
      .select('id, first_visit_at')
      .eq('session_id', sessionId)
      .single()

    const now = new Date().toISOString()
    const attributionData = {
      session_id: sessionId,
      attribution_type: attribution.type,
      user_agent: metadata.userAgent,
      ip_address: metadata.ipAddress,
      utm_source: metadata.utmSource,
      utm_medium: metadata.utmMedium,
      utm_campaign: metadata.utmCampaign,
      utm_term: metadata.utmTerm,
      utm_content: metadata.utmContent,
      last_visit_at: now,
      ...(attribution.type === 'partner' && {
        partner_id: attribution.partnerId,
        referral_slug: attribution.slug,
      }),
      ...(attribution.type === 'sales' && {
        sales_id: attribution.salesId,
        sales_referral_slug: attribution.slug,
      }),
    }

    if (existingSession) {
      // Update existing session
      await client
        .from('partners_referral_attribution')
        .update(attributionData)
        .eq('id', existingSession.id)
    } else {
      // Create new session
      await client
        .from('partners_referral_attribution')
        .insert({
          ...attributionData,
          first_visit_at: now,
        })
    }

    log.info('Attribution tracked', { 
      metadata: {
        sessionId, 
        type: attribution.type, 
        slug: attribution.slug
      }
    })

  } catch (error) {
    log.error('Failed to track attribution', { 
      error,
      metadata: {
        sessionId, 
        attribution: attribution?.type
      }
    })
  }
}

/**
 * Mark attribution as converted when a lead is created
 */
export async function markAttributionConverted(
  sessionId: string,
  leadId: string
): Promise<void> {
  try {
    const client = await createClient(true) // Use service role

    await client
      .from('partners_referral_attribution')
      .update({
        converted: true,
        converted_at: new Date().toISOString(),
        lead_id: leadId,
      })
      .eq('session_id', sessionId)

    log.info('Attribution marked as converted', { 
      metadata: { sessionId, leadId }
    })

  } catch (error) {
    log.error('Failed to mark attribution as converted', { 
      error,
      metadata: {
        sessionId, 
        leadId
      }
    })
  }
}

/**
 * Get attribution stats for reporting
 */
export async function getAttributionStats(
  startDate?: string,
  endDate?: string
) {
  try {
    const client = await createClient(true) // Use service role

    let query = client
      .from('partners_referral_attribution')
      .select(`
        attribution_type,
        converted,
        created_at,
        partners_profiles(full_name, company_name),
        partners_sales(name)
      `)

    if (startDate) {
      query = query.gte('created_at', startDate)
    }

    if (endDate) {
      query = query.lte('created_at', endDate)
    }

    const { data } = await query

    const stats = {
      total: data?.length || 0,
      byType: {
        partner: data?.filter(d => d.attribution_type === 'partner').length || 0,
        sales: data?.filter(d => d.attribution_type === 'sales').length || 0,
        direct: data?.filter(d => d.attribution_type === 'direct').length || 0,
      },
      conversions: {
        total: data?.filter(d => d.converted).length || 0,
        partner: data?.filter(d => d.converted && d.attribution_type === 'partner').length || 0,
        sales: data?.filter(d => d.converted && d.attribution_type === 'sales').length || 0,
        direct: data?.filter(d => d.converted && d.attribution_type === 'direct').length || 0,
      }
    }

    return stats

  } catch (error) {
    log.error('Failed to get attribution stats', { error })
    return null
  }
}