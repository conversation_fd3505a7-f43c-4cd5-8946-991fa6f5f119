import { z } from 'zod'

// Crypto wallet schema
export const cryptoWalletSchema = z.object({
  wallet_address: z.string().min(26, 'Invalid wallet address'),
  wallet_type: z.enum(['bitcoin', 'ethereum', 'usdt', 'usdc']),
  network: z.string().optional() // e.g., 'mainnet', 'polygon', 'bsc'
})

// Bank transfer schema
export const bankTransferSchema = z.object({
  account_holder_name: z.string().min(2, 'Account holder name is required'),
  bank_name: z.string().min(2, 'Bank name is required'),
  account_number: z.string().min(4, 'Account number is required'),
  routing_number: z.string().optional(),
  swift_code: z.string().optional(),
  iban: z.string().optional(),
  bank_address: z.object({
    street: z.string(),
    city: z.string(),
    country: z.string().length(2, 'Country must be 2-letter code')
  }).optional()
})

// PayPal schema
export const paypalSchema = z.object({
  email: z.string().email('Invalid PayPal email address')
})

// Wise schema
export const wiseSchema = z.object({
  email: z.string().email('Invalid Wise email address'),
  currency: z.string().length(3, 'Currency must be 3-letter code').default('USD')
})

// Withdrawal method schema
export const withdrawalMethodSchema = z.object({
  method_type: z.enum(['crypto', 'bank_transfer', 'paypal', 'wise']),
  method_details: z.union([
    cryptoWalletSchema,
    bankTransferSchema,
    paypalSchema,
    wiseSchema
  ]),
  is_default: z.boolean().default(false)
}).refine(data => {
  // Validate method_details matches method_type
  switch (data.method_type) {
    case 'crypto':
      return cryptoWalletSchema.safeParse(data.method_details).success
    case 'bank_transfer':
      return bankTransferSchema.safeParse(data.method_details).success
    case 'paypal':
      return paypalSchema.safeParse(data.method_details).success
    case 'wise':
      return wiseSchema.safeParse(data.method_details).success
    default:
      return false
  }
}, 'Method details must match the selected method type')

// Withdrawal request schema
export const withdrawalRequestSchema = z.object({
  withdrawal_method_id: z.string().uuid('Invalid withdrawal method ID'),
  amount: z.number().min(1, 'Withdrawal amount must be at least $1'),
  currency: z.string().length(3, 'Currency must be 3-letter code').default('USD'),
  deal_ids: z.array(z.string().uuid()).optional() // specific deals to withdraw from
}).refine(data => {
  // Validate minimum withdrawal amount (will be checked against system settings)
  return data.amount >= 10 // basic validation, real validation happens server-side
}, 'Amount below minimum withdrawal threshold')

// Withdrawal processing schema (admin)
export const withdrawalProcessingSchema = z.object({
  status: z.enum(['processing', 'completed', 'failed', 'cancelled']),
  transaction_hash: z.string().optional(),
  bank_reference: z.string().optional(),
  processor_reference: z.string().optional(),
  fee_amount: z.number().min(0).optional(),
  notes: z.string().max(500, 'Notes must be less than 500 characters').optional()
})

// Withdrawal filter schema
export const withdrawalFilterSchema = z.object({
  partner_id: z.string().uuid().optional(),
  status: z.enum(['pending', 'processing', 'completed', 'failed', 'cancelled']).optional(),
  method_type: z.enum(['crypto', 'bank_transfer', 'paypal', 'wise']).optional(),
  min_amount: z.number().min(0).optional(),
  max_amount: z.number().min(0).optional(),
  date_from: z.string().datetime().optional(),
  date_to: z.string().datetime().optional(),
  search: z.string().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20)
})

// Bulk withdrawal processing schema
export const bulkWithdrawalProcessingSchema = z.object({
  withdrawal_ids: z.array(z.string().uuid()).min(1, 'At least one withdrawal must be selected'),
  action: z.enum(['approve', 'reject', 'mark_processing']),
  notes: z.string().max(500, 'Notes must be less than 500 characters').optional()
})

// KYC verification schema
export const kycVerificationSchema = z.object({
  document_type: z.enum(['passport', 'drivers_license', 'national_id', 'business_registration']),
  document_number: z.string().min(1, 'Document number is required'),
  document_url: z.string().url('Invalid document URL'),
  verification_notes: z.string().max(500).optional()
})

// Withdrawal fee calculation schema
export const feeCalculationSchema = z.object({
  amount: z.number().min(0.01),
  method_type: z.enum(['crypto', 'bank_transfer', 'paypal', 'wise']),
  currency: z.string().length(3).default('USD')
})

// Type exports
export type CryptoWallet = z.infer<typeof cryptoWalletSchema>
export type BankTransfer = z.infer<typeof bankTransferSchema>
export type PayPal = z.infer<typeof paypalSchema>
export type Wise = z.infer<typeof wiseSchema>
export type WithdrawalMethod = z.infer<typeof withdrawalMethodSchema>
export type WithdrawalRequest = z.infer<typeof withdrawalRequestSchema>
export type WithdrawalProcessing = z.infer<typeof withdrawalProcessingSchema>
export type WithdrawalFilter = z.infer<typeof withdrawalFilterSchema>
export type BulkWithdrawalProcessing = z.infer<typeof bulkWithdrawalProcessingSchema>
export type KycVerification = z.infer<typeof kycVerificationSchema>
export type FeeCalculation = z.infer<typeof feeCalculationSchema>