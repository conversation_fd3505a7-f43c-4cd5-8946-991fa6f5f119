import { z } from 'zod'

// Company type options based on PRD
const companyTypes = [
  'vc', // Venture Capital (VC)
  'cex', // Central Exchange (CEX)
  'dex', // Decentralized Exchange (DEX)
  'launchpad', // Launchpad
  'marketing_agency', // Marketing Agency
  'incubator_accelerator', // Incubator/Accelerator
  'market_making', // Market Making (MM)
  'development', // Development
  'technology', // Technology
  'research', // Research
  'data_aggregation', // Data and aggregation platform
  'external_bd', // External BD
  'deal_flow_individual', // Deal Flow Individual
  'angel_investor', // Angel Investor
  'other' // Others (Specify)
] as const

// Communication preferences
const communicationMethods = ['whatsapp', 'email', 'telegram'] as const

// Partner roles
const partnerRoles = [
  'ceo', 'cto', 'cmo', 'bd', 'founder', 'co_founder', 
  'partner', 'director', 'manager', 'other'
] as const

// Base profile schema
export const profileSchema = z.object({
  email: z.string().email('Invalid email address'),
  full_name: z.string().min(2, 'Full name must be at least 2 characters').max(50, 'Full name must be less than 50 characters').optional(),
  company_name: z.string().min(2, 'Company name must be at least 2 characters').max(50, 'Company name must be less than 50 characters').optional(),
  company_type: z.enum(companyTypes).optional(),
  company_type_other: z.string().max(100, 'Specification must be less than 100 characters').optional(),
  role: z.string().max(50, 'Role must be less than 50 characters').optional(),
  tier: z.enum(['trusted', 'elite', 'diamond']).default('trusted'),
  status: z.enum(['active', 'pending', 'suspended']).default('pending'),
  
  // Communication preferences (multi-select)
  preferred_communication: z.array(z.enum(communicationMethods)).min(1, 'Please select at least one communication method'),
  telegram: z.string().regex(/^@[\w\d_]+$/, 'Invalid Telegram handle format').max(32, 'Telegram handle too long').optional(),
  whatsapp: z.string().regex(/^\+[\d\s\-\(\)]+$/, 'Invalid WhatsApp number format').max(20, 'WhatsApp number too long').optional(),
  x_profile: z.string().url('Invalid X profile URL').max(200, 'X profile URL too long').optional(),
  
  // ID document for agreement (file upload would be handled separately)
  identification_type: z.enum(['passport', 'government_id']).optional(),
  identification_verified: z.boolean().default(false),
  
  referral_slug: z.string().regex(/^[a-z0-9\-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens').optional(),
  internal_poc: z.string().optional(),
  terms_version: z.string().optional(),
  terms_accepted_at: z.string().datetime().optional()
})


// Payment details schema
export const paymentDetailsSchema = z.object({
  preferred_method: z.enum(['bank_transfer', 'usdt']),
  // Bank details (collected later in withdrawal process)
  bank_details_submitted: z.boolean().default(false),
  // Wallet details (collected later in withdrawal process)
  wallet_details_submitted: z.boolean().default(false)
})


// Onboarding form schema (simplified based on user requirements)
export const onboardingSchema = z.object({
  // Personal Info (required)
  full_name: z.string().min(2, 'Full name must be at least 2 characters').max(50, 'Full name must be less than 50 characters'),
  
  // Communication preferences (required - multi-select)
  preferred_communication: z.array(z.enum(communicationMethods)).min(1, 'Please select at least one communication method'),
  
  // Contact details (conditional based on communication preferences)
  telegram: z.string().regex(/^@[\w\d_]+$/, 'Invalid Telegram handle format').max(32, 'Telegram handle too long').optional(),
  whatsapp: z.string().regex(/^\+[\d\s\-\(\)]+$/, 'Invalid WhatsApp number format').max(20, 'WhatsApp number too long').optional(),
  
  // Company Info (required)
  company_name: z.string().min(2, 'Company name must be at least 2 characters').max(50, 'Company name must be less than 50 characters'),
  company_type: z.enum(companyTypes),
  company_type_other: z.string().max(100, 'Specification must be less than 100 characters').optional(),
  x_profile: z.string().url('Invalid X profile URL').max(200, 'X profile URL too long').optional(),
  
  // Partner Role (required)
  role: z.enum(partnerRoles),
  role_other: z.string().max(50, 'Role specification must be less than 50 characters').optional(),
  
  // Internal POC (required - would be populated from ops list)
  internal_poc: z.string().min(1, 'Internal point of contact is required'),
  
  // Payment Details (simplified - just preference, no billing address)
  preferred_payment_method: z.enum(['bank_transfer', 'usdt']),
  
  // Terms & Conditions (required)
  terms_accepted: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
  privacy_accepted: z.boolean().refine(val => val === true, 'You must accept the privacy policy'),
  terms_version: z.string().default('1.0')
})
.refine((data) => {
  // If company type is "other", company_type_other must be provided
  if (data.company_type === 'other' && !data.company_type_other) {
    return false
  }
  return true
}, {
  message: 'Please specify the company type',
  path: ['company_type_other']
})
.refine((data) => {
  // If role is "other", role_other must be provided
  if (data.role === 'other' && !data.role_other) {
    return false
  }
  return true
}, {
  message: 'Please specify your role',
  path: ['role_other']
})
.refine((data) => {
  // If WhatsApp is selected as communication method, WhatsApp number must be provided
  if (data.preferred_communication.includes('whatsapp') && !data.whatsapp) {
    return false
  }
  return true
}, {
  message: 'WhatsApp number is required when selected as communication method',
  path: ['whatsapp']
})
.refine((data) => {
  // If Telegram is selected as communication method, Telegram handle must be provided
  if (data.preferred_communication.includes('telegram') && !data.telegram) {
    return false
  }
  return true
}, {
  message: 'Telegram handle is required when selected as communication method',
  path: ['telegram']
})

// Profile update schema (all fields optional except email)
export const profileUpdateSchema = profileSchema.partial().extend({
  email: z.string().email('Invalid email address')
})

// Admin profile update schema (includes status changes)
export const adminProfileUpdateSchema = profileSchema.extend({
  status: z.enum(['active', 'pending', 'suspended']),
  tier: z.enum(['trusted', 'elite', 'diamond']),
  internal_poc: z.string().optional()
})

// Referral slug generation schema
export const referralSlugSchema = z.object({
  preferred_slug: z.string()
    .min(3, 'Slug must be at least 3 characters')
    .max(20, 'Slug must be less than 20 characters')
    .regex(/^[a-z0-9\-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens')
    .refine(slug => !slug.startsWith('-') && !slug.endsWith('-'), 'Slug cannot start or end with hyphens')
})

// Type exports
export type Profile = z.infer<typeof profileSchema>
export type PaymentDetails = z.infer<typeof paymentDetailsSchema>
export type OnboardingForm = z.infer<typeof onboardingSchema>
export type ProfileUpdate = z.infer<typeof profileUpdateSchema>
export type AdminProfileUpdate = z.infer<typeof adminProfileUpdateSchema>
export type ReferralSlugForm = z.infer<typeof referralSlugSchema>

// Enum exports for form components
export type CompanyType = typeof companyTypes[number]
export type CommunicationMethod = typeof communicationMethods[number]
export type PartnerRole = typeof partnerRoles[number]

// Export constants for form components
export { companyTypes, communicationMethods, partnerRoles }