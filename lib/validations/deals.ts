import { z } from 'zod'
import { projectPocSchema } from './leads'

// Deal creation schema
export const dealCreationSchema = z.object({
  lead_id: z.string().optional(), // Changed from uuid() to string() to support Clerk IDs
  partner_id: z.string().min(1, 'Invalid partner ID'), // Changed from uuid() to string() to support Clerk IDs
  client_company: z.string().min(2, 'Client company name must be at least 2 characters'),
  client_poc: projectPocSchema,
  deal_value: z.number().min(0.01, 'Deal value must be greater than 0'),
  commission_rate: z.number().min(0).max(1, 'Commission rate must be between 0 and 1'),
  invoice_required: z.boolean().default(false),
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional()
}).refine(data => {
  // Calculate commission amount
  const commission_amount = data.deal_value * data.commission_rate
  return commission_amount >= 0
}, 'Invalid commission calculation')

// Deal update schema
export const dealUpdateSchema = z.object({
  client_company: z.string().min(2, 'Client company name must be at least 2 characters').optional(),
  client_poc: projectPocSchema.optional(),
  deal_value: z.number().min(0.01, 'Deal value must be greater than 0').optional(),
  commission_rate: z.number().min(0).max(1, 'Commission rate must be between 0 and 1').optional(),
  status: z.enum(['pending', 'confirmed', 'paid', 'disputed']).optional(),
  invoice_required: z.boolean().optional(),
  invoice_submitted: z.boolean().optional(),
  payment_date: z.string().datetime().optional(),
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional()
})

// Deal milestone schema
export const dealMilestoneSchema = z.object({
  deal_id: z.string().uuid('Invalid deal ID'),
  milestone_type: z.enum(['lead_submitted', 'deal_confirmed', 'payment_processed', 'commission_paid']),
  amount: z.number().min(0).optional(),
  notes: z.string().max(500, 'Notes must be less than 500 characters').optional()
})

// Deal filter schema
export const dealFilterSchema = z.object({
  partner_id: z.string().optional(), // Changed from uuid() to string() to support Clerk IDs
  status: z.enum(['pending', 'confirmed', 'paid', 'disputed']).optional(),
  min_value: z.coerce.number().min(0).optional(), // Added coerce for URL params
  max_value: z.coerce.number().min(0).optional(), // Added coerce for URL params
  date_from: z.string().datetime().optional(),
  date_to: z.string().datetime().optional(),
  search: z.string().optional(),
  page: z.coerce.number().min(1).default(1), // Added coerce for URL params
  limit: z.coerce.number().min(1).max(100).default(20) // Added coerce for URL params
})

// Deal bulk update schema
export const dealBulkUpdateSchema = z.object({
  deal_ids: z.array(z.string().uuid()).min(1, 'At least one deal must be selected'),
  updates: z.object({
    status: z.enum(['pending', 'confirmed', 'paid', 'disputed']).optional(),
    commission_rate: z.number().min(0).max(1).optional(),
    payment_date: z.string().datetime().optional(),
    notes: z.string().max(500).optional()
  }).refine(updates => Object.keys(updates).length > 0, 'At least one field must be updated')
})

// Commission calculation schema
export const commissionCalculationSchema = z.object({
  deal_value: z.number().min(0.01, 'Deal value must be greater than 0'),
  tier: z.enum(['trusted', 'elite', 'diamond']),
  custom_rate: z.number().min(0).max(1).optional() // override tier rate
})

// Deal performance metrics schema
export const dealMetricsSchema = z.object({
  partner_id: z.string().uuid().optional(),
  period: z.enum(['7d', '30d', '90d', '1y', 'all']).default('30d'),
  group_by: z.enum(['day', 'week', 'month']).default('day'),
  include_disputed: z.boolean().default(false)
})

// Type exports
export type DealCreation = z.infer<typeof dealCreationSchema>
export type DealUpdate = z.infer<typeof dealUpdateSchema>
export type DealMilestone = z.infer<typeof dealMilestoneSchema>
export type DealFilter = z.infer<typeof dealFilterSchema>
export type DealBulkUpdate = z.infer<typeof dealBulkUpdateSchema>
export type CommissionCalculation = z.infer<typeof commissionCalculationSchema>
export type DealMetrics = z.infer<typeof dealMetricsSchema>