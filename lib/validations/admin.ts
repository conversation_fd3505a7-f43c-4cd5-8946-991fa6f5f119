import { z } from 'zod'

// Team assignment schema
export const teamAssignmentSchema = z.object({
  partner_id: z.string().uuid('Invalid partner ID'),
  sales_user_id: z.string().uuid('Invalid sales user ID'),
  ops_user_id: z.string().uuid('Invalid ops user ID').optional(),
  accounting_user_id: z.string().uuid('Invalid accounting user ID').optional(),
  notes: z.string().max(500, 'Notes must be less than 500 characters').optional()
})

// Bulk reassignment schema
export const bulkReassignmentSchema = z.object({
  partner_ids: z.array(z.string().uuid()).min(1, 'At least one partner must be selected'),
  reassignments: z.object({
    sales_user_id: z.string().uuid().optional(),
    ops_user_id: z.string().uuid().optional(),
    accounting_user_id: z.string().uuid().optional()
  }).refine(data => Object.keys(data).length > 0, 'At least one assignment must be specified'),
  reason: z.string().min(5, 'Reason for reassignment is required'),
  notify_partners: z.boolean().default(true)
})

// Internal user creation schema
export const internalUserSchema = z.object({
  email: z.string().email('Invalid email address'),
  display_name: z.string().min(2, 'Display name must be at least 2 characters'),
  role: z.enum(['sales', 'ops', 'accounting', 'super_admin'])
})

// Partner status update schema
export const partnerStatusUpdateSchema = z.object({
  partner_id: z.string().uuid('Invalid partner ID'),
  status: z.enum(['active', 'pending', 'suspended']),
  reason: z.string().min(5, 'Reason for status change is required'),
  notify_partner: z.boolean().default(true)
})

// Tier update schema
export const tierUpdateSchema = z.object({
  partner_id: z.string().uuid('Invalid partner ID'),
  new_tier: z.enum(['trusted', 'elite', 'diamond']),
  reason: z.string().min(5, 'Reason for tier change is required'),
  notify_partner: z.boolean().default(true),
  effective_date: z.string().datetime().optional()
})

// System settings schema
export const systemSettingSchema = z.object({
  setting_key: z.string().min(1, 'Setting key is required'),
  setting_value: z.any(), // flexible JSON value
  description: z.string().optional(),
  category: z.enum(['commission', 'system', 'email', 'security'])
})

// Feature flag schema
export const featureFlagSchema = z.object({
  flag_key: z.string().regex(/^[a-z_]+$/, 'Flag key must be lowercase with underscores'),
  flag_name: z.string().min(1, 'Flag name is required'),
  description: z.string().optional(),
  enabled: z.boolean().default(false),
  rollout_percentage: z.number().min(0).max(100).default(0),
  target_users: z.array(z.string().uuid()).optional(),
  target_tiers: z.array(z.enum(['trusted', 'elite', 'diamond'])).optional(),
  environment: z.enum(['development', 'staging', 'production']).default('production')
})

// Analytics query schema
export const analyticsQuerySchema = z.object({
  partner_id: z.string().uuid().optional(),
  date_from: z.string().date(),
  date_to: z.string().date(),
  metrics: z.array(z.enum([
    'referral_clicks', 'leads_submitted', 'deals_confirmed', 
    'commission_earned', 'conversion_rate', 'page_views'
  ])).min(1, 'At least one metric must be selected'),
  group_by: z.enum(['day', 'week', 'month']).default('day')
}).refine(data => {
  const fromDate = new Date(data.date_from)
  const toDate = new Date(data.date_to)
  return fromDate <= toDate
}, 'From date must be before or equal to to date')

// Data export schema
export const dataExportSchema = z.object({
  table: z.enum(['partners', 'leads', 'deals', 'withdrawals', 'invoices', 'analytics']),
  format: z.enum(['csv', 'xlsx', 'json']).default('csv'),
  date_from: z.string().datetime().optional(),
  date_to: z.string().datetime().optional(),
  filters: z.record(z.string(), z.any()).optional(), // flexible filters per table
  fields: z.array(z.string()).optional() // specific fields to export
})

// Commission rate update schema
export const commissionRateUpdateSchema = z.object({
  tier: z.enum(['trusted', 'elite', 'diamond']),
  new_rate: z.number().min(0).max(1, 'Commission rate must be between 0 and 1'),
  effective_date: z.string().datetime().optional(),
  reason: z.string().min(5, 'Reason for rate change is required')
})

// Audit log filter schema
export const auditLogFilterSchema = z.object({
  user_id: z.string().uuid().optional(),
  user_type: z.enum(['partner', 'internal']).optional(),
  action: z.string().optional(),
  resource_type: z.string().optional(),
  date_from: z.string().datetime().optional(),
  date_to: z.string().datetime().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(50)
})

// Type exports
export type TeamAssignment = z.infer<typeof teamAssignmentSchema>
export type BulkReassignment = z.infer<typeof bulkReassignmentSchema>
export type InternalUser = z.infer<typeof internalUserSchema>
export type PartnerStatusUpdate = z.infer<typeof partnerStatusUpdateSchema>
export type TierUpdate = z.infer<typeof tierUpdateSchema>
export type SystemSetting = z.infer<typeof systemSettingSchema>
export type FeatureFlag = z.infer<typeof featureFlagSchema>
export type AnalyticsQuery = z.infer<typeof analyticsQuerySchema>
export type DataExport = z.infer<typeof dataExportSchema>
export type CommissionRateUpdate = z.infer<typeof commissionRateUpdateSchema>
export type AuditLogFilter = z.infer<typeof auditLogFilterSchema>