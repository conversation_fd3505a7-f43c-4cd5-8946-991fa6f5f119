import { z } from 'zod'

// Project POC schema
export const projectPocSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  telegram: z.string().regex(/^@[\w\d_]+$/, 'Invalid Telegram handle format').optional(),
  whatsapp: z.string().regex(/^\+[\d\s\-\(\)]+$/, 'Invalid WhatsApp number format').optional()
})

// Lead submission schema
export const leadSubmissionSchema = z.object({
  company_name: z.string().min(2, 'Company name must be at least 2 characters'),
  website: z.string().url('Invalid website URL').optional(),
  x_link: z.string().url('Invalid X profile URL').optional(),
  project_poc: projectPocSchema,
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
  source: z.object({
    utm_source: z.string().optional(),
    utm_medium: z.string().optional(),
    utm_campaign: z.string().optional(),
    utm_term: z.string().optional(),
    utm_content: z.string().optional(),
    referrer: z.string().optional(),
    ip_address: z.string().optional(),
    user_agent: z.string().optional()
  }).optional()
})

// Lead review schema (for internal users)
export const leadReviewSchema = z.object({
  status: z.enum(['in_review', 'approved', 'rejected']),
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
  partner_id: z.string().uuid('Invalid partner ID').optional() // for reassignment
})

// Lead search/filter schema
export const leadFilterSchema = z.object({
  status: z.enum(['in_review', 'approved', 'rejected']).optional(),
  partner_id: z.string().uuid().optional(),
  search: z.string().optional(),
  date_from: z.string().datetime().optional(),
  date_to: z.string().datetime().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20)
})

// Bulk lead assignment schema
export const bulkLeadAssignmentSchema = z.object({
  lead_ids: z.array(z.string().uuid()).min(1, 'At least one lead must be selected'),
  partner_id: z.string().uuid('Invalid partner ID'),
  notes: z.string().max(500, 'Notes must be less than 500 characters').optional()
})

// Lead export schema
export const leadExportSchema = z.object({
  format: z.enum(['csv', 'xlsx', 'json']).default('csv'),
  filters: leadFilterSchema.optional(),
  fields: z.array(z.enum([
    'company_name', 'website', 'project_poc', 'status', 
    'partner_name', 'created_at', 'notes'
  ])).optional()
})

// Type exports
export type ProjectPoc = z.infer<typeof projectPocSchema>
export type LeadSubmission = z.infer<typeof leadSubmissionSchema>
export type LeadReview = z.infer<typeof leadReviewSchema>
export type LeadFilter = z.infer<typeof leadFilterSchema>
export type BulkLeadAssignment = z.infer<typeof bulkLeadAssignmentSchema>
export type LeadExport = z.infer<typeof leadExportSchema>