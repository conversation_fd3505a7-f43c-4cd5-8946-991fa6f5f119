import { z } from 'zod'

// Available admin roles (excluding partner which is auto-assigned)
export const adminRoles = ['sales', 'ops', 'accounting', 'super_admin'] as const

// User management schemas
export const addAdminUserSchema = z.object({
  email: z.string().email('Invalid email address'),
  display_name: z.string().min(2, 'Display name must be at least 2 characters').max(50, 'Display name must be less than 50 characters'),
  role: z.enum(adminRoles, { message: 'Invalid role selected' })
})

export const updateUserRoleSchema = z.object({
  user_id: z.string().min(1, 'User ID is required'),
  role: z.enum(adminRoles, { message: 'Invalid role selected' }),
  reason: z.string().min(5, 'Reason must be at least 5 characters').max(200, 'Reason must be less than 200 characters').optional()
})

export const removeAdminAccessSchema = z.object({
  user_id: z.string().min(1, 'User ID is required'),
  reason: z.string().min(5, 'Reason must be at least 5 characters').max(200, 'Reason must be less than 200 characters')
})

export const userFiltersSchema = z.object({
  role: z.enum([...adminRoles, 'all']).optional(),
  search: z.string().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20)
})

// Type exports
export type AdminRole = typeof adminRoles[number]
export type AddAdminUser = z.infer<typeof addAdminUserSchema>
export type UpdateUserRole = z.infer<typeof updateUserRoleSchema>
export type RemoveAdminAccess = z.infer<typeof removeAdminAccessSchema>
export type UserFilters = z.infer<typeof userFiltersSchema>

// User display data type
export type AdminUser = {
  id: string
  email: string
  display_name: string | null
  role: AdminRole | null
  created_at: string
  updated_at: string
  last_sign_in_at?: string | null
  first_name?: string | null
  last_name?: string | null
  profile_image_url?: string | null
  clerk_user_id?: string
}