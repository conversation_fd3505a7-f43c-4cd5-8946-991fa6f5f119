import { z } from 'zod'

// Support ticket creation schema
export const supportTicketSchema = z.object({
  subject: z.string().min(5, 'Subject must be at least 5 characters').max(100, 'Subject must be less than 100 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters').max(2000, 'Description must be less than 2000 characters'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  category: z.enum(['technical', 'billing', 'general', 'feature_request']).default('general'),
  attachments: z.array(z.object({
    filename: z.string(),
    url: z.string().url(),
    size: z.number().min(1),
    mime_type: z.string()
  })).max(5, 'Maximum 5 attachments allowed').optional()
})

// Support message schema
export const supportMessageSchema = z.object({
  ticket_id: z.string().uuid('Invalid ticket ID'),
  message: z.string().min(1, 'Message cannot be empty').max(2000, 'Message must be less than 2000 characters'),
  attachments: z.array(z.object({
    filename: z.string(),
    url: z.string().url(),
    size: z.number().min(1),
    mime_type: z.string()
  })).max(3, 'Maximum 3 attachments per message').optional(),
  is_internal: z.boolean().default(false) // for internal notes
})

// Ticket update schema (for internal users)
export const ticketUpdateSchema = z.object({
  status: z.enum(['open', 'in_progress', 'waiting_response', 'resolved', 'closed']).optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  assigned_to: z.string().uuid('Invalid user ID').optional(),
  resolution: z.string().max(1000, 'Resolution must be less than 1000 characters').optional()
})

// Support ticket filter schema
export const supportFilterSchema = z.object({
  status: z.enum(['open', 'in_progress', 'waiting_response', 'resolved', 'closed']).optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  category: z.enum(['technical', 'billing', 'general', 'feature_request']).optional(),
  assigned_to: z.string().uuid().optional(),
  partner_id: z.string().uuid().optional(),
  search: z.string().optional(),
  date_from: z.string().datetime().optional(),
  date_to: z.string().datetime().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20)
})

// FAQ schema
export const faqSchema = z.object({
  question: z.string().min(10, 'Question must be at least 10 characters').max(200, 'Question must be less than 200 characters'),
  answer: z.string().min(10, 'Answer must be at least 10 characters').max(2000, 'Answer must be less than 2000 characters'),
  category: z.string().min(1, 'Category is required'),
  tags: z.array(z.string()).max(10, 'Maximum 10 tags allowed').optional(),
  active: z.boolean().default(true)
})

// FAQ search schema
export const faqSearchSchema = z.object({
  query: z.string().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  active: z.boolean().default(true)
})

// File upload schema
export const fileUploadSchema = z.object({
  file: z.object({
    name: z.string(),
    type: z.string(),
    size: z.number().max(10 * 1024 * 1024, 'File size must be less than 10MB')
  }),
  allowed_types: z.array(z.string()).default(['image/jpeg', 'image/png', 'application/pdf', 'text/plain'])
}).refine(data => {
  return data.allowed_types.includes(data.file.type)
}, 'File type not allowed')

// Bulk ticket actions schema
export const bulkTicketActionSchema = z.object({
  ticket_ids: z.array(z.string().uuid()).min(1, 'At least one ticket must be selected'),
  action: z.enum(['assign', 'close', 'change_priority', 'change_status']),
  assigned_to: z.string().uuid().optional(), // for assign action
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(), // for change_priority action
  status: z.enum(['open', 'in_progress', 'waiting_response', 'resolved', 'closed']).optional(), // for change_status action
  notes: z.string().max(500).optional()
})

// Type exports
export type SupportTicket = z.infer<typeof supportTicketSchema>
export type SupportMessage = z.infer<typeof supportMessageSchema>
export type TicketUpdate = z.infer<typeof ticketUpdateSchema>
export type SupportFilter = z.infer<typeof supportFilterSchema>
export type FAQ = z.infer<typeof faqSchema>
export type FAQSearch = z.infer<typeof faqSearchSchema>
export type FileUpload = z.infer<typeof fileUploadSchema>
export type BulkTicketAction = z.infer<typeof bulkTicketActionSchema>