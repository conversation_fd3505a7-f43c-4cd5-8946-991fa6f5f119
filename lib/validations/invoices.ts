import { z } from 'zod'

// Invoice line item schema
export const invoiceLineItemSchema = z.object({
  description: z.string().min(1, 'Description is required'),
  quantity: z.number().min(0.01, 'Quantity must be greater than 0').default(1),
  unit_price: z.number().min(0.01, 'Unit price must be greater than 0'),
  deal_reference: z.string().optional()
}).refine(data => {
  const total = data.quantity * data.unit_price
  return total > 0
}, 'Total amount must be greater than 0')

// Invoice creation schema
export const invoiceCreationSchema = z.object({
  partner_id: z.string().uuid('Invalid partner ID'),
  deal_id: z.string().uuid('Invalid deal ID').optional(),
  line_items: z.array(invoiceLineItemSchema).min(1, 'At least one line item is required'),
  due_date: z.string().date('Invalid due date'),
  currency: z.string().length(3, 'Currency must be 3-letter code').default('USD'),
  notes: z.string().max(500, 'Notes must be less than 500 characters').optional()
}).refine(data => {
  // Validate due date is in the future
  const dueDate = new Date(data.due_date)
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return dueDate >= today
}, 'Due date must be today or in the future')

// Invoice update schema
export const invoiceUpdateSchema = z.object({
  status: z.enum(['draft', 'sent', 'paid', 'overdue', 'cancelled']).optional(),
  due_date: z.string().date().optional(),
  notes: z.string().max(500).optional(),
  payment_date: z.string().datetime().optional()
})

// Invoice filter schema
export const invoiceFilterSchema = z.object({
  partner_id: z.string().uuid().optional(),
  status: z.enum(['draft', 'sent', 'paid', 'overdue', 'cancelled']).optional(),
  min_amount: z.number().min(0).optional(),
  max_amount: z.number().min(0).optional(),
  due_date_from: z.string().date().optional(),
  due_date_to: z.string().date().optional(),
  created_from: z.string().datetime().optional(),
  created_to: z.string().datetime().optional(),
  search: z.string().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20)
})

// Invoice data schema (for PDF generation)
export const invoiceDataSchema = z.object({
  invoice_number: z.string(),
  partner: z.object({
    full_name: z.string(),
    company_name: z.string(),
    email: z.string().email(),
    billing_address: z.object({
      street1: z.string(),
      street2: z.string().optional(),
      city: z.string(),
      state: z.string(),
      postal_code: z.string(),
      country: z.string(),
      tax_id: z.string().optional()
    })
  }),
  client: z.object({
    company_name: z.string(),
    contact_name: z.string(),
    contact_email: z.string().email()
  }),
  line_items: z.array(invoiceLineItemSchema),
  subtotal: z.number(),
  tax_rate: z.number().min(0).max(1).default(0),
  tax_amount: z.number().min(0).default(0),
  total_amount: z.number().min(0.01),
  currency: z.string().length(3).default('USD'),
  due_date: z.string().date(),
  issue_date: z.string().date(),
  payment_terms: z.string().default('Net 30'),
  notes: z.string().optional()
})

// Bulk invoice actions schema
export const bulkInvoiceActionSchema = z.object({
  invoice_ids: z.array(z.string().uuid()).min(1, 'At least one invoice must be selected'),
  action: z.enum(['send', 'mark_paid', 'cancel', 'regenerate']),
  payment_date: z.string().datetime().optional(), // for mark_paid action
  notes: z.string().max(500).optional()
})

// Invoice settings schema
export const invoiceSettingsSchema = z.object({
  default_payment_terms: z.string().default('Net 30'),
  default_due_days: z.number().min(1).max(365).default(30),
  tax_rate: z.number().min(0).max(1).default(0),
  company_info: z.object({
    name: z.string(),
    address: z.object({
      street1: z.string(),
      street2: z.string().optional(),
      city: z.string(),
      state: z.string(),
      postal_code: z.string(),
      country: z.string()
    }),
    tax_id: z.string().optional(),
    email: z.string().email(),
    phone: z.string().optional()
  }),
  logo_url: z.string().url().optional(),
  footer_text: z.string().optional()
})

// Invoice export schema
export const invoiceExportSchema = z.object({
  format: z.enum(['pdf', 'csv', 'xlsx']).default('pdf'),
  filters: invoiceFilterSchema.optional(),
  include_line_items: z.boolean().default(true)
})

// Type exports
export type InvoiceLineItem = z.infer<typeof invoiceLineItemSchema>
export type InvoiceCreation = z.infer<typeof invoiceCreationSchema>
export type InvoiceUpdate = z.infer<typeof invoiceUpdateSchema>
export type InvoiceFilter = z.infer<typeof invoiceFilterSchema>
export type InvoiceData = z.infer<typeof invoiceDataSchema>
export type BulkInvoiceAction = z.infer<typeof bulkInvoiceActionSchema>
export type InvoiceSettings = z.infer<typeof invoiceSettingsSchema>
export type InvoiceExport = z.infer<typeof invoiceExportSchema>