import type { ReactNode } from 'react'

interface FeatureFlags {
  invoiceWizard: boolean
  bulkReassignment: boolean
  referralMetrics: boolean
  adminAnalytics: boolean
  emailNotifications: boolean
  advancedReporting: boolean
}

type FeatureFlagKey = keyof FeatureFlags

class FeatureFlagsManager {
  private flags: FeatureFlags

  constructor() {
    this.flags = {
      invoiceWizard: this.getEnvFlag('INVOICE_WIZARD', true),
      bulkReassignment: this.getEnvFlag('BULK_REASSIGNMENT', false),
      referralMetrics: this.getEnvFlag('REFERRAL_METRICS', true),
      adminAnalytics: this.getEnvFlag('ADMIN_ANALYTICS', true),
      emailNotifications: this.getEnvFlag('EMAIL_NOTIFICATIONS', true),
      advancedReporting: this.getEnvFlag('ADVANCED_REPORTING', false),
    }
  }

  private getEnvFlag(name: string, defaultValue: boolean): boolean {
    const envValue = process.env[`NEXT_PUBLIC_FEATURE_${name}`]
    if (envValue === undefined) return defaultValue
    return envValue.toLowerCase() === 'true'
  }

  isEnabled(flag: FeatureFlagKey): boolean {
    return this.flags[flag]
  }

  getAll(): FeatureFlags {
    return { ...this.flags }
  }

  // For admin override (future enhancement)
  setFlag(flag: FeatureFlagKey, enabled: boolean): void {
    this.flags[flag] = enabled
  }
}

export const featureFlags = new FeatureFlagsManager()

// React hook for client-side usage
export function useFeatureFlag(flag: FeatureFlagKey): boolean {
  return featureFlags.isEnabled(flag)
}

// Server-side feature flag checking
export function isFeatureEnabled(flag: FeatureFlagKey): boolean {
  return featureFlags.isEnabled(flag)
}

// Component wrapper for feature-gated UI
interface FeatureGateProps {
  feature: FeatureFlagKey
  children: ReactNode
  fallback?: ReactNode
}

export function FeatureGate({ feature, children, fallback = null }: FeatureGateProps): ReactNode {
  return useFeatureFlag(feature) ? children : fallback
}

// Server component version
export function ServerFeatureGate({
  feature,
  children,
  fallback = null
}: FeatureGateProps): ReactNode {
  return isFeatureEnabled(feature) ? children : fallback
}