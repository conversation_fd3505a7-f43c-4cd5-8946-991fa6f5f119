interface LogContext {
  requestId?: string
  userId?: string
  action?: string
  duration?: number
  error?: any
  metadata?: Record<string, any>
}

type LogLevel = 'debug' | 'info' | 'warn' | 'error'

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development'
  private sensitiveFields = new Set([
    'password', 'token', 'secret', 'key', 'wallet_or_bank', 
    'billing_address', 'iban', 'account_no', 'turnstile_token'
  ])

  private redactSecrets(obj: any): any {
    if (!obj || typeof obj !== 'object') return obj
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.redactSecrets(item))
    }

    const redacted: any = {}
    for (const [key, value] of Object.entries(obj)) {
      if (this.sensitiveFields.has(key.toLowerCase())) {
        redacted[key] = '[REDACTED]'
      } else if (typeof value === 'object') {
        redacted[key] = this.redactSecrets(value)
      } else {
        redacted[key] = value
      }
    }
    return redacted
  }

  private formatMessage(level: LogLevel, message: string, context?: LogContext) {
    const timestamp = new Date().toISOString()
    const logEntry = {
      level,
      message,
      timestamp,
      ...this.redactSecrets(context || {})
    }

    return this.isDevelopment 
      ? JSON.stringify(logEntry, null, 2)
      : JSON.stringify(logEntry)
  }

  debug(message: string, context?: LogContext) {
    if (this.isDevelopment || process.env.LOG_LEVEL === 'debug') {
      console.debug(this.formatMessage('debug', message, context))
    }
  }

  info(message: string, context?: LogContext) {
    console.log(this.formatMessage('info', message, context))
  }

  warn(message: string, context?: LogContext) {
    console.warn(this.formatMessage('warn', message, context))
  }

  error(message: string, context?: LogContext) {
    console.error(this.formatMessage('error', message, context))
  }

  // Specialized logging methods for common use cases
  apiRequest(method: string, url: string, context?: LogContext) {
    this.info(`${method} ${url}`, { 
      ...context, 
      action: 'api_request',
      metadata: {
        ...context?.metadata,
        method,
        url
      }
    })
  }

  apiResponse(method: string, url: string, status: number, duration: number, context?: LogContext) {
    const level = status >= 400 ? 'error' : status >= 300 ? 'warn' : 'info'
    this[level](`${method} ${url} ${status}`, {
      ...context,
      action: 'api_response',
      duration,
      metadata: {
        ...context?.metadata,
        method,
        url,
        status
      }
    })
  }

  authEvent(event: string, userId: string, context?: LogContext) {
    this.info(`Auth: ${event}`, {
      ...context,
      action: 'auth_event',
      userId,
      metadata: {
        ...context?.metadata,
        event
      }
    })
  }

  businessEvent(event: string, context?: LogContext) {
    this.info(`Business: ${event}`, {
      ...context,
      action: 'business_event',
      metadata: {
        ...context?.metadata,
        event
      }
    })
  }

  securityEvent(event: string, context?: LogContext) {
    this.warn(`Security: ${event}`, {
      ...context,
      action: 'security_event',
      metadata: {
        ...context?.metadata,
        event
      }
    })
  }
}

export const log = new Logger()

// Helper for measuring function execution time
export function withLogging<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  operation: string
) {
  return async (...args: T): Promise<R> => {
    const start = Date.now()
    const requestId = Math.random().toString(36).substring(7)
    
    log.debug(`Starting ${operation}`, { 
      requestId, 
      metadata: { operation }
    })
    
    try {
      const result = await fn(...args)
      const duration = Date.now() - start
      
      log.info(`Completed ${operation}`, { 
        requestId, 
        duration,
        metadata: {
          operation,
          success: true 
        }
      })
      
      return result
    } catch (error) {
      const duration = Date.now() - start
      
      log.error(`Failed ${operation}`, {
        requestId,
        duration,
        error: error instanceof Error ? error.message : String(error),
        metadata: {
          operation,
          success: false
        }
      })
      
      throw error
    }
  }
}