// This file will be auto-generated by Supabase CLI
// Run: bun run db:generate-types
// For now, providing basic type structure

export interface Database {
  public: {
    Tables: {
      partners_profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          company_name: string | null
          company_type: string | null
          role: string | null
          tier: 'trusted' | 'elite' | 'diamond'
          status: 'active' | 'pending' | 'suspended'
          billing_address: any | null
          telegram: string | null
          whatsapp: string | null
          x_profile: string | null
          referral_slug: string | null
          internal_poc: string | null
          terms_version: string | null
          terms_accepted_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          full_name?: string | null
          company_name?: string | null
          company_type?: string | null
          role?: string | null
          tier?: 'trusted' | 'elite' | 'diamond'
          status?: 'active' | 'pending' | 'suspended'
          billing_address?: any | null
          telegram?: string | null
          whatsapp?: string | null
          x_profile?: string | null
          referral_slug?: string | null
          internal_poc?: string | null
          terms_version?: string | null
          terms_accepted_at?: string | null
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          company_name?: string | null
          company_type?: string | null
          role?: string | null
          tier?: 'trusted' | 'elite' | 'diamond'
          status?: 'active' | 'pending' | 'suspended'
          billing_address?: any | null
          telegram?: string | null
          whatsapp?: string | null
          x_profile?: string | null
          referral_slug?: string | null
          internal_poc?: string | null
          terms_version?: string | null
          terms_accepted_at?: string | null
        }
      }
      partners_users: {
        Row: {
          id: string
          email: string
          display_name: string | null
          role: 'sales' | 'ops' | 'accounting' | 'super_admin'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          display_name?: string | null
          role: 'sales' | 'ops' | 'accounting' | 'super_admin'
        }
        Update: {
          id?: string
          email?: string
          display_name?: string | null
          role?: 'sales' | 'ops' | 'accounting' | 'super_admin'
        }
      }
      partners_leads: {
        Row: {
          id: string
          partner_id: string | null
          company_name: string
          website: string | null
          x_link: string | null
          project_poc: any
          notes: string | null
          status: 'in_review' | 'approved' | 'rejected'
          source: any | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          partner_id?: string | null
          company_name: string
          website?: string | null
          x_link?: string | null
          project_poc: any
          notes?: string | null
          status?: 'in_review' | 'approved' | 'rejected'
          source?: any | null
        }
        Update: {
          id?: string
          partner_id?: string | null
          company_name?: string
          website?: string | null
          x_link?: string | null
          project_poc?: any
          notes?: string | null
          status?: 'in_review' | 'approved' | 'rejected'
          source?: any | null
        }
      }
      partners_deals: {
        Row: {
          id: string
          lead_id: string | null
          partner_id: string
          client_company: string
          client_poc: any
          deal_value: number
          commission_rate: number
          commission_amount: number
          status: 'pending' | 'confirmed' | 'paid' | 'disputed'
          invoice_required: boolean
          invoice_submitted: boolean
          invoice_data: any | null
          payment_date: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          lead_id?: string | null
          partner_id: string
          client_company: string
          client_poc: any
          deal_value: number
          commission_rate: number
          commission_amount: number
          status?: 'pending' | 'confirmed' | 'paid' | 'disputed'
          invoice_required?: boolean
          invoice_submitted?: boolean
          invoice_data?: any | null
          payment_date?: string | null
          notes?: string | null
        }
        Update: {
          id?: string
          lead_id?: string | null
          partner_id?: string
          client_company?: string
          client_poc?: any
          deal_value?: number
          commission_rate?: number
          commission_amount?: number
          status?: 'pending' | 'confirmed' | 'paid' | 'disputed'
          invoice_required?: boolean
          invoice_submitted?: boolean
          invoice_data?: any | null
          payment_date?: string | null
          notes?: string | null
        }
      }
      partners_withdrawals: {
        Row: {
          id: string
          partner_id: string
          withdrawal_method_id: string
          amount: number
          currency: string
          status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
          fee_amount: number
          net_amount: number
          transaction_hash: string | null
          bank_reference: string | null
          processor_reference: string | null
          notes: string | null
          processed_at: string | null
          completed_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          partner_id: string
          withdrawal_method_id: string
          amount: number
          currency?: string
          status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
          fee_amount?: number
          net_amount: number
          transaction_hash?: string | null
          bank_reference?: string | null
          processor_reference?: string | null
          notes?: string | null
          processed_at?: string | null
          completed_at?: string | null
        }
        Update: {
          id?: string
          partner_id?: string
          withdrawal_method_id?: string
          amount?: number
          currency?: string
          status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
          fee_amount?: number
          net_amount?: number
          transaction_hash?: string | null
          bank_reference?: string | null
          processor_reference?: string | null
          notes?: string | null
          processed_at?: string | null
          completed_at?: string | null
        }
      }
      partners_teams_assignments: {
        Row: {
          id: string
          partner_id: string
          sales_user_id: string
          ops_user_id: string | null
          accounting_user_id: string | null
          active: boolean
          notes: string | null
          assigned_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          partner_id: string
          sales_user_id: string
          ops_user_id?: string | null
          accounting_user_id?: string | null
          active?: boolean
          notes?: string | null
          assigned_at?: string
        }
        Update: {
          id?: string
          partner_id?: string
          sales_user_id?: string
          ops_user_id?: string | null
          accounting_user_id?: string | null
          active?: boolean
          notes?: string | null
        }
      }
      // Additional tables will be defined here...
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      set_auth_user_id: {
        Args: {
          user_id: string
        }
        Returns: undefined
      }
      get_my_role: {
        Args: Record<PropertyKey, never>
        Returns: string | null
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}