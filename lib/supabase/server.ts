import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { auth } from '@clerk/nextjs/server'
import { log } from '@/lib/log'
import type { Database } from './types'

export async function createClient(useServiceRole = false) {
  const cookieStore = await cookies()
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  const supabaseKey = useServiceRole
    ? process.env.SUPABASE_SERVICE_ROLE_KEY!
    : process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

  if (!supabaseUrl || !supabaseKey) {
    log.error('Supabase configuration missing', {
      metadata: {
        hasUrl: !!supabaseUrl,
        hasKey: !!supabaseKey,
        useServiceRole
      }
    })
    throw new Error('Supabase configuration incomplete')
  }

  const client = createServerClient<Database>(supabaseUrl, supabaseKey, {
    cookies: {
      getAll() {
        return cookieStore.getAll()
      },
      setAll(cookiesToSet) {
        try {
          cookiesToSet.forEach(({ name, value, options }) => 
            cookieStore.set(name, value, options)
          )
        } catch (error) {
          // Ignore errors from setting cookies in Server Components
          log.warn('Failed to set Supabase cookies', { 
            error, 
            metadata: { cookieCount: cookiesToSet.length } 
          })
        }
      }
    }
  })

  // Note: RLS context is now set per-operation in specific functions
  // that need it, rather than globally on the client

  return client
}

// Convenience functions for common operations
export async function getProfile(userId?: string) {
  const client = await createClient()
  const authUserId = userId || (await auth()).userId
  
  if (!authUserId) {
    log.warn('Attempted to get profile without user ID')
    return { data: null, error: 'No user ID provided' }
  }
  
  log.debug('Fetching profile', { userId: authUserId })
  
  const { data, error } = await client
    .from('partners_profiles')
    .select('*')
    .eq('id', authUserId)
    .single()
    
  if (error) {
    log.error('Failed to fetch profile', { 
      userId: authUserId, 
      error: error.message 
    })
  }
    
  return { data, error }
}

export async function getUserRole(userId?: string): Promise<string | null> {
  const client = await createClient()
  const authUserId = userId || (await auth()).userId
  
  if (!authUserId) return null
  
  const { data, error } = await client
    .from('partners_users')
    .select('role')
    .eq('id', authUserId)
    .single()
    
  if (error) {
    log.debug('User not found in internal users table', { userId: authUserId })
    return null
  }
  
  log.debug('Internal user role retrieved', { userId: authUserId, metadata: { role: data.role } })
  return data.role
}

export async function isInternalUser(userId?: string): Promise<boolean> {
  const role = await getUserRole(userId)
  return role !== null
}

export async function getAssignedPartners(salesUserId: string) {
  const client = await createClient()
  
  const { data, error } = await client
    .from('partners_teams_assignments')
    .select(`
      partner_id,
      partners_profiles!inner(
        id,
        full_name,
        company_name,
        tier,
        status,
        email
      )
    `)
    .eq('sales_user_id', salesUserId)
    .eq('active', true)
    
  if (error) {
    log.error('Failed to fetch assigned partners', { 
      metadata: {
        salesUserId,
        errorMessage: error.message
      }
    })
    return { data: [], error }
  }
  
  return { data: data.map(item => item.partners_profiles), error: null }
}