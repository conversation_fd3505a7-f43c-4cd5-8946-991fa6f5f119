import { createBrowserClient } from '@supabase/ssr'
import type { Database } from './types'

export function createClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Supabase configuration incomplete - missing URL or anon key')
  }

  return createBrowserClient<Database>(supabaseUrl, supabaseAnonKey)
}

// For public access (like lead submission form)
export const supabase = createClient()