import { createClient } from "@/lib/supabase/server"

type NotificationType = 'approved' | 'rejected' | 'under_review'

interface EmailTemplate {
  subject: string
  html: string
  text: string
}

export class ApplicationNotificationService {
  private supabase = createClient()

  // Email templates for different notification types
  private getEmailTemplate(type: NotificationType, data: any): EmailTemplate {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://partners.ibcgroup.io'
    
    switch (type) {
      case 'approved':
        return {
          subject: `🎉 Your IBC Partner Application has been Approved!`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h1 style="color: #10b981;">Application Approved!</h1>
              <p>Hello ${data.full_name},</p>
              <p>Great news! Your partner application has been approved.</p>
              
              <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #059669;">Next Steps:</h3>
                <ol style="margin: 0;">
                  <li>Sign in to your partner portal: <a href="${baseUrl}/sign-in">${baseUrl}/sign-in</a></li>
                  <li>Complete your profile and set up your referral link</li>
                  <li>Start referring clients and earning commissions</li>
                </ol>
              </div>

              <p><strong>Company:</strong> ${data.company_name}</p>
              <p><strong>Tier:</strong> Trusted (5% commission rate)</p>
              
              ${data.review_notes ? `
                <div style="background: #f8fafc; padding: 15px; border-radius: 6px; margin: 15px 0;">
                  <h4 style="margin: 0 0 10px 0;">Review Notes:</h4>
                  <p style="margin: 0; color: #64748b;">${data.review_notes}</p>
                </div>
              ` : ''}

              <p>Welcome to the IBC Partner Program!</p>
              
              <p>Best regards,<br>The IBC Team</p>
            </div>
          `,
          text: `
            Application Approved!
            
            Hello ${data.full_name},
            
            Great news! Your partner application has been approved.
            
            Next Steps:
            1. Sign in to your partner portal: ${baseUrl}/sign-in
            2. Complete your profile and set up your referral link
            3. Start referring clients and earning commissions
            
            Company: ${data.company_name}
            Tier: Trusted (5% commission rate)
            
            ${data.review_notes ? `Review Notes: ${data.review_notes}` : ''}
            
            Welcome to the IBC Partner Program!
            
            Best regards,
            The IBC Team
          `
        }

      case 'rejected':
        return {
          subject: `Update on Your IBC Partner Application`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h1 style="color: #ef4444;">Application Update</h1>
              <p>Hello ${data.full_name},</p>
              <p>Thank you for your interest in becoming an IBC partner.</p>
              
              <p>After careful review, we are unable to approve your application at this time.</p>
              
              ${data.rejection_reason ? `
                <div style="background: #fef2f2; padding: 15px; border-radius: 6px; margin: 15px 0;">
                  <h4 style="margin: 0 0 10px 0;">Reason:</h4>
                  <p style="margin: 0; color: #991b1b;">${this.formatRejectionReason(data.rejection_reason)}</p>
                </div>
              ` : ''}

              ${data.review_notes ? `
                <div style="background: #f8fafc; padding: 15px; border-radius: 6px; margin: 15px 0;">
                  <h4 style="margin: 0 0 10px 0;">Additional Notes:</h4>
                  <p style="margin: 0; color: #64748b;">${data.review_notes}</p>
                </div>
              ` : ''}

              <p>If you believe this decision was made in error or you have additional information to provide, please contact our support team.</p>
              
              <p>Best regards,<br>The IBC Team</p>
            </div>
          `,
          text: `
            Application Update
            
            Hello ${data.full_name},
            
            Thank you for your interest in becoming an IBC partner.
            
            After careful review, we are unable to approve your application at this time.
            
            ${data.rejection_reason ? `Reason: ${this.formatRejectionReason(data.rejection_reason)}` : ''}
            ${data.review_notes ? `Additional Notes: ${data.review_notes}` : ''}
            
            If you believe this decision was made in error or you have additional information to provide, please contact our support team.
            
            Best regards,
            The IBC Team
          `
        }

      case 'under_review':
        return {
          subject: `Your IBC Partner Application is Under Review`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h1 style="color: #3b82f6;">Application Under Review</h1>
              <p>Hello ${data.full_name},</p>
              <p>Thank you for your interest in becoming an IBC partner.</p>
              
              <p>Your application is currently under review by our team. We typically complete our review process within 2-3 business days.</p>
              
              <div style="background: #eff6ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #1d4ed8;">What happens next?</h3>
                <ul style="margin: 0;">
                  <li>Our team will review your application details</li>
                  <li>You'll receive an email once a decision has been made</li>
                  <li>If approved, you'll get access to the partner portal</li>
                </ul>
              </div>

              <p><strong>Company:</strong> ${data.company_name}</p>
              <p><strong>Application ID:</strong> ${data.id.substring(0, 8)}...</p>
              
              <p>We appreciate your patience during the review process.</p>
              
              <p>Best regards,<br>The IBC Team</p>
            </div>
          `,
          text: `
            Application Under Review
            
            Hello ${data.full_name},
            
            Thank you for your interest in becoming an IBC partner.
            
            Your application is currently under review by our team. We typically complete our review process within 2-3 business days.
            
            What happens next?
            - Our team will review your application details
            - You'll receive an email once a decision has been made
            - If approved, you'll get access to the partner portal
            
            Company: ${data.company_name}
            Application ID: ${data.id.substring(0, 8)}...
            
            We appreciate your patience during the review process.
            
            Best regards,
            The IBC Team
          `
        }

      default:
        throw new Error(`Unknown notification type: ${type}`)
    }
  }

  private formatRejectionReason(reason: string): string {
    const reasons: Record<string, string> = {
      'incomplete_application': 'Incomplete application information',
      'not_qualified': 'Application does not meet our current partner criteria',
      'duplicate_application': 'Duplicate application detected',
      'invalid_company': 'Company information could not be verified',
      'terms_not_accepted': 'Terms and conditions not properly accepted',
      'other': 'Other reasons (see additional notes)'
    }
    
    return reasons[reason] || reason
  }

  // Send notification to applicant
  async sendNotificationToApplicant(
    applicationId: string, 
    type: NotificationType
  ): Promise<boolean> {
    try {
      // Get application details
      const { data: application, error: appError } = await (await this.supabase)
        .from('partners_applications')
        .select('*')
        .eq('id', applicationId)
        .single()

      if (appError || !application) {
        console.error('Error fetching application:', appError)
        return false
      }

      const template = this.getEmailTemplate(type, application)

      // Send email using your preferred service (Resend, Postmark, etc.)
      // For now, just log the email content
      console.log('📧 Email notification would be sent:', {
        to: application.email,
        subject: template.subject,
        type,
        applicationId
      })

      // TODO: Integrate with actual email service
      // Example with Resend:
      // await resend.emails.send({
      //   from: 'Partners <<EMAIL>>',
      //   to: application.email,
      //   subject: template.subject,
      //   html: template.html,
      //   text: template.text
      // })

      return true

    } catch (error) {
      console.error('Error sending applicant notification:', error)
      return false
    }
  }

  // Send notification to ops team
  async sendNotificationToOpsTeam(
    applicationId: string,
    action: string,
    performedBy: string
  ): Promise<boolean> {
    try {
      // Get application details
      const { data: application, error: appError } = await (await this.supabase)
        .from('partners_applications')
        .select('*')
        .eq('id', applicationId)
        .single()

      if (appError || !application) {
        console.error('Error fetching application:', appError)
        return false
      }

      // Get ops team members with notifications enabled
      const { data: opsUsers, error: opsError } = await (await this.supabase)
        .from('partners_users')
        .select(`
          email,
          display_name,
          partners_notification_preferences!inner(*)
        `)
        .eq('role', 'ops')
        .eq('partners_notification_preferences.application_submitted', true)

      if (opsError || !opsUsers?.length) {
        console.log('No ops users found or notification preferences error:', opsError)
        return false
      }

      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://partners.ibcgroup.io'

      const subject = `📋 Partner Application ${action.charAt(0).toUpperCase() + action.slice(1)}`
      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1>Partner Application ${action.charAt(0).toUpperCase() + action.slice(1)}</h1>
          
          <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin: 0 0 15px 0;">Application Details</h3>
            <p><strong>Applicant:</strong> ${application.full_name}</p>
            <p><strong>Email:</strong> ${application.email}</p>
            <p><strong>Company:</strong> ${application.company_name}</p>
            <p><strong>Status:</strong> ${application.status}</p>
            <p><strong>Action performed by:</strong> ${performedBy}</p>
          </div>

          <p>
            <a href="${baseUrl}/admin/leads" style="background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
              Review in Admin Panel
            </a>
          </p>
        </div>
      `

      // Send to all ops team members
      for (const user of opsUsers) {
        console.log(`📧 Ops notification would be sent to: ${user.email}`, {
          subject,
          applicationId,
          action
        })

        // TODO: Send actual email
        // await resend.emails.send({
        //   from: 'Partners <<EMAIL>>',
        //   to: user.email,
        //   subject,
        //   html
        // })
      }

      return true

    } catch (error) {
      console.error('Error sending ops notification:', error)
      return false
    }
  }

  // Main method to send all relevant notifications
  async sendApplicationNotifications(
    applicationId: string,
    newStatus: string,
    oldStatus: string,
    performedBy: string
  ): Promise<void> {
    try {
      // Send notification to applicant
      if (newStatus === 'approved') {
        await this.sendNotificationToApplicant(applicationId, 'approved')
      } else if (newStatus === 'rejected') {
        await this.sendNotificationToApplicant(applicationId, 'rejected')
      } else if (newStatus === 'under_review' && oldStatus === 'pending') {
        await this.sendNotificationToApplicant(applicationId, 'under_review')
      }

      // Send notification to ops team (for status changes)
      if (newStatus !== oldStatus) {
        await this.sendNotificationToOpsTeam(applicationId, newStatus, performedBy)
      }

    } catch (error) {
      console.error('Error in sendApplicationNotifications:', error)
      // Don't throw - notifications are not critical to the main flow
    }
  }
}

// Export singleton instance
export const applicationNotificationService = new ApplicationNotificationService()