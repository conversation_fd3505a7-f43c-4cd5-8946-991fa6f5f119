import { Resend } from 'resend'
import { log } from './log'

if (!process.env.RESEND_API_KEY) {
  throw new Error('RESEND_API_KEY environment variable is required')
}

const resend = new Resend(process.env.RESEND_API_KEY)

export type EmailTemplate = {
  to: string
  subject: string
  html: string
  text?: string
}

export const email = {
  async send(template: EmailTemplate) {
    try {
      const result = await resend.emails.send({
        from: 'IBC Partners <<EMAIL>>',
        to: template.to,
        subject: template.subject,
        html: template.html,
        text: template.text || stripHtml(template.html)
      })

      log.info('Email sent successfully', { 
        metadata: {
          to: template.to, 
          subject: template.subject,
          emailId: result.data?.id
        }
      })

      return result
    } catch (error) {
      log.error('Failed to send email', { 
        error,
        metadata: {
          to: template.to, 
          subject: template.subject
        }
      })
      throw error
    }
  },

  templates: {
    partnerApplicationApproved: (data: {
      partnerName: string
      loginUrl: string
      tier: string
    }) => ({
      subject: '🎉 Welcome to IBC Partners Program!',
      html: `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to IBC Partners</title>
          </head>
          <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #ffffff;">
              <!-- Header -->
              <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #2563eb; margin: 0; font-size: 28px; font-weight: bold;">IBC Group</h1>
                <p style="color: #64748b; margin: 5px 0 0 0; font-size: 14px;">Partners Program</p>
              </div>

              <!-- Content -->
              <div style="background-color: #f8fafc; border-radius: 8px; padding: 24px; margin-bottom: 24px;">
                <h2 style="color: #1e293b; margin: 0 0 16px 0; font-size: 24px;">Welcome, ${data.partnerName}! 🎉</h2>
                <p style="margin: 0 0 16px 0; color: #475569; font-size: 16px;">
                  Congratulations! Your application to join the IBC Partners Program has been approved.
                </p>
                <p style="margin: 0 0 16px 0; color: #475569; font-size: 16px;">
                  You've been assigned to our <strong>${data.tier.charAt(0).toUpperCase() + data.tier.slice(1)} tier</strong>, 
                  which means you'll earn competitive commissions on every successful referral.
                </p>
              </div>

              <!-- Next Steps -->
              <div style="margin-bottom: 24px;">
                <h3 style="color: #1e293b; margin: 0 0 16px 0; font-size: 20px;">Next Steps:</h3>
                <ul style="color: #475569; margin: 0; padding-left: 20px;">
                  <li style="margin-bottom: 8px;">Access your partner dashboard to get your unique referral links</li>
                  <li style="margin-bottom: 8px;">Download partner resources and marketing materials</li>
                  <li style="margin-bottom: 8px;">Start referring clients and track your commissions</li>
                  <li>Request payouts directly through the portal</li>
                </ul>
              </div>

              <!-- CTA Button -->
              <div style="text-align: center; margin: 32px 0;">
                <a href="${data.loginUrl}" 
                   style="display: inline-block; background-color: #2563eb; color: white; text-decoration: none; padding: 12px 24px; border-radius: 6px; font-weight: 600; font-size: 16px;">
                  Access Partner Dashboard
                </a>
              </div>

              <!-- Support -->
              <div style="background-color: #f1f5f9; border-radius: 6px; padding: 16px; margin-bottom: 24px;">
                <p style="margin: 0; color: #475569; font-size: 14px;">
                  <strong>Need help?</strong> Our partner support team is here to assist you. 
                  Contact us at <a href="mailto:<EMAIL>" style="color: #2563eb;"><EMAIL></a>
                </p>
              </div>

              <!-- Footer -->
              <div style="text-align: center; padding-top: 24px; border-top: 1px solid #e2e8f0;">
                <p style="margin: 0; color: #94a3b8; font-size: 12px;">
                  © 2024 IBC Group. All rights reserved.
                </p>
              </div>
            </div>
          </body>
        </html>
      `
    }),

    partnerApplicationRejected: (data: {
      partnerName: string
      reason?: string
    }) => ({
      subject: 'Update on your IBC Partners Application',
      html: `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>IBC Partners Application Update</title>
          </head>
          <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #ffffff;">
              <!-- Header -->
              <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #2563eb; margin: 0; font-size: 28px; font-weight: bold;">IBC Group</h1>
                <p style="color: #64748b; margin: 5px 0 0 0; font-size: 14px;">Partners Program</p>
              </div>

              <!-- Content -->
              <div style="margin-bottom: 24px;">
                <h2 style="color: #1e293b; margin: 0 0 16px 0; font-size: 24px;">Hello ${data.partnerName},</h2>
                <p style="margin: 0 0 16px 0; color: #475569; font-size: 16px;">
                  Thank you for your interest in the IBC Partners Program. After careful review, 
                  we are unable to approve your application at this time.
                </p>
                ${data.reason ? `
                  <p style="margin: 0 0 16px 0; color: #475569; font-size: 16px;">
                    <strong>Reason:</strong> ${data.reason}
                  </p>
                ` : ''}
                <p style="margin: 0 0 16px 0; color: #475569; font-size: 16px;">
                  We encourage you to reapply in the future as your business grows and evolves. 
                  Our requirements may also change over time.
                </p>
              </div>

              <!-- Support -->
              <div style="background-color: #f1f5f9; border-radius: 6px; padding: 16px; margin-bottom: 24px;">
                <p style="margin: 0; color: #475569; font-size: 14px;">
                  If you have questions about this decision, please contact us at 
                  <a href="mailto:<EMAIL>" style="color: #2563eb;"><EMAIL></a>
                </p>
              </div>

              <!-- Footer -->
              <div style="text-align: center; padding-top: 24px; border-top: 1px solid #e2e8f0;">
                <p style="margin: 0; color: #94a3b8; font-size: 12px;">
                  © 2024 IBC Group. All rights reserved.
                </p>
              </div>
            </div>
          </body>
        </html>
      `
    }),

    dealStatusUpdate: (data: {
      partnerName: string
      clientCompany: string
      dealValue: number
      commissionAmount: number
      status: string
      statusMessage: string
    }) => ({
      subject: `Deal Update: ${data.clientCompany} - ${data.status.charAt(0).toUpperCase() + data.status.slice(1)}`,
      html: `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Deal Status Update</title>
          </head>
          <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #ffffff;">
              <!-- Header -->
              <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #2563eb; margin: 0; font-size: 28px; font-weight: bold;">IBC Group</h1>
                <p style="color: #64748b; margin: 5px 0 0 0; font-size: 14px;">Partners Program</p>
              </div>

              <!-- Content -->
              <div style="background-color: #f8fafc; border-radius: 8px; padding: 24px; margin-bottom: 24px;">
                <h2 style="color: #1e293b; margin: 0 0 16px 0; font-size: 24px;">Deal Status Update</h2>
                <p style="margin: 0 0 16px 0; color: #475569; font-size: 16px;">
                  Hello ${data.partnerName}, we have an update on your referral for <strong>${data.clientCompany}</strong>.
                </p>
              </div>

              <!-- Deal Details -->
              <div style="border: 1px solid #e2e8f0; border-radius: 6px; padding: 20px; margin-bottom: 24px;">
                <h3 style="color: #1e293b; margin: 0 0 16px 0; font-size: 18px;">Deal Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 8px 0; color: #64748b; font-size: 14px;">Client:</td>
                    <td style="padding: 8px 0; color: #1e293b; font-weight: 600; text-align: right;">${data.clientCompany}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; color: #64748b; font-size: 14px;">Deal Value:</td>
                    <td style="padding: 8px 0; color: #1e293b; font-weight: 600; text-align: right;">$${data.dealValue.toLocaleString()}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; color: #64748b; font-size: 14px;">Your Commission:</td>
                    <td style="padding: 8px 0; color: #059669; font-weight: 600; text-align: right;">$${data.commissionAmount.toFixed(2)}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; color: #64748b; font-size: 14px;">Status:</td>
                    <td style="padding: 8px 0; color: #1e293b; font-weight: 600; text-align: right;">
                      <span style="background-color: ${getStatusColor(data.status)}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; text-transform: uppercase;">
                        ${data.status}
                      </span>
                    </td>
                  </tr>
                </table>
              </div>

              <!-- Status Message -->
              ${data.statusMessage ? `
                <div style="background-color: #f1f5f9; border-radius: 6px; padding: 16px; margin-bottom: 24px;">
                  <p style="margin: 0; color: #475569; font-size: 14px;">
                    <strong>Update:</strong> ${data.statusMessage}
                  </p>
                </div>
              ` : ''}

              <!-- CTA -->
              <div style="text-align: center; margin: 32px 0;">
                <a href="${process.env.NEXT_PUBLIC_APP_URL}/deals" 
                   style="display: inline-block; background-color: #2563eb; color: white; text-decoration: none; padding: 12px 24px; border-radius: 6px; font-weight: 600; font-size: 16px;">
                  View Deal Details
                </a>
              </div>

              <!-- Footer -->
              <div style="text-align: center; padding-top: 24px; border-top: 1px solid #e2e8f0;">
                <p style="margin: 0; color: #94a3b8; font-size: 12px;">
                  © 2024 IBC Group. All rights reserved.
                </p>
              </div>
            </div>
          </body>
        </html>
      `
    })
  }
}

function stripHtml(html: string): string {
  return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim()
}

function getStatusColor(status: string): string {
  switch (status) {
    case 'confirmed':
    case 'paid':
      return '#059669'
    case 'pending':
      return '#d97706'
    case 'rejected':
      return '#dc2626'
    default:
      return '#6b7280'
  }
}