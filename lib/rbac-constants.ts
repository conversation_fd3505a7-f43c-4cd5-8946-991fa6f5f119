// Role definitions
export type UserRole = 'partner' | 'sales' | 'ops' | 'accounting' | 'super_admin'
export type PartnerTier = 'trusted' | 'elite' | 'diamond'

// Role label mapping for consistent display
export const roleLabels: Record<UserRole, string> = {
  partner: 'Partner',
  sales: 'Sales',
  ops: 'Operations',
  accounting: 'Accounting',
  super_admin: 'Super Admin'
}

// Tier label mapping
export const tierLabels: Record<PartnerTier, string> = {
  trusted: 'Trusted',
  elite: 'Elite', 
  diamond: 'Diamond'
}

// Permission definitions
export type Permission =
  | 'view_own_profile'
  | 'update_own_profile'
  | 'view_own_deals'
  | 'view_own_withdrawals'
  | 'create_withdrawal_request'
  | 'view_financial_data'
  | 'view_partner_analytics'
  | 'view_own_analytics'
  | 'view_system_analytics'
  | 'submit_leads'
  | 'manage_users'
  | 'view_all_partners'
  | 'manage_partner_tiers'
  | 'approve_applications'
  | 'view_all_leads'
  | 'manage_leads'
  | 'view_all_deals'
  | 'manage_deals'
  | 'process_withdrawals'
  | 'view_all_earnings'
  | 'manage_system_settings'
  | 'view_audit_logs'
  | 'bulk_operations'

// Role to permissions mapping
export const rolePermissions: Record<UserRole, Permission[]> = {
  partner: [
    'view_own_profile',
    'update_own_profile',
    'view_own_deals',
    'view_own_withdrawals',
    'create_withdrawal_request',
    'view_own_analytics',
    'submit_leads'
  ],
  sales: [
    'view_all_partners',
    'view_all_leads',
    'manage_leads',
    'view_all_deals',
    'manage_deals',
    'view_partner_analytics',
    'view_financial_data'
  ],
  ops: [
    'view_all_partners',
    'approve_applications',
    'manage_partner_tiers',
    'view_all_leads',
    'manage_leads',
    'view_all_deals',
    'manage_deals',
    'view_all_earnings',
    'view_financial_data',
    'view_system_analytics'
  ],
  accounting: [
    'view_all_partners',
    'view_all_deals',
    'process_withdrawals',
    'view_all_earnings',
    'view_financial_data'
  ],
  super_admin: [
    'manage_users',
    'view_all_partners',
    'manage_partner_tiers',
    'approve_applications',
    'view_all_leads',
    'manage_leads',
    'view_all_deals',
    'manage_deals',
    'process_withdrawals',
    'view_all_earnings',
    'view_financial_data',
    'view_own_withdrawals',
    'create_withdrawal_request',
    'manage_system_settings',
    'view_audit_logs',
    'bulk_operations',
    'view_own_analytics',
    'view_system_analytics'
  ]
}