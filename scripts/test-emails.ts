#!/usr/bin/env bun

/**
 * Email testing script for IBC Partners Portal
 * 
 * Usage:
 *   bun run scripts/test-emails.ts --type approval --email <EMAIL>
 *   bun run scripts/test-emails.ts --type rejection --email <EMAIL>
 *   bun run scripts/test-emails.ts --type deal-update --email <EMAIL>
 */

import { email } from '../lib/email'

const args = process.argv.slice(2)
const getArg = (name: string): string | undefined => {
  const index = args.indexOf(`--${name}`)
  return index !== -1 ? args[index + 1] : undefined
}

const type = getArg('type')
const testEmail = getArg('email')

if (!type || !testEmail) {
  console.error('Usage: bun run scripts/test-emails.ts --type [approval|rejection|deal-update] --email <EMAIL>')
  process.exit(1)
}

async function testApprovalEmail() {
  console.log('🧪 Testing partner approval email...')
  
  const template = email.templates.partnerApplicationApproved({
    partnerName: 'John Test Partner',
    loginUrl: 'http://localhost:3000/sign-in',
    tier: 'trusted'
  })

  await email.send({
    to: testEmail!,
    subject: template.subject,
    html: template.html
  })

  console.log('✅ Partner approval email sent successfully')
}

async function testRejectionEmail() {
  console.log('🧪 Testing partner rejection email...')
  
  const template = email.templates.partnerApplicationRejected({
    partnerName: 'John Test Partner',
    reason: 'Insufficient business documentation provided'
  })

  await email.send({
    to: testEmail!,
    subject: template.subject,
    html: template.html
  })

  console.log('✅ Partner rejection email sent successfully')
}

async function testDealUpdateEmail() {
  console.log('🧪 Testing deal status update email...')
  
  const template = email.templates.dealStatusUpdate({
    partnerName: 'John Test Partner',
    clientCompany: 'TestCorp Inc.',
    dealValue: 50000,
    commissionAmount: 2500,
    status: 'confirmed',
    statusMessage: 'Deal has been confirmed and commission will be processed within 30 days.'
  })

  await email.send({
    to: testEmail!,
    subject: template.subject,
    html: template.html
  })

  console.log('✅ Deal update email sent successfully')
}

async function main() {
  try {
    console.log(`📧 Testing email delivery to: ${testEmail}`)
    console.log(`🎯 Email type: ${type}\n`)

    switch (type) {
      case 'approval':
        await testApprovalEmail()
        break
      case 'rejection':
        await testRejectionEmail()
        break
      case 'deal-update':
        await testDealUpdateEmail()
        break
      default:
        console.error('❌ Invalid email type. Use: approval, rejection, or deal-update')
        process.exit(1)
    }

    console.log('\n🎉 Email test completed successfully!')
    console.log('💡 Check your email inbox and spam folder')

  } catch (error) {
    console.error('❌ Email test failed:', error)
    process.exit(1)
  }
}

main()