/* eslint-disable no-console */
import 'dotenv/config'
import { createClient } from '@supabase/supabase-js'

type AppRow = {
  id: string
  email: string
  full_name: string | null
  company_name: string | null
  company_type: string | null
  role: string | null
  status: 'pending' | 'under_review' | 'approved' | 'rejected' | 'withdrawn'
  preferred_communication?: string[] | null
  preferred_payment_method?: string | null
  telegram?: string | null
  whatsapp?: string | null
  x_profile?: string | null
  created_at: string
  updated_at: string
}

type Profile = {
  id: string           // Clerk user ID
  email: string
  full_name: string | null
  company_name: string | null
  company_type: string | null
  role: string | null
  status: 'active' | 'pending' | 'suspended' | 'deleted'
  tier: 'trusted' | 'elite' | 'diamond' | null
  referral_slug: string | null
  telegram: string | null
  whatsapp: string | null
  x_profile: string | null
  created_at: string
  updated_at: string
}

type ReferralLink = {
  id: string
  partner_id: string
  slug: string
  active: boolean
  created_at: string
  updated_at: string
}

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('SYNC: ❌ Missing NEXT_PUBLIC_SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY in environment')
  process.exit(1)
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, { auth: { persistSession: false } })

const args = process.argv.slice(2)
const apply = args.includes('--apply')
const dryRun = args.includes('--dry-run') || !apply
const emailFilterIdx = args.indexOf('--email')
const emailFilter = emailFilterIdx >= 0 ? args[emailFilterIdx + 1] : undefined

function sleep(ms: number) {
  return new Promise((r) => setTimeout(r, ms))
}

function toSlug(input: string): string {
  return input.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '').slice(0, 20)
}

async function ensureUniqueSlug(base: string): Promise<string> {
  let slug = base
  let counter = 1
  for (let i = 0; i < 200; i++) {
    const { data, error } = await supabase
      .from('partners_referral_links')
      .select('id')
      .eq('slug', slug)
      .maybeSingle()

    if (error) {
      console.warn('SYNC: ⚠️ slug check error', { slug, error: error.message })
      return slug
    }
    if (!data) return slug
    slug = `${base}-${counter++}`
  }
  return `${base}-${Date.now()}`
}

function desiredProfileStatus(appStatus: AppRow['status']): Profile['status'] | null {
  if (appStatus === 'approved') return 'active'
  if (appStatus === 'pending' || appStatus === 'under_review') return 'pending'
  return null
}

async function upsertReferralForActiveProfile(profile: Profile) {
  if (profile.status !== 'active') return
  const { data: existing, error: linksErr } = await supabase
    .from('partners_referral_links')
    .select('*')
    .eq('partner_id', profile.id)
    .eq('active', true)

  if (linksErr) {
    console.error('SYNC: ❌ Failed to fetch referral links', { partnerId: profile.id, error: linksErr.message })
    return
  }

  if (existing && existing.length > 0) {
    console.log('SYNC: ✅ Active referral link already exists', {
      partnerId: profile.id,
      slug: existing[0].slug,
    })
    return
  }

  const base = profile.referral_slug?.trim()
    || (profile.company_name ? toSlug(profile.company_name) : null)
    || (profile.full_name ? toSlug(profile.full_name) : null)
    || toSlug(profile.email.split('@')[0])

  const unique = await ensureUniqueSlug(base)

  console.log('SYNC: 🧩 Creating referral link', { partnerId: profile.id, base, unique })

  if (dryRun) {
    console.log('SYNC: [DRY-RUN] Would create partners_referral_links', { partnerId: profile.id, slug: unique })
    return
  }

  const { error: createErr } = await supabase.from('partners_referral_links').insert({
    partner_id: profile.id,
    slug: unique,
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  })

  if (createErr) {
    console.error('SYNC: ❌ Failed to insert referral link', { partnerId: profile.id, error: createErr.message })
  } else {
    console.log('SYNC: ✅ Referral link created', { partnerId: profile.id, slug: unique })
  }
}

async function syncOne(application: AppRow) {
  const wanted = desiredProfileStatus(application.status)

  console.log('SYNC: ▶ Processing application', {
    applicationId: application.id,
    email: application.email,
    appStatus: application.status,
    desiredProfileStatus: wanted,
  })

  const { data: profile, error: profErr } = await supabase
    .from('partners_profiles')
    .select('*')
    .eq('email', application.email)
    .maybeSingle()

  if (profErr) {
    console.error('SYNC: ❌ profile lookup failed', { email: application.email, error: profErr.message })
    return
  }

  if (!profile) {
    console.warn('SYNC: ⚠️ No partners_profiles row for application email', {
      applicationId: application.id,
      email: application.email,
      note: 'Cannot create profile safely (id must equal Clerk user id). Ask user to complete onboarding or map email→userId.',
    })
    return
  }

  if (!wanted) {
    console.log('SYNC: ℹ No profile status change for this application status', {
      applicationId: application.id,
      appStatus: application.status,
      profileId: profile.id,
      currentProfileStatus: profile.status,
    })
    if (profile.status === 'active') {
      await upsertReferralForActiveProfile(profile)
    }
    return
  }

  if (profile.status === wanted) {
    console.log('SYNC: ✅ Profile already in desired status', {
      profileId: profile.id,
      email: profile.email,
      status: profile.status,
    })
    if (profile.status === 'active') {
      await upsertReferralForActiveProfile(profile)
    }
    return
  }

  console.log('SYNC: 🔧 Updating profile status', {
    profileId: profile.id,
    email: profile.email,
    from: profile.status,
    to: wanted,
  })

  if (dryRun) {
    console.log('SYNC: [DRY-RUN] Would update partners_profiles.status', {
      profileId: profile.id,
      to: wanted,
    })
  } else {
    const { error: updErr } = await supabase
      .from('partners_profiles')
      .update({ status: wanted, updated_at: new Date().toISOString() })
      .eq('id', profile.id)

    if (updErr) {
      console.error('SYNC: ❌ Failed to update profile status', {
        profileId: profile.id,
        to: wanted,
        error: updErr.message,
      })
      return
    }
    console.log('SYNC: ✅ Profile status updated', {
      profileId: profile.id,
      to: wanted,
    })
    if (wanted === 'active') {
      await upsertReferralForActiveProfile({ ...profile, status: 'active' })
    }
  }
}

async function main() {
  console.log('SYNC: ---- START ----', {
    dryRun,
    apply,
    emailFilter: emailFilter || null,
    timestamp: new Date().toISOString(),
  })

  const { data: apps, error } = await supabase
    .from('partners_applications')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('SYNC: ❌ Failed to fetch applications', { error: error.message })
    process.exit(1)
  }

  const latestByEmail = new Map<string, AppRow>()
  for (const app of apps || []) {
    if (emailFilter && app.email !== emailFilter) continue
    if (!latestByEmail.has(app.email)) latestByEmail.set(app.email, app)
  }

  console.log('SYNC: Found applications', {
    total: apps?.length || 0,
    uniqueEmails: latestByEmail.size,
    filtered: !!emailFilter,
  })

  let processed = 0
  for (const [email, application] of Array.from(latestByEmail.entries())) {
    try {
      await syncOne(application)
      processed++
      await sleep(50)
    } catch (e: any) {
      console.error('SYNC: ❌ Exception processing application', {
        applicationId: application.id,
        email,
        error: e?.message || String(e),
      })
    }
  }

  console.log('SYNC: ---- DONE ----', {
    processed,
    dryRun,
    apply,
    timestamp: new Date().toISOString(),
  })
}

main().catch((e) => {
  console.error('SYNC: ❌ Fatal error', { error: e?.message || String(e) })
  process.exit(1)
})

