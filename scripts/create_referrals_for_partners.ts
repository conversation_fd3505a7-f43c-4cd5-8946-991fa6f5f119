/* eslint-disable no-console */
import 'dotenv/config'
import { createClient } from '@supabase/supabase-js'

// Simple script: create active referral links for partners without one
// Usage:
//   bunx tsx scripts/create_referrals_for_partners.ts --dry-run
//   bunx tsx scripts/create_referrals_for_partners.ts --apply
//   bunx tsx scripts/create_referrals_for_partners.ts --apply --status active
//   bunx tsx scripts/create_referrals_for_partners.ts --apply --email <EMAIL>

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('REF-SYNC: ❌ Missing NEXT_PUBLIC_SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, { auth: { persistSession: false } })

const args = process.argv.slice(2)
const apply = args.includes('--apply')
const dryRun = args.includes('--dry-run') || !apply
const statusIdx = args.indexOf('--status')
const statusFilter = statusIdx >= 0 ? args[statusIdx + 1] : undefined as undefined | 'active' | 'pending'
const emailIdx = args.indexOf('--email')
const emailFilter = emailIdx >= 0 ? args[emailIdx + 1] : undefined

function toSlug(input: string): string {
  return input.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '').slice(0, 20)
}

async function ensureUniqueSlug(base: string): Promise<string> {
  let slug = base
  let counter = 1
  while (true) {
    const { data } = await supabase.from('partners_referral_links').select('id').eq('slug', slug).maybeSingle()
    if (!data) return slug
    slug = `${base}-${counter++}`
  }
}

async function main() {
  console.log('REF-SYNC: ---- START ----', { dryRun, apply, statusFilter: statusFilter || null, emailFilter: emailFilter || null })

  // Build base query for partners
  let query = supabase.from('partners_profiles').select('id, email, full_name, company_name, referral_slug, status')
  if (statusFilter) query = query.eq('status', statusFilter)
  if (emailFilter) query = query.eq('email', emailFilter)

  const { data: partners, error } = await query
  if (error) {
    console.error('REF-SYNC: ❌ Failed to fetch partners', { error: error.message })
    process.exit(1)
  }

  console.log('REF-SYNC: Fetched partners', { count: partners?.length || 0 })

  let created = 0
  let skippedHasActive = 0
  let skippedNoStatus = 0

  for (const p of partners || []) {
    console.log('REF-SYNC: Checking partner', { id: p.id, email: p.email, status: p.status })

    // Only create for active by default; pending partners can be included via --status pending
    if (!p.status) {
      console.warn('REF-SYNC: ⚠️ Partner has no status, skipping', { id: p.id, email: p.email })
      skippedNoStatus++
      continue
    }

    // Does an active link already exist?
    const { data: existingActive, error: existErr } = await supabase
      .from('partners_referral_links')
      .select('id, slug, active')
      .eq('partner_id', p.id)
      .eq('active', true)
      .maybeSingle()

    if (existErr) {
      console.error('REF-SYNC: ❌ Failed to check existing links', { id: p.id, email: p.email, error: existErr.message })
      continue
    }

    if (existingActive) {
      console.log('REF-SYNC: ✅ Active link exists, skipping', { id: p.id, slug: existingActive.slug })
      skippedHasActive++
      continue
    }

    // Determine base slug
    const baseRaw = p.referral_slug || p.company_name || p.full_name || (p.email ? p.email.split('@')[0] : 'partner')
    const base = toSlug(String(baseRaw) || 'partner') || 'partner'

    const unique = await ensureUniqueSlug(base)
    console.log('REF-SYNC: 🧩 Creating referral link', { partnerId: p.id, base, slug: unique })

    if (dryRun) {
      console.log('REF-SYNC: [DRY-RUN] Would create partners_referral_links', { partnerId: p.id, slug: unique, active: true })
      continue
    }

    const { error: createErr } = await supabase.from('partners_referral_links').insert({
      partner_id: p.id,
      slug: unique,
      active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })

    if (createErr) {
      console.error('REF-SYNC: ❌ Failed to create referral link', { partnerId: p.id, email: p.email, error: createErr.message })
      continue
    }

    console.log('REF-SYNC: ✅ Referral link created', { partnerId: p.id, slug: unique })
    created++
  }

  console.log('REF-SYNC: ---- DONE ----', { created, skippedHasActive, skippedNoStatus })
}

main().catch((e) => {
  console.error('REF-SYNC: ❌ Fatal error', { error: e?.message || String(e) })
  process.exit(1)
})

