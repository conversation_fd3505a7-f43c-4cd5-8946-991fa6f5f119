import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { log } from '@/lib/log'
import { createClient } from '@/lib/supabase/server'

// Define route matchers using Clerk best practices
const isPublicRoute = createRouteMatcher([
  '/sign-in(.*)',
  '/sign-up(.*)',
  '/access-pending',
  '/apply', // Public marketing page for partnership applications
  '/referral/(.*)',
  '/api/webhooks/(.*)',
  '/api/debug/(.*)'
])

const isAdminRoute = createRouteMatcher(['/admin(.*)'])
const isPublicApiRoute = createRouteMatcher(['/api/leads', '/api/webhooks/(.*)'])

export default clerkMiddleware(async (auth, req: NextRequest) => {
  const { pathname } = req.nextUrl
  
  // Handle referral attribution cookies for /referral/{slug} routes
  if (pathname.startsWith('/referral/')) {
    const response = NextResponse.next()
    const slug = pathname.split('/referral/')[1]

    if (slug) {
      const ip = req.headers.get('x-forwarded-for') || req.headers.get('cf-connecting-ip') || 'unknown'
      log.info('Setting referral attribution cookie', { 
        metadata: { slug: slug, ip: ip } 
      })
      
      // Extract UTM parameters
      const utm = {
        source: req.nextUrl.searchParams.get('utm_source'),
        medium: req.nextUrl.searchParams.get('utm_medium'),
        campaign: req.nextUrl.searchParams.get('utm_campaign'),
        term: req.nextUrl.searchParams.get('utm_term'),
        content: req.nextUrl.searchParams.get('utm_content'),
      }

      // Fetch partner name for the slug
      let partnerName = null
      try {
        const client = await createClient(true) // Use service role
        const { data: referralLink } = await client
          .from('partners_referral_links')
          .select(`
            slug,
            active,
            partners_profiles (
              full_name
            )
          `)
          .eq('slug', slug)
          .eq('active', true)
          .single()

        if (referralLink) {
          const partner = Array.isArray(referralLink.partners_profiles) 
            ? referralLink.partners_profiles[0] 
            : referralLink.partners_profiles
          partnerName = partner?.full_name || null
        }
      } catch (error) {
        log.error('Failed to fetch partner name for cookie', { 
          error,
          metadata: { slug }
        })
      }

      // Set attribution cookie with 30-day expiry
      const attributionData = {
        slug,
        partnerName,
        utm: Object.fromEntries(Object.entries(utm).filter(([_, v]) => v !== null)),
        timestamp: Date.now(),
        ip: ip
      }

      response.cookies.set('ibc_partner_ref', JSON.stringify(attributionData), {
        maxAge: 30 * 24 * 60 * 60, // 30 days
        httpOnly: false,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production'
      })

      log.debug('Referral cookie set', { 
        metadata: { slug: attributionData.slug, timestamp: attributionData.timestamp } 
      })
    }

    return response
  }

  // Skip auth check for public API routes
  if (isPublicApiRoute(req)) {
    return NextResponse.next()
  }

  // For public routes, allow access
  if (isPublicRoute(req)) {
    return NextResponse.next()
  }

  // All other routes are protected - check authentication first
  const authResult = await auth()
  const { userId } = authResult
  
  console.log('MIDDLEWARE: Processing protected route', { pathname, userId: userId ? 'present' : 'missing' })

  if (!userId) {
    const ip = req.headers.get('x-forwarded-for') || req.headers.get('cf-connecting-ip') || 'unknown'
    console.log('MIDDLEWARE: No userId', { pathname, ip })
    log.authEvent('unauthenticated_access_attempt', 'anonymous', { 
      metadata: {
        path: pathname,
        ip: ip
      }
    })

    // For API routes, return JSON error instead of redirect
    if (pathname.startsWith('/api/')) {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { status: 401 }
      )
    }

    // For non-API routes, redirect to sign-in
    return NextResponse.redirect(new URL(`/sign-in?redirect_url=${encodeURIComponent(req.url)}`, req.url))
  }

  // User is authenticated, now check profile status
  try {
    console.log('MIDDLEWARE: Checking profile for user', userId, 'accessing', pathname)
    const client = await createClient(true) // Use service role to bypass RLS
    
    // First, get the user's email from Clerk session (reuse authResult from above)
    const userEmail = authResult.sessionClaims?.email as string
    
    console.log('MIDDLEWARE: User email from session:', userEmail)
    
    // Check if user is an internal admin user (in partners_users table)
    if (userEmail) {
      const { data: adminUser, error: adminError } = await client
        .from('partners_users')
        .select('role, display_name, email')
        .eq('email', userEmail)
        .maybeSingle()
      
      console.log('MIDDLEWARE: Admin user check result', { 
        adminUser: adminUser, 
        error: adminError?.message 
      })
      
      if (adminUser) {
        // User is an internal admin - grant access
        console.log('MIDDLEWARE: Internal admin user access granted', { 
          userId, 
          email: adminUser.email,
          role: adminUser.role,
          name: adminUser.display_name,
          path: pathname 
        })
        
        log.authEvent('admin_user_access', userId, { 
          metadata: {
            path: pathname,
            role: adminUser.role,
            email: adminUser.email
          }
        })
        
        return NextResponse.next()
      }
    }
    
    // Not an admin user, check regular partner profile
    // Add timeout to prevent hanging
    const profilePromise = client
      .from('partners_profiles')
      .select('status, tier, full_name')
      .eq('id', userId)
      .maybeSingle() // Use maybeSingle() instead of single() to handle no rows gracefully
    
    const timeoutPromise = new Promise((_, reject) => 
      setTimeout(() => reject(new Error('Database query timeout')), 5000)
    )
    
    const { data: profile, error: profileError } = await Promise.race([
      profilePromise,
      timeoutPromise
    ]) as any
    
    console.log('MIDDLEWARE: Profile query result', { 
      profileExists: !!profile, 
      profileStatus: profile?.status, 
      error: profileError?.message 
    })

    // Enhanced error logging
    if (profileError && profileError.code !== 'PGRST116') { // PGRST116 means no rows found
      console.error('MIDDLEWARE: Database error', {
        userId,
        error: profileError.message,
        code: profileError.code,
        path: pathname
      })
      log.error('Profile lookup failed in middleware', {
        userId,
        error: profileError.message,
        metadata: {
          path: pathname,
          errorCode: profileError.code
        }
      })
      
      // On database error, allow debug endpoints but redirect to onboarding for new users
      if (pathname.startsWith('/api/debug/')) {
        return NextResponse.next()
      }
      // If it's a database error (not just "no rows"), redirect to onboarding
      return NextResponse.redirect(new URL('/onboarding', req.url))
    }

    // Profile not found - user needs to be created
    if (!profile) {
      console.log('MIDDLEWARE: No profile found', { userId, path: pathname })
      log.authEvent('no_profile_found', userId, { 
        metadata: { path: pathname } 
      })
      
      // Allow access to onboarding page and API routes for users without profiles
      if (pathname === '/onboarding' || pathname.startsWith('/api/')) {
        console.log('MIDDLEWARE: ✅ Allowing access for new user', { userId, pathname })
        return NextResponse.next()
      }
      
      // Redirect users without profiles to onboarding
      console.log('MIDDLEWARE: No profile found, redirecting to onboarding', { userId, pathname })
      return NextResponse.redirect(new URL('/onboarding', req.url))
    }

    // Check profile status - but allow API routes to return JSON responses
    if (profile.status === 'pending') {
      console.log('MIDDLEWARE: Profile pending approval', { userId, path: pathname })
      log.authEvent('unapproved_access_attempt', userId, { 
        metadata: {
          path: pathname,
          profileStatus: 'pending'
        }
      })
      
      // Allow onboarding API to complete successfully even with pending status
      if (pathname === '/api/onboarding') {
        console.log('MIDDLEWARE: ✅ Allowing onboarding API to complete', { userId, pathname })
        return NextResponse.next()
      }
      
      // For other API routes, return JSON instead of redirecting
      if (pathname.startsWith('/api/')) {
        return NextResponse.json(
          { error: 'Account pending approval', status: 'pending' }, 
          { status: 403 }
        )
      }
      
      return NextResponse.redirect(new URL('/access-pending', req.url))
    }

    if (profile.status === 'suspended' || profile.status === 'deleted') {
      console.log('MIDDLEWARE: Profile suspended/deleted', { userId, status: profile.status, path: pathname })
      log.authEvent('suspended_access_attempt', userId, { 
        metadata: {
          path: pathname,
          profileStatus: profile.status
        }
      })
      
      // For API routes, return JSON instead of redirecting
      if (pathname.startsWith('/api/')) {
        return NextResponse.json(
          { error: 'Account suspended', status: profile.status }, 
          { status: 403 }
        )
      }
      
      return NextResponse.redirect(new URL('/access-pending', req.url))
    }

    if (profile.status !== 'active') {
      console.log('MIDDLEWARE: Profile not active', { userId, status: profile.status, path: pathname })
      log.authEvent('inactive_profile_access', userId, { 
        metadata: {
          path: pathname,
          profileStatus: profile.status
        }
      })
      
      // For API routes, return JSON instead of redirecting
      if (pathname.startsWith('/api/')) {
        return NextResponse.json(
          { error: 'Account not active', status: profile.status }, 
          { status: 403 }
        )
      }
      
      return NextResponse.redirect(new URL('/access-pending', req.url))
    }

    // Profile is active - allow access
    console.log('MIDDLEWARE: Access granted', { 
      userId, 
      profileStatus: profile.status, 
      userName: profile.full_name,
      path: pathname 
    })
    
    log.authEvent('authenticated_access', userId, { 
      metadata: {
        path: pathname,
        profileStatus: profile.status,
        tier: profile.tier
      }
    })

    // Admin route additional checks
    if (isAdminRoute(req)) {
      // TODO: Add role-based access control
      log.authEvent('admin_route_access', userId, { 
        metadata: { path: pathname } 
      })
    }

    return NextResponse.next()

  } catch (error) {
    // Enhanced error logging for debugging
    const errorMessage = error instanceof Error ? error.message : String(error)
    const isTimeoutError = errorMessage.includes('timeout')
    
    console.error('MIDDLEWARE: Critical error', {
      userId,
      path: pathname,
      error: errorMessage,
      isTimeout: isTimeoutError,
      timestamp: new Date().toISOString()
    })
    
    log.error('Critical middleware error', { 
      userId, 
      error: errorMessage,
      metadata: {
        path: pathname,
        isTimeout: isTimeoutError,
        timestamp: new Date().toISOString()
      }
    })
    
    // Allow debug endpoints even on critical errors
    if (pathname.startsWith('/api/debug/')) {
      return NextResponse.next()
    }
    
    // For all other routes, fail safely to access-pending
    return NextResponse.redirect(new URL('/access-pending', req.url))
  }
})

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}