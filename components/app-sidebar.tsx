"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState, useEffect } from "react"
import {
  type LucideIcon,
  GalleryVerticalEnd,
  Home,
  Package,
  Wallet,
  DollarSign,
  Link2,
  FileText,
  Settings,
  ShieldCheck,
  Users,
  BarChartIcon as ChartBar,
  Wrench,
  ChevronDown,
} from "lucide-react"
import { roleLabels, tierLabels, type UserRole, type PartnerTier } from "@/lib/rbac-constants"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupAction,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarMenu,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
  SidebarSeparator,
  useSidebar,
} from "@/components/ui/sidebar"

type NavItem = {
  title: string
  url: string
  icon: LucideIcon
  badge?: string
}

export function AppSidebar() {
  const pathname = usePathname()
  const { state } = useSidebar()
  const [userRole, setUserRole] = useState<string | null>(null)
  const [userTier, setUserTier] = useState<string>('trusted')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Fetch user role and tier for permissions
    const fetchUserInfo = async () => {
      try {
        console.log('Sidebar: Starting role detection...')
        
        // Try profile API first (for partners)
        const profileResponse = await fetch('/api/profile')
        console.log('Sidebar: Profile API response status:', profileResponse.status)
        
        if (profileResponse.ok) {
          const profileData = await profileResponse.json()
          console.log('Sidebar: Profile data:', profileData)
          
          if (profileData.role) {
            console.log('Sidebar: Setting role from profile:', profileData.role)
            setUserRole(profileData.role)
            setUserTier(profileData.tier || 'trusted')
            setIsLoading(false)
            return
          }
        }

        // Try admin role check API (for admin users)
        console.log('Sidebar: Trying admin role check API...')
        const roleResponse = await fetch('/api/admin/check-role')
        console.log('Sidebar: Admin role API response status:', roleResponse.status)
        
        if (roleResponse.ok) {
          const roleData = await roleResponse.json()
          console.log('Sidebar: Admin role check result:', roleData)
          
          if (roleData.role) {
            console.log('Sidebar: Setting role from admin API:', roleData.role)
            setUserRole(roleData.role)
            setUserTier('admin') // Admin users don't have tiers
          } else {
            console.log('Sidebar: No role found in admin API response')
          }
        } else {
          const errorData = await roleResponse.text()
          console.log('Sidebar: Admin role API error response:', { 
            status: roleResponse.status, 
            data: errorData 
          })
        }
      } catch (error) {
        console.error('Sidebar: Failed to fetch user info:', error)
      } finally {
        console.log('Sidebar: Final role state:', { userRole, isLoading: false })
        setIsLoading(false)
      }
    }
    fetchUserInfo()
  }, [])

  const main: NavItem[] = [
    { title: "Home", url: "/", icon: Home },
    { title: "Deals", url: "/deals", icon: Package },
    { title: "Earnings", url: "/earnings", icon: Wallet },
    { title: "Withdrawals", url: "/withdrawals", icon: DollarSign },
    { title: "Referrals", url: "/referrals", icon: Link2 },
    { title: "Resources", url: "/resources", icon: FileText },
  ]

  const adminChildren = [
    { title: "Dashboard", url: "/admin" },
    { title: "Partners", url: "/admin/partners" },
    { title: "Application Review", url: "/admin/leads" },
    { title: "Deals", url: "/admin/deals" },
    { title: "Withdrawals", url: "/admin/withdrawals" },
    { title: "Analytics", url: "/admin/analytics" },
    { title: "Tools • Sales Assignment", url: "/admin/tools/sales-assignment", opsOnly: true },
    { title: "Tools • Reassign", url: "/admin/tools/reassign" },
    { title: "User Management", url: "/admin/users", superAdminOnly: true },
  ]

  return (
    <Sidebar collapsible="icon" variant="sidebar">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                    <GalleryVerticalEnd className="size-4" />
                  </div>
                  <div className="flex min-w-0 flex-col text-left leading-tight">
                    <span className="truncate font-semibold">Partner Portal</span>
                    <span className="truncate text-[0.8rem] text-sidebar-foreground/70">Production</span>
                  </div>
                  <ChevronDown className="ml-auto" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[--radix-popper-anchor-width]" align="start">
                <DropdownMenuItem>Production</DropdownMenuItem>
                <DropdownMenuItem>Staging</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
        {state === 'expanded' && (
          <form action="#" className="pt-1">
            <SidebarInput placeholder="Search…" aria-label="Search navigation" />
          </form>
        )}
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Overview</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {main.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild isActive={pathname === item.url} tooltip={item.title}>
                    <Link href={item.url}>
                      <item.icon />
                      <span className="truncate">{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                  {item.badge ? <SidebarMenuBadge>{item.badge}</SidebarMenuBadge> : null}
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Only show Admin section for authorized roles */}
        {!isLoading && userRole && ['ops', 'accounting', 'super_admin', 'sales'].includes(userRole) && (
          <>
            <SidebarSeparator />
            <SidebarGroup>
              <SidebarGroupLabel>Admin</SidebarGroupLabel>
              <SidebarGroupAction asChild title="Admin center">
                <Link href="/admin">
                  <Wrench />
                  <span className="sr-only">Open admin</span>
                </Link>
              </SidebarGroupAction>
              <SidebarGroupContent>
                <SidebarMenu>
                  <SidebarMenuItem>
                    <SidebarMenuButton>
                      <ShieldCheck />
                      <span>Admin</span>
                    </SidebarMenuButton>
                    <SidebarMenuSub>
                      {adminChildren.map((child) => {
                        // Filter admin menu items based on role
                        if (userRole === 'sales' && ['Analytics', 'Tools • Reassign', 'Tools • Sales Assignment'].some(item => child.title.includes(item))) {
                          return null // Sales can't see analytics or tools
                        }
                        // Only show User Management to super admins
                        if ((child as any).superAdminOnly && userRole !== 'super_admin') {
                          return null
                        }
                        // Only show ops-only items to ops and super admins
                        if ((child as any).opsOnly && !['ops', 'super_admin'].includes(userRole)) {
                          return null
                        }
                        return (
                          <SidebarMenuSubItem key={child.url}>
                            <SidebarMenuSubButton asChild isActive={pathname === child.url}>
                              <Link href={child.url}>
                                <Users />
                                <span className="truncate">{child.title}</span>
                              </Link>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        )
                      })}
                    </SidebarMenuSub>
                  </SidebarMenuItem>
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </>
        )}

      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild tooltip="Settings" isActive={pathname === "/account/settings"}>
              <Link href="/account/settings">
                <Settings />
                <span>Settings</span>
                <Badge className="ml-auto" variant="outline">
                  {userRole && ['ops', 'super_admin', 'accounting', 'sales'].includes(userRole) 
                    ? roleLabels[userRole as UserRole]
                    : userTier && userTier !== 'admin' ? tierLabels[userTier as PartnerTier] : 'Partner'
                  }
                </Badge>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  )
}
