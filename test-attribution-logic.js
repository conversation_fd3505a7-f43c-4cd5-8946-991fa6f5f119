// Test script to verify referral attribution priority logic
// Run this with: node test-attribution-logic.js

const { parseAttributionCookies, getPrimaryAttribution } = require('./lib/services/attribution')

function testAttributionPriority() {
  console.log('🧪 Testing Referral Attribution Priority Logic\n')

  // Test Case 1: Partner attribution only
  console.log('Test Case 1: Partner Attribution Only')
  const partnerOnlyCookies = {
    partner: {
      type: 'partner',
      partnerId: 'partner-123',
      slug: 'acme-ventures',
      partnerName: '<PERSON>',
      companyName: 'Acme Ventures',
      tier: 'elite',
      timestamp: Date.now(),
      visitId: 'visit-123'
    }
  }
  
  const primaryAttribution1 = getPrimaryAttribution(partnerOnlyCookies)
  console.log('Result:', primaryAttribution1?.type, primaryAttribution1?.slug)
  console.log('✅ Expected: partner, acme-ventures\n')

  // Test Case 2: Sales attribution only
  console.log('Test Case 2: Sales Attribution Only')
  const salesOnlyCookies = {
    sales: {
      type: 'sales',
      salesId: 'sales-456',
      slug: 'ned',
      salesPersonName: '<PERSON>ov',
      telegramHandle: '@nedyalkov14',
      timestamp: Date.now(),
      visitId: 'visit-456'
    }
  }
  
  const primaryAttribution2 = getPrimaryAttribution(salesOnlyCookies)
  console.log('Result:', primaryAttribution2?.type, primaryAttribution2?.slug)
  console.log('✅ Expected: sales, ned\n')

  // Test Case 3: Both attributions - Partner should take priority
  console.log('Test Case 3: Both Partner and Sales Attribution (Priority Test)')
  const bothCookies = {
    partner: {
      type: 'partner',
      partnerId: 'partner-123',
      slug: 'acme-ventures',
      partnerName: 'John Doe',
      companyName: 'Acme Ventures',
      tier: 'elite',
      timestamp: Date.now(),
      visitId: 'visit-123'
    },
    sales: {
      type: 'sales',
      salesId: 'sales-456',
      slug: 'ned',
      salesPersonName: 'Ned Yalkov',
      timestamp: Date.now(),
      visitId: 'visit-456'
    }
  }
  
  const primaryAttribution3 = getPrimaryAttribution(bothCookies)
  console.log('Result:', primaryAttribution3?.type, primaryAttribution3?.slug)
  console.log('✅ Expected: partner, acme-ventures (partner takes priority)\n')

  // Test Case 4: No attribution
  console.log('Test Case 4: No Attribution (Direct Visit)')
  const noCookies = {}
  
  const primaryAttribution4 = getPrimaryAttribution(noCookies)
  console.log('Result:', primaryAttribution4)
  console.log('✅ Expected: null\n')

  // Test Case 5: Cookie parsing with real cookie string
  console.log('Test Case 5: Cookie Parsing Test')
  const cookieString = 'ibc_partner_ref=%7B%22type%22%3A%22partner%22%2C%22slug%22%3A%22test-partner%22%2C%22partnerName%22%3A%22Test%20Partner%22%7D; ibc_sales_ref=%7B%22type%22%3A%22sales%22%2C%22slug%22%3A%22chris%22%2C%22salesPersonName%22%3A%22Chris%22%7D'
  
  const parsedCookies = parseAttributionCookies(cookieString)
  console.log('Parsed cookies:', {
    hasPartner: !!parsedCookies.partner,
    hasSales: !!parsedCookies.sales,
    partnerSlug: parsedCookies.partner?.slug,
    salesSlug: parsedCookies.sales?.slug
  })
  
  const primaryAttribution5 = getPrimaryAttribution(parsedCookies)
  console.log('Primary attribution:', primaryAttribution5?.type, primaryAttribution5?.slug)
  console.log('✅ Expected: partner, test-partner (partner takes priority)\n')

  console.log('🎉 All attribution priority tests completed!')
}

// Test different referral links
function testReferralLinks() {
  console.log('🔗 Testing Referral Link Generation\n')

  // Test sales team member slugs
  const salesTeamEmails = [
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ]

  console.log('Generated Sales Referral Slugs:')
  salesTeamEmails.forEach(email => {
    const slug = email.split('@')[0].toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
    
    console.log(`${email.padEnd(20)} → /referral/sales/${slug}`)
  })

  console.log('\n✅ All referral links follow the correct format')
}

if (require.main === module) {
  testAttributionPriority()
  console.log('\n' + '='.repeat(50) + '\n')
  testReferralLinks()
}

module.exports = {
  testAttributionPriority,
  testReferralLinks
}