{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "bun run next build", "dev": "bun run next dev", "lint": "bun run next lint", "start": "bun run next start", "type-check": "bun run tsc --noEmit", "db:generate-types": "bunx supabase gen types typescript --project-id=$SUPABASE_PROJECT_ID --schema=public > lib/supabase/types.ts", "db:migrate": "bunx supabase db push", "db:reset": "bunx supabase db reset", "db:seed": "bun run scripts/seed.ts", "test": "bun run vitest", "test:e2e": "bunx playwright test", "test:e2e:ui": "bunx playwright test --ui"}, "dependencies": {"@clerk/nextjs": "^6.30.0", "@clerk/themes": "^2.4.6", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-icons": "latest", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@sentry/nextjs": "^10.3.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.54.0", "@tanstack/react-form": "^1.19.0", "@tanstack/react-query": "^5.84.2", "@tanstack/react-table": "latest", "@types/qrcode": "^1.5.5", "@uploadthing/react": "^7.3.2", "@upstash/ratelimit": "^2.0.6", "@upstash/redis": "^1.35.3", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^4.1.0", "embla-carousel-react": "8.5.1", "geist": "^1.3.1", "input-otp": "1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.4", "pdf-lib": "^1.17.1", "qrcode": "^1.5.4", "react": "^19", "react-day-picker": "9.8.0", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "resend": "^6.0.1", "sonner": "^1.7.1", "svix": "^1.71.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.7.3", "vaul": "^0.9.6", "zod": "^4.0.16"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.33.0", "eslint-config-next": "^15.4.6", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}